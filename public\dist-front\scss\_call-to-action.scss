/*** 

====================================================================
		Call To Action
====================================================================

***/

.call-to-action{
    position: relative;
    padding: 120px 0;
    z-index: 2;
    .bg-image{
        &:before{
            @include overlay;
            background-color: var(--theme-color2);
            opacity: .7;
            content: "";
        }
    }
    .outer-box{
        position: relative;
        max-width: 920px;
        margin: 0 auto;
        @include media-breakpoint-down(lg){
            text-align: center;
        }
        .small-image{
            position: absolute;
            right: 0px;
            top:100px;
            border-radius: 100px;
            overflow: hidden;
            margin-bottom: 0;
            @include media-breakpoint-down(lg){
                display: none;
            }
        }
        .title{
            font-size: 60px;
            color: var(--theme-color-light);
            margin-bottom: 35px;
            @include media-breakpoint-down(lg){
                br{display: none;}
            }
            @include media-breakpoint-down(md){
                font-size: 42px;
                line-height: 1.2em;
            }
            .selected{
                position: relative;
                &:before{
                    position: absolute;
                    left: -20px;
                    top: 12px;
                    height: 71px;
                    width: 275px;
                    background-image: url(../images/icons/arrow-circle.png);
                    z-index: -1;
                    content: "";
                }
            }
        }       
    }
}


/*** 

====================================================================
		Call To Action Two
====================================================================

***/

.call-to-action-two{
    position: relative;
    z-index: 2;
    .outer-box{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #0e0d16;
        padding: 70px 100px;
        @include media-breakpoint-down(xl){
            padding: 50px 50px;
        }
        @include media-breakpoint-down(lg){
            flex-direction: column;
            text-align: center;
        }
    }

    .title{
        font-size: 30px;
        line-height: 40px;
        color: #fff;
        letter-spacing: 0;
        margin-bottom: 0;
    }

    .info-box{
        position: relative;
        display: flex;
        flex-direction: column;
        text-align: right;
        @include media-breakpoint-down(lg){
            text-align: center;
        }
        a{
            display: inline-block;
            color: #fff;
        }
        .num{
            font-size: 30px;
            color: #fff;
            margin-bottom: 10px;
            &:hover{
                text-decoration: underline;
            }
        }
        .mail{
            color: var(--theme-color1);
            font-size: 20px;
            font-weight: 700;
            &:hover{
                text-decoration: underline;
            }
        }
    }
    

    .icon-box{
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        @include media-breakpoint-down(lg){
            position: relative;
            left: 0;
            top: 0;
            display: block;
            transform: none;
            margin: 50px 0;
        }
        &::before{
            position: absolute;
            left: 100%;
            top: 50%;
            width: 60px;
            margin-top: -1px;
            border-bottom: 1px solid rgba(255,255,255,.20);
            margin-left: 20px;
            content: "";
            @include media-breakpoint-down(xl){
                display: none;
            }
        }
        &::after {
            position: absolute;
            right: 100%;
            top: 50%;
            width: 60px;
            margin-top: -1px;
            border-bottom: 1px solid rgba(255, 255, 255, .20);
            margin-right: 20px;
            content: "";
            @include media-breakpoint-down(xl) {
                display: none;
            }
        }
        .icon{
            @include flex-center;
            height: 100px;
            width: 100px;
            background-color: var(--bg-theme-color1);
            font-size: 42px;
            color: var(--theme-color2);
            border-radius: 50%;
            transition: all 300ms ease;
            &:hover{
                background-color: var(--theme-color1);
            }
        }
    }
    
}
