
/***

==================================================================
	Main Footer
==================================================================

***/

.main-footer{
	position: relative;
	background-color: var(--theme-color2);
	.footer-upper{
		position: relative;
		padding: 30px 0 7px;
		background-color: #151518;
	}

	/* Widget Section */
	.widgets-section{
		position: relative;
		padding:70px 0 50px;
	}
	.footer-column {
		position: relative;
		margin-bottom: 50px;
	}
	.footer-widget {
		position: relative;
	}

	.widget-title {
		position: relative;
		font-weight: 600;
		color: #ffffff;
		line-height: 1.2em;
		margin-bottom: 20px;
	}
}

.contact-info-block{
	position: relative;
	margin-bottom: 30px;
	&:last-child{
		.inner:before{display: none;}
	}
	.inner{
		position: relative;
		padding-left: 40px;
		&:before{
			position: absolute;
			right: 20px;
			top: 0;
			height: 100%;
			width: 1px;
			background-color: #fff;
			opacity: .1;
			content: "";
		}
		@include media-breakpoint-down(sm){
			padding-left: 0;
			text-align: center;
		}
		.icon{
			position: absolute;
			left: 0;
			top: 12px;
			font-size: 20px;
			color: var(--theme-color1);
			@include media-breakpoint-down(sm){
				position: relative;
				display: inline-block;
				margin-bottom: 20px;
			}
		}
		.sub-title{
			font-size: 12px;
			color: #7f7f7f;
			font-weight: 400;
			line-height: 20px;
			display: block;
		}
		.text{
			font-size: 18px;
			color: #fff;
			line-height: 24px;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			@include media-breakpoint-down(sm){
				align-items: center;
			}
			a{
				color: #fff;
				&:hover{color: var(--theme-color1);}
			}
		}
	}
}


.main-footer .about-widget{
	position: relative;
	.logo{
		margin-bottom: 15px;
	}
	.text{
		margin-bottom: 30px;
	}
}

/*=== User LInks ===*/
.user-links {
	position: relative;
	li {
		position: relative;
		font-size: 15px;
		line-height: 26px;
		color: #75767a;
		font-weight: 400;
		margin-bottom: 10px;
		&:last-child {
			margin-bottom: 0;
		}
		a {
			position: relative;
			display: inline-block;
			color: inherit;
			transition: all 300ms ease;
			&:hover {
				color: #FFFFFF;
			}
			&:before {
				position: absolute;
				left: 0;
				bottom: 0;
				width: 0;
				height: 1px;
				background-color: var(--bg-theme-color1);
				content: "";
				transition: all 300ms ease;
			}
			&:hover:before {
				width: 100%;
			}
		}
	}
	&.two-column{
		display: flex;
		flex-wrap: wrap;
		li{
			width: 50%;
			flex: 0 0 50%;
		}
	}
}


.newsletter-widget{
	position: relative;
	.text{
		margin-bottom: 10px;
	}
}

/* Subscribe Form */
.subscribe-form {
	position: relative;
	.form-group {
		position: relative;
		input[type=text],
		input[type=email] {
			position: relative;
			display: block;
			height: 60px;
			width: 100%;
			font-size: 14px;
			line-height: 30px;
			color: #717070;
			padding: 20px 30px;
			background: #fff;
			text-align: center;
			transition: all 300ms ease;
			&::placeholder {
				color: #717070;
			}
		}
		.theme-btn {
			position: relative;
			margin-top: 10px;
			width: 100%;
		}
	}
}


/*=== Footer Bottom ===*/
.footer-bottom {
	position: relative;
	z-index: 3;
	text-align: center;
	.copyright-text {
		position: relative;
		font-size: 15px;
		line-height: 24px;
		padding: 33px 15px;
		font-weight: 400;
		color: #75767a;
		margin-bottom: 0;
		border-top: 1px solid rgba(255,255,255, .1);
		a {
			color: inherit;
			&:hover {
				color: #ffffff;
			}
		}
	}
}