{"name": "ramsey/uuid", "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "license": "MIT", "type": "library", "keywords": ["uuid", "identifier", "guid"], "require": {"php": "^8.0", "ext-json": "*", "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11", "ramsey/collection": "^1.2 || ^2.0"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "replace": {"rhumsaa/uuid": "self.version"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Ramsey\\Uuid\\Benchmark\\": "tests/benchmark/", "Ramsey\\Uuid\\StaticAnalysis\\": "tests/static-analysis/", "Ramsey\\Uuid\\Test\\": "tests/"}}, "config": {"allow-plugins": {"captainhook/plugin-composer": true, "ergebnis/composer-normalize": true, "phpstan/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ramsey/composer-repl": true}, "sort-packages": true}, "extra": {"captainhook": {"force-install": true}}, "scripts": {"analyze": ["@phpstan", "@psalm"], "build:clean": "git clean -fX build/", "lint": "parallel-lint src tests", "lint:paths": "parallel-lint", "phpbench": "phpbench run", "phpcbf": "phpcbf -vpw --cache=build/cache/phpcs.cache", "phpcs": "phpcs --cache=build/cache/phpcs.cache", "phpstan": ["phpstan analyse --no-progress --memory-limit=1G", "phpstan analyse -c phpstan-tests.neon --no-progress --memory-limit=1G"], "phpunit": "phpunit --verbose --colors=always", "phpunit-coverage": "phpunit --verbose --colors=always --coverage-html build/coverage", "psalm": "psalm --show-info=false --config=psalm.xml", "test": ["@lint", "@phpbench", "@phpcs", "@phpstan", "@psalm", "@phpunit"]}}