<!DOCTYPE html>
<html @if(session('sess_lang_direction') == 'Right to Left (RTL)') dir="rtl" @endif lang="{{ session('sess_lang_code') }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <title>@yield('seo_title')</title>
    <meta name="description" content="@yield('seo_meta_description')">

    <link rel="shortcut icon" href="{{ asset('uploads/'.$global_setting->favicon) }}" type="image/x-icon">
    <link rel="icon" href="{{ asset('uploads/'.$global_setting->favicon) }}" type="image/x-icon">

    @include('front.layouts.styles')

    @yield('dark_mode')

    @if($global_setting->sticky_header == 'Hide')
    <style >
        .sticky-header.fixed-header {
            display: none;
        }
    </style>
    @endif

    @if($global_setting->tawk_live_chat_status == 'Show')
		<style >
		.scroll-to-top {
			bottom: 50px!important;
		}
		</style>
    @endif

    @if($global_setting->cookie_consent_status == 'Show')
        <!-- Cookie Consent Styles -->
        <link rel="stylesheet" href="{{ asset('css/cookie-consent.css') }}?v={{ time() }}">

        <!-- Additional Inline Styles for Cookie Consent -->
        <style>
            @keyframes cookieSlideIn {
                from {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                to {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .cookie-banner-enter {
                animation: cookieSlideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }

            .cookie-banner-content::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, #9333ea, #ec4899, #8b5cf6);
                background-size: 200% 100%;
                animation: gradientShift 2s linear infinite;
            }

            .cookie-icon-container:hover {
                transform: scale(1.1) rotate(5deg);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
            }

            .cookie-accept-btn:hover {
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
                transform: translateY(-2px) scale(1.05);
            }

            .cookie-decline-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
        </style>

        <!-- Compact Cookie Consent Banner -->
        <div id="cookieConsentBanner" class="fixed bottom-6 right-6 z-50 max-w-sm transform translate-x-full transition-all duration-700 ease-out opacity-0">
            <!-- Floating Card -->
            <div class="cookie-banner-content relative bg-white rounded-2xl shadow-2xl border border-purple-200 overflow-hidden backdrop-blur-lg">
                <!-- Gradient Border Animation -->
                <div class="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 rounded-2xl opacity-75 animate-pulse"></div>
                <div class="absolute inset-[2px] bg-white rounded-2xl"></div>

                <!-- Content -->
                <div class="relative p-4">
                    <!-- Header with Cookie Icon -->
                    <div class="flex items-center gap-3 mb-3">
                        <div class="cookie-icon-container w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                            <span class="text-lg animate-bounce">🍪</span>
                        </div>
                        <div class="flex-1">
                            <h3 class="cookie-title text-sm font-bold text-gray-800 flex items-center gap-1">
                                <span>ملفات تعريف الارتباط</span>
                                <div class="w-1.5 h-1.5 bg-purple-500 rounded-full animate-ping"></div>
                            </h3>
                        </div>
                        <!-- Close Button -->
                        <button id="cookieClose" class="cookie-close-btn w-6 h-6 bg-gray-100 hover:bg-red-100 rounded-full flex items-center justify-center transition-all duration-200 group">
                            <svg class="w-3 h-3 text-gray-400 group-hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Message -->
                    <p class="text-xs text-gray-600 leading-relaxed mb-4">
                        {{ $global_setting->cookie_consent_message }}
                        <a href="#" class="cookie-learn-more text-purple-600 hover:text-purple-700 font-medium underline decoration-dotted underline-offset-2">
                            تعرف على المزيد
                        </a>
                    </p>

                    <!-- Action Buttons -->
                    <div class="flex gap-2">
                        <button id="cookieDecline" class="cookie-decline-btn flex-1 px-3 py-2 text-xs font-medium text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all duration-200 border border-gray-200">
                            رفض
                        </button>
                        <button id="cookieAccept" class="cookie-accept-btn flex-1 px-3 py-2 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                            {{ $global_setting->cookie_consent_button_text }}
                        </button>
                    </div>

                    <!-- Powered by (smaller) -->
                    <div class="mt-3 pt-2 border-t border-gray-100">
                        <p class="text-[10px] text-gray-400 text-center">
                            Powered by <span class="font-medium text-purple-400">WebsitePolicies</span>
                        </p>
                    </div>
                </div>

                <!-- Floating Particles Effect -->
                <div class="absolute top-2 right-2 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-60"></div>
                <div class="absolute bottom-3 left-3 w-0.5 h-0.5 bg-pink-400 rounded-full animate-pulse opacity-40"></div>
                <div class="absolute top-1/2 left-1 w-0.5 h-0.5 bg-purple-300 rounded-full animate-bounce opacity-50"></div>
            </div>
        </div>

        <!-- Cookie Consent Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const banner = document.getElementById('cookieConsentBanner');
                const acceptBtn = document.getElementById('cookieAccept');
                const declineBtn = document.getElementById('cookieDecline');
                const closeBtn = document.getElementById('cookieClose');

                // Check if user has already made a choice
                if (!localStorage.getItem('cookieConsent')) {
                    // Show banner after a short delay with slide-in animation
                    setTimeout(() => {
                        banner.classList.remove('translate-x-full', 'opacity-0');
                        banner.classList.add('cookie-banner-enter');

                        // Add subtle shake animation to get attention
                        setTimeout(() => {
                            banner.style.animation = 'shake 0.5s ease-in-out';
                        }, 800);
                    }, 1000); // Reduced delay for testing
                }

                // Accept cookies
                acceptBtn.addEventListener('click', function() {
                    // Add click animation
                    acceptBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        acceptBtn.style.transform = 'scale(1)';
                    }, 100);

                    localStorage.setItem('cookieConsent', 'accepted');
                    hideBanner('accepted');
                });

                // Decline cookies
                declineBtn.addEventListener('click', function() {
                    // Add click animation
                    declineBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        declineBtn.style.transform = 'scale(1)';
                    }, 100);

                    localStorage.setItem('cookieConsent', 'declined');
                    hideBanner('declined');
                });

                // Close button (same as decline)
                closeBtn.addEventListener('click', function() {
                    localStorage.setItem('cookieConsent', 'dismissed');
                    hideBanner('dismissed');
                });

                function hideBanner(action = 'dismissed') {
                    // Add slide-out animation
                    banner.style.transform = 'translateX(100%) scale(0.8)';
                    banner.style.opacity = '0';

                    setTimeout(() => {
                        banner.style.display = 'none';

                        // Show mini confirmation toast
                        if (action === 'accepted') {
                            showMiniToast('✅ تم القبول', 'success');
                        } else if (action === 'declined') {
                            showMiniToast('❌ تم الرفض', 'error');
                        }
                    }, 400);
                }

                // Mini toast notification function
                function showMiniToast(message, type = 'info') {
                    const toast = document.createElement('div');
                    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

                    toast.className = `fixed bottom-20 right-6 z-50 px-4 py-2 rounded-full shadow-lg text-white text-xs font-medium transform translate-x-full transition-all duration-300 ${bgColor}`;
                    toast.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span>${message}</span>
                            <div class="w-1 h-1 bg-white rounded-full animate-ping"></div>
                        </div>
                    `;

                    document.body.appendChild(toast);

                    // Show toast with bounce effect
                    setTimeout(() => {
                        toast.classList.remove('translate-x-full');
                        toast.style.transform = 'translateX(0) scale(1.1)';
                        setTimeout(() => {
                            toast.style.transform = 'translateX(0) scale(1)';
                        }, 150);
                    }, 100);

                    // Hide toast after 2 seconds
                    setTimeout(() => {
                        toast.style.transform = 'translateX(100%) scale(0.8)';
                        toast.style.opacity = '0';
                        setTimeout(() => {
                            if (document.body.contains(toast)) {
                                document.body.removeChild(toast);
                            }
                        }, 300);
                    }, 2000);
                }
            });
        </script>
    @endif

    @if($global_setting->google_analytic_status == 'Show')
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ $global_setting->google_analytic_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', '{{ $global_setting->google_analytic_id }}');
    </script>
    @endif

    <style >
    :root {
        --theme-color1: #{{ $global_setting->theme_color }};
    }
    </style>

</head>

<body>

    @if($global_setting->preloader == 'Show')
    <div class="preloader"></div>
    @endif

    <!-- Top Bar -->
    <div class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-10 text-sm">
                <!-- Left Side - Contact Info -->
                <div class="flex items-center space-x-4 text-gray-300">
                    @if($global_setting->top_bar_email)
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <a href="mailto:{{ $global_setting->top_bar_email }}" class="hover:text-blue-400 transition-colors">{{ $global_setting->top_bar_email }}</a>
                    </div>
                    @endif

                    @if($global_setting->top_bar_phone)
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <a href="tel:{{ $global_setting->top_bar_phone }}" class="hover:text-green-400 transition-colors">{{ $global_setting->top_bar_phone }}</a>
                    </div>
                    @endif

                    @if($global_setting->top_bar_address)
                    <div class="hidden md:flex items-center space-x-2">
                        <svg class="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>{{ $global_setting->top_bar_address }}</span>
                    </div>
                    @endif
                </div>

                <!-- Right Side - Social Media -->
                <div class="flex items-center space-x-3">
                    <span class="text-xs text-gray-400 hidden lg:block" >تابعنا     :</span>
                    @if($global_setting->facebook && $global_setting->facebook != '')
                    <a href="{{ $global_setting->facebook }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-blue-600 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="Facebook">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                    @endif

                    @if($global_setting->twitter && $global_setting->twitter != '')
                    <a href="{{ $global_setting->twitter }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-sky-500 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="Twitter">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                    </a>
                    @endif

                    @if($global_setting->linkedin && $global_setting->linkedin != '')
                    <a href="{{ $global_setting->linkedin }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-blue-700 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="LinkedIn">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    @endif

                    @if($global_setting->instagram && $global_setting->instagram != '')
                    <a href="{{ $global_setting->instagram }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="Instagram">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.40s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    @endif

                    @if($global_setting->youtube && $global_setting->youtube != '')
                    <a href="{{ $global_setting->youtube }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-red-600 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="YouTube">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                    </a>
                    @endif

                    @if($global_setting->pinterest && $global_setting->pinterest != '')
                    <a href="{{ $global_setting->pinterest }}" target="_blank" rel="noopener noreferrer" class="social-icon w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:bg-red-700 hover:text-white transition-all duration-300 hover:scale-110 cursor-pointer" title="Pinterest">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 1.139 0 2.241-.16 3.282-.45-.187-.906-.348-2.187-.348-3.08 0-.647.263-1.411.432-2.35l1.433-6.038c.263-.72.263-1.411.263-2.187 0-1.411-.432-2.35-1.618-2.35-.72 0-1.411.263-1.411 1.618 0 .72.263 1.618.263 2.35 0 .72-.432 1.411-1.411 1.411-.906 0-1.618-.72-1.618-2.35 0-2.35 1.618-4.237 4.237-4.237 2.35 0 4.237 1.618 4.237 4.237 0 2.35-1.618 4.237-4.237 4.237-.72 0-1.411-.263-1.618-.72-.263.72-.432 1.411-.72 2.35-.263.72-.432 1.411-.432 2.35 0 .72.263 1.411.432 2.35C18.107 22.987 24.004 18.107 24.004 11.987 24.004 5.367 18.637.001 12.017.001z"/>
                        </svg>
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Header -->
    <header class="bg-white shadow-lg border-b border-[#d9d9d9] relative z-40">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-white"></div>

        <nav class="relative py-2">
        <div class="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
            <div class="relative flex h-16 items-center justify-between">
                <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
                    <!-- Mobile menu button-->
                    <button type="button" id="mobile-menu-button"
                        class="relative inline-flex items-center justify-center rounded-md p-2 text-black hover:bg-purple-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-400"
                        aria-controls="mobile-menu" aria-expanded="false">
                        <span class="absolute -inset-0.5"></span>
                        <span class="sr-only">Open main menu</span>
                        <!-- Icon when menu is closed -->
                        <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                            aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                        </svg>
                        <!-- Icon when menu is open -->
                        <svg class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                            aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                    <div class="flex flex-shrink-0 items-center">
                        <a href="{{ route('home') }}" class="group">
                            <img class="h-16 w-auto transition-all duration-300 group-hover:scale-105"
                                 style="filter: drop-shadow(1px 1px 0px #d1d5db) drop-shadow(-1px -1px 0px #d1d5db) drop-shadow(1px -1px 0px #d1d5db) drop-shadow(-1px 1px 0px #d1d5db) drop-shadow(2px 2px 6px rgba(156,163,175,0.4));"
                                 src="{{ asset('uploads/'.$global_setting->logo) }}" alt="شعار برونكس">
                        </a>
                    </div>
                    <div class="hidden sm:ml-6 sm:block pr-16">
                        <div class="flex space-x-6 py-10">

                            @foreach($global_menu as $item)
                            @php
                                $menu_arr[$item->name] = $item->status;
                            @endphp
                            @endforeach

                            <!-- 1. الرئيسية -->
                            @if($global_setting->home_show == 'All')
                            <a href="{{ route('home_1') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ (Request::is('/')||Route::is('home_1')||Route::is('home_2')||Route::is('home_3')||Route::is('home_4')) ? 'text-purple-700' : '' }}" aria-current="page">الرئيسية</a>
                            @endif

                            @if($global_setting->home_show == 'Home 1')
                            <a href="{{ route('home_1') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Route::is('home_1') ? 'text-purple-700' : '' }}">الرئيسية</a>
                            @endif

                            @if($global_setting->home_show == 'Home 2')
                            <a href="{{ route('home_2') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Route::is('home_2') ? 'text-purple-700' : '' }}">الرئيسية</a>
                            @endif

                            @if($global_setting->home_show == 'Home 3')
                            <a href="{{ route('home_3') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Route::is('home_3') ? 'text-purple-700' : '' }}">الرئيسية</a>
                            @endif

                            @if($global_setting->home_show == 'Home 4')
                            <a href="{{ route('home_4') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Route::is('home_4') ? 'text-purple-700' : '' }}">الرئيسية</a>
                            @endif

                            <!-- 2. المميزات (الخدمات) -->
                            <a href="{{ route('services') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Request::is('services') ? 'text-purple-700' : '' }}">المميزات</a>

                            <!-- 3. التسعير -->
                            @if($menu_arr['Pricing'] == 'Show')
                            <a href="{{ route('pricing_plans') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Request::is('pricing') ? 'text-purple-700' : '' }}">التسعير</a>
                            @endif

                            <!-- 4. الأدوات والتكاملات -->
                            <a href="{{ route('portfolios') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold whitespace-nowrap {{ Request::is('portfolios') ? 'text-purple-700' : '' }}">الأدوات والتكاملات</a>

                            <!-- 5. مدونة -->
                            @if($menu_arr['Blog'] == 'Show')
                            <a href="{{ route('blog') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Request::is('blog') ? 'text-purple-700' : '' }}">مدونة</a>
                            @endif

                            <!-- 6. اتصال -->
                            <a href="{{ route('contact') }}" class="text-black hover:text-purple-700 px-4 py-2 transition-all duration-300 font-semibold {{ Request::is('contact') ? 'text-purple-700' : '' }}">اتصال</a>

                        </div>
                    </div>

                    <!-- Right Section: Call & Search -->
                    <div class="flex items-center space-x-4">
                        <!-- Call Section -->
                        @if($global_setting->top_bar_phone)
                        <div class="hidden xl:flex items-center space-x-3 bg-gray-50 border border-[#d9d9d9] rounded-xl px-4 py-2 hover:bg-gray-100 transition-all duration-300">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-700 rounded-full flex items-center justify-center shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                </div>
                                <div class="text-right">
                                    <div class="text-xs text-gray-600 font-medium">اتصل في أي وقت</div>
                                    <a href="tel:{{ $global_setting->top_bar_phone }}" class="text-black font-bold text-sm hover:text-purple-700 transition-colors">{{ $global_setting->top_bar_phone }}</a>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Search Icon - Perfect Position -->
                        <div class="relative">
                            <button type="button" id="search-btn" class="w-12 h-12 bg-gray-50 border border-[#d9d9d9] rounded-xl flex items-center justify-center hover:bg-purple-500 hover:border-purple-500 transition-all duration-300 group shadow-sm hover:shadow-md">
                                <svg class="w-6 h-6 text-black group-hover:text-white transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                            <!-- Search Tooltip -->
                            <div class="absolute -bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                                البحث
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="lg:hidden">
                        <button type="button" id="mobile-menu-button" class="mobile-menu-btn">
                            <span class="sr-only">فتح القائمة الرئيسية</span>
                            <div class="hamburger">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

        <!-- Mobile menu, show/hide based on menu state -->
        <div class="sm:hidden bg-white border-t border-[#d9d9d9]" id="mobile-menu">
            <div class="space-y-1 px-2 pb-3 pt-2">
                <!-- Mobile Home Link -->
                @if($global_setting->home_show == 'All')
                <a href="{{ route('home_1') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الرئيسية</a>
                @endif

                @if($global_setting->home_show == 'Home 1')
                <a href="{{ route('home_1') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الرئيسية</a>
                @endif

                @if($global_setting->home_show == 'Home 2')
                <a href="{{ route('home_2') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الرئيسية</a>
                @endif

                @if($global_setting->home_show == 'Home 3')
                <a href="{{ route('home_3') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الرئيسية</a>
                @endif

                @if($global_setting->home_show == 'Home 4')
                <a href="{{ route('home_4') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الرئيسية</a>
                @endif

                <!-- Mobile Services Link -->
                <a href="{{ route('services') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">المميزات</a>

                <!-- Mobile Pricing Link -->
                @if($menu_arr['Pricing'] == 'Show')
                <a href="{{ route('pricing_plans') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">التسعير</a>
                @endif

                <!-- Mobile Portfolios Link -->
                <a href="{{ route('portfolios') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">الأدوات والتكاملات</a>

                <!-- Mobile Blog Link -->
                @if($menu_arr['Blog'] == 'Show')
                <a href="{{ route('blog') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">مدونة</a>
                @endif

                <!-- Mobile Contact Link -->
                <a href="{{ route('contact') }}" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">اتصال</a>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var mobileMenuButton = document.getElementById('mobile-menu-button');
                var mobileMenu = document.getElementById('mobile-menu');

                mobileMenuButton.addEventListener('click', function () {
                    var expanded = this.getAttribute('aria-expanded') === 'true';

                    if (expanded) {
                        mobileMenu.classList.remove('block');
                        mobileMenu.classList.add('hidden');
                        this.setAttribute('aria-expanded', 'false');
                    } else {
                        mobileMenu.classList.remove('hidden');
                        mobileMenu.classList.add('block');
                        this.setAttribute('aria-expanded', 'true');
                    }
                });
            });
        </script>
        </nav>
    </header>

    <!-- Main Content -->
    @yield('content')


    <!-- WhatsApp Float Button -->
    @if($global_setting->top_bar_phone)
    <a href="https://wa.me/+967{{ $global_setting->top_bar_phone }}" class="whatsapp-float" target="_blank" title="تواصل معنا عبر واتساب">
    @else
    <a href="https://wa.me/+967779500091" class="whatsapp-float" target="_blank" title="تواصل معنا عبر واتساب">
    @endif
        <span class="whatsapp-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 256 258">
                <defs>
                    <linearGradient id="logosWhatsappIcon0" x1="50%" x2="50%" y1="100%" y2="0%">
                        <stop offset="0%" stop-color="#1faf38"/>
                        <stop offset="100%" stop-color="#60d669"/>
                    </linearGradient>
                    <linearGradient id="logosWhatsappIcon1" x1="50%" x2="50%" y1="100%" y2="0%">
                        <stop offset="0%" stop-color="#f9f9f9"/>
                        <stop offset="100%" stop-color="#fff"/>
                    </linearGradient>
                </defs>
                <path fill="url(#logosWhatsappIcon0)" d="M5.463 127.456c-.006 21.677 5.658 42.843 16.428 61.499L4.433 252.697l65.232-17.104a123 123 0 0 0 58.8 14.97h.054c67.815 0 123.018-55.183 123.047-123.01c.013-32.867-12.775-63.773-36.009-87.025c-23.23-23.25-54.125-36.061-87.043-36.076c-67.823 0-123.022 55.18-123.05 123.004"/>
                <path fill="url(#logosWhatsappIcon1)" d="M1.07 127.416c-.007 22.457 5.86 44.38 17.014 63.704L0 257.147l67.571-17.717c18.618 10.151 39.58 15.503 60.91 15.511h.055c70.248 0 127.434-57.168 127.464-127.423c.012-34.048-13.236-66.065-37.3-90.15C194.633 13.286 162.633.014 128.536 0C58.276 0 1.099 57.16 1.071 127.416m40.24 60.376l-2.523-4.005c-10.606-16.864-16.204-36.352-16.196-56.363C22.614 69.029 70.138 21.52 128.576 21.52c28.3.012 54.896 11.044 74.9 31.06c20.003 20.018 31.01 46.628 31.003 74.93c-.026 58.395-47.551 105.91-105.943 105.91h-.042c-19.013-.01-37.66-5.116-53.922-14.765l-3.87-2.295l-40.098 10.513z"/>
                <path fill="#fff" d="M96.678 74.148c-2.386-5.303-4.897-5.41-7.166-5.503c-1.858-.08-3.982-.074-6.104-.074c-2.124 0-5.575.799-8.492 3.984c-2.92 3.188-11.148 10.892-11.148 26.561s11.413 30.813 13.004 32.94c1.593 2.123 22.033 35.307 54.405 48.073c26.904 10.609 32.379 8.499 38.218 7.967c5.84-.53 18.844-7.702 21.497-15.139c2.655-7.436 2.655-13.81 1.859-15.142c-.796-1.327-2.92-2.124-6.105-3.716s-18.844-9.298-21.763-10.361c-2.92-1.062-5.043-1.592-7.167 1.597c-2.124 3.184-8.223 10.356-10.082 12.48c-1.857 2.129-3.716 2.394-6.9.801c-3.187-1.598-13.444-4.957-25.613-15.806c-9.468-8.442-15.86-18.867-17.718-22.056c-1.858-3.184-.199-4.91 1.398-6.497c1.431-1.427 3.186-3.719 4.78-5.578c1.588-1.86 2.118-3.187 3.18-5.311c1.063-2.126.531-3.986-.264-5.579c-.798-1.593-6.987-17.343-9.819-23.64"/>
            </svg>
        </span>
    </a>

    <!-- TBL-Tech Footer -->
    <footer aria-label="Site Footer" class="bg-gray-100 relative">
        <!-- Top Border -->
        <div class="h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 shadow-sm"></div>

        <div class="relative max-w-7xl px-6 py-16 mx-auto sm:px-8 lg:px-10">
            <!-- Main Footer Content -->
            <div class="grid grid-cols-1 gap-12 lg:grid-cols-6">
                <!-- Company Info -->
                <div class="lg:col-span-2 text-center lg:text-right">
                    <!-- Logo -->
                    <div class="mb-6">
                        <a href="{{ route('home') }}" class="inline-block group">
                            <img class="h-16 w-auto transition-all duration-300 group-hover:scale-105 mx-auto lg:mx-0"
                                 style="filter: drop-shadow(1px 1px 0px #d1d5db) drop-shadow(-1px -1px 0px #d1d5db) drop-shadow(1px -1px 0px #d1d5db) drop-shadow(-1px 1px 0px #d1d5db) drop-shadow(2px 2px 6px rgba(156,163,175,0.4));"
                                 src="{{ asset('uploads/'.$global_setting->logo) }}" alt="شعار برونكس">
                        </a>
                    </div>

                    <!-- Company Description -->
                    <p class="text-blue-500 text-base leading-relaxed mb-8 max-w-md mx-auto lg:mx-0">
                        @if($global_setting->footer_text)
                            {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_setting->footer_text))) !!}
                        @else
                            نقدم حلولاً تقنية متطورة ومبتكرة لتطوير أعمالكم وتحقيق أهدافكم الرقمية بأحدث التقنيات والأساليب المتقدمة
                        @endif
                    </p>



                    <!-- Social Media -->
                    <div>
                        <h3 class="text-gray-900 font-semibold mb-6 text-center lg:text-right flex items-center justify-center lg:justify-start">
                            <svg class="w-5 h-5 ml-2 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            تابعنا على
                        </h3>
                        <div class="flex gap-2 justify-center lg:justify-start">
                            @if($global_setting->facebook!='')
                            <a href="{{ $global_setting->facebook }}" rel="noreferrer" target="_blank"
                                class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 hover:scale-110 transition-all duration-300 shadow-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            @endif

                            @if($global_setting->instagram!='')
                            <a href="{{ $global_setting->instagram }}" rel="noreferrer" target="_blank"
                                class="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center text-white hover:from-pink-400 hover:to-purple-500 hover:scale-110 transition-all duration-300 shadow-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                                </svg>

                            </a>
                            @endif

                            @if($global_setting->twitter!='')
                            <a href="{{ $global_setting->twitter }}" rel="noreferrer" target="_blank"
                                class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center text-white hover:bg-sky-600 hover:scale-110 transition-all duration-300 shadow-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                                </svg>
                            </a>
                            @endif

                            @if($global_setting->linkedin!='')
                            <a href="{{ $global_setting->linkedin }}" rel="noreferrer" target="_blank"
                                class="w-8 h-8 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 hover:scale-110 transition-all duration-300 shadow-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M19 0H5a5 5 0 00-5 5v14a5 5 0 005 5h14a5 5 0 005-5V5a5 5 0 00-5-5zM8 19H5V8h3v11zM6.5 6.732c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zM20 19h-3v-5.604c0-3.368-4-3.113-4 0V19h-3V8h3v1.765c1.396-2.586 7-2.777 7 2.476V19z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            @endif

                            @if($global_setting->youtube!='')
                            <a href="{{ $global_setting->youtube }}" rel="noreferrer" target="_blank"
                                class="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white hover:bg-red-700 hover:scale-110 transition-all duration-300 shadow-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-6 sm:grid-cols-4 lg:col-span-3 lg:grid-cols-4">
                    <div>
                        <p class="font-medium text-gray-900 mb-3 text-sm">عن برونكس</p>
                        <nav class="space-y-2">
                            @if($menu_arr['About'] == 'Show')
                            <a href="{{ route('about') }}" class="block text-gray-700 hover:text-purple-500 transition text-xs">
                                من نحن
                            </a>
                            @endif
                            @if($menu_arr['Team Members'] == 'Show')
                            <a href="{{ route('team_members') }}" class="block text-gray-700 hover:text-purple-500 transition text-xs">
                                أعضاء الفريق
                            </a>
                            @endif
                            @if($menu_arr['Portfolios'] == 'Show')
                            <a href="{{ route('portfolios') }}" class="block text-gray-700 hover:text-purple-500 transition text-xs">
                                أعمالنا
                            </a>
                            @endif
                        </nav>
                    </div>

                    <div>
                        <p class="font-medium text-gray-900">خدماتنا</p>
                        <nav aria-label="Footer Navigation - Services" class="mt-6">
                            <ul class="space-y-4 text-sm">
                                @if($menu_arr['Services'] == 'Show')
                                <li>
                                    <a href="{{ route('services') }}" class="text-gray-700 transition hover:text-purple-500">
                                        جميع الخدمات
                                    </a>
                                </li>
                                @endif
                                @if($menu_arr['Testimonials'] == 'Show')
                                <li>
                                    <a href="{{ route('testimonials') }}" class="text-gray-700 transition hover:text-purple-500">
                                     الشهادات التوصيات
                                    </a>
                                </li>
                                @endif
                                @if($menu_arr['Pricing'] == 'Show')
                                <li>
                                    <a href="{{ route('pricing_plans') }}" class="text-gray-700 transition hover:text-purple-500">
                                        التسعير
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </nav>
                    </div>

                    <div>
                        <p class="font-medium text-gray-900">الأدوات والتكاملات</p>
                        <nav aria-label="Footer Navigation - Tools" class="mt-6">
                            <ul class="space-y-4 text-sm">
                                @if($menu_arr['FAQ'] == 'Show')
                                <li>
                                    <a href="{{ route('faqs') }}" class="text-gray-700 transition hover:text-purple-500">
                                        التعليمات
                                    </a>
                                </li>
                                @endif
                                @if($menu_arr['Blog'] == 'Show')
                                <li>
                                    <a href="{{ route('blog') }}" class="text-gray-700 transition hover:text-purple-500">
                                        المدونة
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </nav>
                    </div>

                    <div>
                        <p class="font-medium text-gray-900">روابط مفيدة</p>
                        <nav aria-label="Footer Navigation - Links" class="mt-6">
                            <ul class="space-y-4 text-sm">
                                @if($menu_arr['Contact'] == 'Show')
                                <li>
                                    <a href="{{ route('contact') }}" class="text-gray-700 transition hover:text-purple-500">
                                        اتصال
                                    </a>
                                </li>
                                @endif
                                <li>
                                    <a href="#" class="text-gray-700 transition hover:text-purple-500">
                                        شروط الاستخدام
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="text-gray-700 transition hover:text-purple-500">
                                        سياسة الخصوصية
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Newsletter Subscription -->
                <div class="lg:col-span-1">
                    @if($global_setting->footer_subscriber_heading)
                    <p class="font-medium text-gray-900 mb-3 text-sm">{{ $global_setting->footer_subscriber_heading }}</p>
                    @else
                    <p class="font-medium text-gray-900 mb-3 text-sm">اشترك في النشرة الإخبارية</p>
                    @endif

                    @if($global_setting->footer_subscriber_text)
                    <p class="text-blue-500 mb-4 text-xs leading-relaxed">{{ $global_setting->footer_subscriber_text }}</p>
                    @else
                    <p class="text-blue-500 mb-4 text-xs leading-relaxed">احصل على آخر الأخبار والتحديثات</p>
                    @endif

                    <div class="flex flex-col gap-2">
                        <input type="email" placeholder="بريدك الإلكتروني"
                            class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-xs">
                        <button type="button" onclick="alert('شكراً لاهتمامك! سيتم تفعيل هذه الخدمة قريباً')"
                            class="px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors text-xs">
                            اشترك
                        </button>
                    </div>
                </div>

            </div>

            <p class="text-xs text-center text-gray-500">
                {{ $global_setting->footer_copyright }}
            </p>
        </div>
    </footer>

    <!-- WhatsApp Float Button Styles -->
    <style>
        .whatsapp-float {
            position: fixed;
            width: 60px;
            height: 60px;
            bottom: 40px;
            right: 40px;
            background-color: #25d366;
            color: #FFF;
            border-radius: 50px;
            text-align: center;
            font-size: 30px;
            box-shadow: 2px 2px 3px #999;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .whatsapp-float:hover {
            background-color: #128c7e;
            transform: scale(1.1);
        }

        /* Footer Animation Delays */
        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }

        .whatsapp-icon svg {
            width: 35px;
            height: 35px;
        }

        /* Search Button Enhancement */
        #search-btn {
            position: relative;
            overflow: hidden;
        }

        #search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #search-btn:hover::before {
            left: 100%;
        }

        /* Search Button Pulse Animation */
        @keyframes searchPulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
            }
        }

        #search-btn:hover {
            animation: searchPulse 1.5s infinite;
        }

        /* Perfect Search Icon Positioning */
        .search-container {
            position: relative;
            z-index: 10;
        }

        .search-tooltip {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }

        .search-container:hover .search-tooltip {
            opacity: 1;
        }
    </style>
    
    @include('front.layouts.scripts')

    <!-- Search Overlay -->
    <div id="search-overlay" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden opacity-0 transition-opacity duration-300">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl transform transition-transform duration-300">
                <!-- Search Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-xl font-bold text-gray-800">البحث في الموقع</h3>
                    <button id="search-close" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Search Form -->
                <div class="p-6">
                    <form action="#" method="GET" class="space-y-4">
                        <div class="relative">
                            <input
                                type="text"
                                name="q"
                                id="search-input"
                                placeholder="ابحث عن الخدمات، المقالات، أو أي محتوى..."
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                                autocomplete="off"
                            >
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                اضغط Enter للبحث أو Esc للإغلاق
                            </div>
                            <button type="submit" class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 font-semibold">
                                بحث
                            </button>
                        </div>
                    </form>

                    <!-- Quick Links -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-600 mb-3">روابط سريعة:</h4>
                        <div class="grid grid-cols-2 gap-2">
                            <a href="{{ route('services') }}" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">الخدمات</a>
                            <a href="{{ route('blog') }}" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">المدونة</a>
                            <a href="{{ route('portfolios') }}" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">أعمالنا</a>
                            <a href="{{ route('contact') }}" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">اتصل بنا</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchBtn = document.getElementById('search-btn');
            const searchOverlay = document.getElementById('search-overlay');
            const searchClose = document.getElementById('search-close');
            const searchInput = document.getElementById('search-input');

            if (searchBtn && searchOverlay) {
                searchBtn.addEventListener('click', function() {
                    searchOverlay.classList.remove('hidden');
                    setTimeout(() => {
                        searchOverlay.classList.remove('opacity-0');
                        searchInput.focus();
                    }, 10);
                });

                searchClose.addEventListener('click', function() {
                    searchOverlay.classList.add('opacity-0');
                    setTimeout(() => {
                        searchOverlay.classList.add('hidden');
                    }, 300);
                });

                // Close on escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !searchOverlay.classList.contains('hidden')) {
                        searchClose.click();
                    }
                });

                // Close on overlay click
                searchOverlay.addEventListener('click', function(e) {
                    if (e.target === searchOverlay) {
                        searchClose.click();
                    }
                });
            }
        });
    </script>

    @if($global_setting->tawk_live_chat_status == 'Show')
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/{{ $global_setting->tawk_live_chat_property_id }}/default';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
    @endif

</body>
</html>