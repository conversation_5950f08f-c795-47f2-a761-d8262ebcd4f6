<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Event;

use function count;
use Iterator;

/**
 * @template-implements Iterator<int, Event>
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final class EventCollectionIterator implements Iterator
{
    /**
     * @psalm-var list<Event>
     */
    private readonly array $events;
    private int $position = 0;

    public function __construct(EventCollection $events)
    {
        $this->events = $events->asArray();
    }

    public function rewind(): void
    {
        $this->position = 0;
    }

    public function valid(): bool
    {
        return $this->position < count($this->events);
    }

    public function key(): int
    {
        return $this->position;
    }

    public function current(): Event
    {
        return $this->events[$this->position];
    }

    public function next(): void
    {
        $this->position++;
    }
}
