{"name": "sebastian/diff", "description": "Diff implementation", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "homepage": "https://github.com/sebastian<PERSON>mann/diff", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy"}, "prefer-stable": true, "config": {"platform": {"php": "8.1.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^4.2 || ^5"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"]}, "extra": {"branch-alias": {"dev-main": "5.0-dev"}}}