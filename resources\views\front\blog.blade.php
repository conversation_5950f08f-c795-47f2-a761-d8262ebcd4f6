@extends('front.layouts.master')

@section('seo_title', $global_other_page_items->page_blog_seo_title)
@section('seo_meta_description', $global_other_page_items->page_blog_seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-purple-600 to-indigo-600 py-20 overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $global_other_page_items->page_blog_title }}</h1>
            <p class="text-xl text-purple-100 mb-8">اكتشف أحدث المقالات والأخبار في عالم التقنية والتطوير</p>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 text-white">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-purple-200 transition-colors">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-purple-200">{{ $global_other_page_items->page_blog_title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">أحدث المقالات</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                تابع أحدث المقالات والأخبار في مجال التقنية والتطوير والتصميم
            </p>
        </div>

        @if($posts->count() > 0)
        <!-- Blog Posts Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            @foreach($posts as $item)
            <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

                <!-- Post Image -->
                <div class="relative h-64 overflow-hidden">
                    <img src="{{ asset('uploads/'.$item->photo) }}"
                         alt="{{ $item->title }}"
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">

                    <!-- Date Badge -->
                    <div class="absolute top-4 right-4">
                        <div class="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">{{ $item->created_at->format('d') }}</div>
                                <div class="text-sm text-gray-600">{{ $item->created_at->format('M Y') }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Badge -->
                    @if($item->post_category_id)
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                            {{ $item->postCategory->name ?? 'عام' }}
                        </span>
                    </div>
                    @endif
                </div>

                <!-- Post Content -->
                <div class="p-6">
                    <!-- Post Meta -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span>{{ __('by Admin') }}</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $item->created_at->format('d M, Y') }}</span>
                        </div>
                    </div>

                    <!-- Post Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3 text-right group-hover:text-purple-600 transition-colors line-clamp-2">
                        <a href="{{ route('post',$item->slug) }}" class="hover:underline">
                            {{ $item->title }}
                        </a>
                    </h3>

                    <!-- Post Excerpt -->
                    @if($item->description)
                    <p class="text-gray-600 text-right mb-4 line-clamp-3">
                        {{ Str::limit(strip_tags($item->description), 150) }}
                    </p>
                    @endif

                    <!-- Tags -->
                    @if($item->tags)
                    <div class="flex flex-wrap gap-2 mb-4">
                        @foreach(explode(',', $item->tags) as $tag)
                        <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                            #{{ trim($tag) }}
                        </span>
                        @endforeach
                    </div>
                    @endif

                    <!-- Read More Button -->
                    <div class="text-center">
                        <a href="{{ route('post',$item->slug) }}"
                           class="inline-flex items-center justify-center w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 group">
                            {{ __('Read More') }}
                            <svg class="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </article>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="flex justify-center">
            <div class="bg-white rounded-lg shadow-lg p-4">
                {{ $posts->links() }}
            </div>
        </div>

        @else
        <!-- Empty State -->
        <div class="text-center py-16">
            <div class="max-w-md mx-auto">
                <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">لا توجد مقالات حالياً</h3>
                <p class="text-gray-600">سيتم إضافة المقالات قريباً. تابعونا للاطلاع على أحدث المحتوى.</p>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Newsletter Section -->
<section class="bg-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl p-8 text-white">
            <h2 class="text-3xl font-bold mb-4">اشترك في النشرة الإخبارية</h2>
            <p class="text-xl text-purple-100 mb-8">احصل على أحدث المقالات والأخبار مباشرة في بريدك الإلكتروني</p>

            <form class="max-w-md mx-auto">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input type="email"
                           placeholder="أدخل بريدك الإلكتروني"
                           class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white">
                    <button type="submit"
                            class="px-6 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-purple-50 transition-colors">
                        اشتراك
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="bg-gradient-to-r from-purple-600 to-indigo-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">هل لديك فكرة لمقال؟</h2>
        <p class="text-xl text-purple-100 mb-8">شاركنا أفكارك واقتراحاتك للمحتوى القادم</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact') }}"
               class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-purple-600 bg-white hover:bg-purple-50 transition-colors">
                تواصل معنا
            </a>
            @if($global_setting->whatsapp != null)
            <a href="https://wa.me/{{ $global_setting->whatsapp }}"
               target="_blank"
               class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-purple-600 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                </svg>
                واتساب
            </a>
            @endif
        </div>
    </div>
</section>
@endsection