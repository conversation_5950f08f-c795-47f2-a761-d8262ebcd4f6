/*** 

====================================================================
    Home Layout 3 Styles
====================================================================

***/
/* Service Style Three */
.service-block-new-3 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
  &.at-home6{
    margin-top: -100px;
    @media only screen and (max-width: 1199px){
      margin-top: 0;
    }
  }
}
.service-block-new-3 .inner-box {
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 25px 25px 25px;
  background-color: var(--bg-theme-color1);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  text-align: center;
  min-height: 280px;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  border-top: 6px solid var(--theme-color2);
}
.service-block-new-3 .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  content: "";
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}
.service-block-new-3 .inner-box:hover:before {
  height: 0;
}
.service-block-new-3 .inner-box:hover .title {
  color: #ffffff;
}
.service-block-new-3 .inner-box:hover .text {
  color: #454545;
}
.service-block-new-3 .icon {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100px;
  width: 100px;
  font-size: 64px;
  color: var(--theme-color1);
  font-weight: 900;
  line-height: 64px;
  background-color: #f4f5f8;
  border-radius: 50%;
  margin: 0 auto 25px;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}
.service-block-new-3 .title {
  margin-bottom: 10px;
  line-height: 1.2em;
}
.service-block-new-3 .title:hover {
  color: var(--theme-color1);
}
.service-block-new-3 .text {
  position: relative;
  font-size: 14px;
  line-height: 24px;
  color: #797582;
  font-weight: 500;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

/* Service image with iconbox */

.service-block-new-1 {
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
    .inner-box {
        position: relative;
        background-color: #ffffff;
        padding: 15px;
        border: 1px solid #e6e8ed;
        transition: all 300ms ease;
        &:hover {
            box-shadow: 0 10px 60px rgba(0, 0, 0, .07);
            .image-box img{
                transform: scale(1.1);
            }
            .image-box .image:after {
                left: 0;
                right: 0;
                opacity: 0;
            }
        }
    }
    .image-box {
        position: relative;
        &::before{
            background: linear-gradient(-135deg, rgba(23, 23, 23, 0.0) 50%, var(--theme-color1) 100%);
            height: 100%;
            width: 100%;
            left: 0;
            bottom: 0;
            content: "";
            position: absolute;
            pointer-events: none;
            z-index: 2;
            transition: all .2s ease;
        }
        .image {
            overflow: hidden;
            margin-bottom: 0;
            img {
                width: 100%;
                transition: all 300ms ease;
            }
            &:after {
                background: rgba(255, 255, 255, .3);
                bottom: 0;
                content: "";
                left: 50%;
                position: absolute;
                right: 51%;
                top: 0;
                opacity: 1;
                pointer-events: none;
                transition: all 400ms linear;
            }
        }
        .icon-box{
            position: absolute;
            left: 25px;
            bottom: -10px;
            height: 90px;
            width: 90px;
            background: var(--bg-theme-color2);
            color: #ffffff;
            @include flex-center;
            font-size: 58px;
            z-index: 2;
            transition: all 300ms ease;
            &:before{
                position: absolute;
                bottom: 0;
                left: -10px;
                border-right: 10px solid var(--theme-color2);
                border-bottom: 10px solid transparent;
                content: "";
            }
            &:after {
                position: absolute;
                bottom: 0;
                right: -10px;
                border-left: 10px solid var(--theme-color2);
                border-bottom: 10px solid transparent;
                content: "";
            }
        }
    }
    .content-box {
        position: relative;
        padding: 35px 25px 15px;
        .title{
            margin-bottom: 14px;
            &:hover{
                color: var(--theme-color2);
            }
        }
        .text {
            position: relative;
            margin-bottom: 0;
        }
        .read-more {
            font-size: 12px;
            line-height: 30px;
            color: #808287;
            font-weight: 700;
            text-transform: uppercase;
            display: flex;
            letter-spacing: .1em;
            transition: all 100ms linear;
            margin-top: 15px;
            i {
                margin-left: 10px;
                color: var(--theme-color2);
                font-size: 16px;
                transition: all 100ms linear;
            }
            &:hover{
                color: var(--theme-color2);
                i{
                    transform: translateX(-15px);
                    opacity: 0;
                }
            }
        }
    }
    
}

/*  Video Section Style  */
.video-section-new-3{
    position: relative;
    z-index: 9;
    .video-box-new-3{
        position: relative;
        min-height: 560px;
        margin-top: 20px;
        .bg{
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            &::before{
                @include overlay;
                background-color: #141417;
                opacity: .20;
                content: "";
            }
        }
        .content{
            padding: 240px 0 95px;
            text-align: center;
            .title{
                position: relative;
                color: #fff;
                font-size: 60px;
                margin-bottom: 0;
                margin-top: 60px;
                font-weight: 700;
                @include for-lg{
                    margin-bottom: 30px;
                    font-size: 32px;
                }
            }
            .play-now{
                position: relative;
                display: inline-block;
                margin-bottom: 30px;
                .icon{
                    color: #fff;
                    font-size: 30px;
                    background-color: var(--bg-theme-color2);
                }
            }
        }
    }
}

.play-now{
    .icon{
        width: 100px;
        height: 100px;
        line-height: 100px;
    }
}
/*  Funfact Style At Home Layout 3  */
.fun-fact-section-new-3{
    position: relative;
    background: #000;
    padding: 220px 0 85px;
    margin-top: -100px;
    &::before{
        position: absolute;
        content: "";
        background-image: url(../images/icons/pattern-1.jpg);        
        width: 100%;
        height: 100%;
        top: 0;
        background-size: cover;
    }
    .fact-counter{
        position: relative;
        z-index: 2;
    }
}
.counter-block-new-3 {
    position: relative;
    margin-bottom: 50px;
    &:last-child{
        .inner:before{
            display: none;
        }
    }
    .inner{
        position: relative;
        transition: all 300ms ease;
        text-align: center;
        padding: 35px 20px 30px;
        background-color: #313131;
        &:hover{
            &::before{
                height: 0;
            }
            &:after{
                height: 100%;
            }
        }
    }
    .content{
        position: relative;
        width: 100%;
        text-align: center;
        transition: all 300ms ease;
        z-index: 9;
        &:hover{
            .icon{
                transform: scale(-1) rotate(180deg);
            }
        }
    }
    .icon {
        position: relative;
        display: inline-block;
        color: var(--bg-theme-color1);
        border-radius: 50%;
        font-size: 45px;
        z-index: 1;
        transition: all 300ms ease;
        margin-bottom: 20px;
    }
    .count-box {
        position: relative;
        font-size: 50px;
        font-weight: 400;
        color: #ffffff;
        line-height: 1em;
        font-family: var(--title-font);
        display: block;
        margin-bottom: 15px;
        .count-text {
            font-size:30px;
            font-weight: 700;
            line-height: 1em;
        }
    }
    .counter-title {
        display: block;
        font-size: 14px;
        line-height: 24px;
        color: #ffffff;
        font-weight: normal;
        margin-bottom: 0;
    }
}



.projects-section-home3{
    position: relative;
    z-index: 1;
    .upper-box{
        position: relative;
        padding: 120px 0 50px;
        background-color: #f6f6f6;
        .sec-title{
            margin-bottom: 0;
        }
    }
    .owl-nav{
        position: absolute;
        top: -120px;
        right: 0;
        left: 0;
        margin: 0 auto;
        max-width: 1200px;
        padding: 0 15px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        z-index: 1;
        @include for-sm{
            display: none;
        }
    }
}
.project-block-home3,
.project-block-home4{
    position: relative;
    overflow: hidden;

    .image-box {
        position: relative;
        overflow: hidden;

        .image {
            position: relative;
            margin-bottom: 0;

            &:after,
            &:before {
                position: absolute;
                left: 0;
                bottom: 0;
                height: 100%;
                width: 100%;
                background-image: linear-gradient(transparent, var(--bg-theme-color2));
                z-index: 1;
                content: "";
                pointer-events: none;
                transition: all 300ms ease;
            }

            &:after {
                background-image: linear-gradient(transparent, var(--bg-theme-color1));
                height: 0;
                z-index: 1;
            }

            img {
                display: inline-block;
                max-width: 100%;
                width: 100%;
                height: auto;
                transition: all 0.3s ease;
                min-height: 300px;
                object-fit: cover;
            }

            a {
                position: relative;
                display: block;
            }
        }
    }

    &:hover {
        img {
            opacity: 1;
            transform: scale(1.1);
        }

        .image::before {
            height: 0%;
        }

        .image::after {
            height: 100%;
        }
    }

    .caption-box {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 40px 40px;
        width: 100%;
        text-align: left;
        z-index: 2;

        .title {
            margin-bottom: 0;
            color: #ffffff;
        }
    }
}

/*  Home 4 Services  */
.bg-f7{
    background-color: #f7f7f7;
}
.border-top-1{
    border-top: 1px solid #e4e4e4;
}
.service-block-home4 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
  .inner-box {
    position: relative;
    overflow: hidden;
    height: 100%;
    padding: 45px 50px 45px;
    background-color: #ffffff;
    -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
    box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
    -webkit-transition: all 300ms ease;
    transition: all 300ms ease;
    z-index: 2;
    &:after {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      background-color: var(--bg-theme-color2);
      -webkit-transform: scale(0);
      transform: scale(0);
      -webkit-transform-origin: right top;
      transform-origin: right top;
      border-radius: 0 0 0 500px;
      content: "";
      z-index: -1;
      -webkit-transition: all 300ms linear;
      transition: all 300ms linear;
    }
    &:hover{
      &:after {
        -webkit-transform: scale(1.5);
        transform: scale(1.5);
        opacity: 1;
      }
      .title,
      .text,
      .icon{
        color: #ffffff;
      }
    }
  }
  .count {
    position: absolute;
    right: -40px;
    top: -70px;
    height: 170px;
    width: 170px;
    background-color: var(--bg-theme-color2);
    color: #ffffff;
    border-radius: 50%;
    -webkit-transition: all 300ms linear;
    transition: all 300ms linear;
  }
  em {
    font-style: normal;
    font-size: 30px;
    font-weight: 600;
    font-family: var(--title-font);
    position: absolute;
    left: 60px;
    bottom: 45px;
  }
  .icon {
    position: relative;
    display: block;
    font-size: 62px;
    color: var(--theme-color2);
    font-weight: 400;
    line-height: 1em;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    margin-bottom: 20px;
  }
  .title {
    margin-bottom: 20px;
  }
  .text {
    line-height: 30px;
    -webkit-transition: all 300ms linear;
    transition: all 300ms linear;
  }
}

.home4-image-column{
  .info-box {
    position: absolute;
    left: 0;
    right: 45px;
    top: 0;
    padding: 40px 30px;
    padding-left: 140px;
    background-color: var(--bg-theme-color2);
    z-index: 2;
    @media (max-width: 767.98px) {
      display: block;
      padding-left: 15px;
      position: relative;
    }
    .icon {
      position: absolute;
      left: 40px;
      top: 45px;
      font-size: 68px;
      line-height: 1em;
      color: #fff;
      @media (max-width: 767.98px) {
        margin-bottom: 10px;
        left: 0;
        position: relative;
        top: 0;
      }
    }
    .title {
      font-size: 40px;
      color: #ffffff;
      margin-bottom: 0;
    }
    .sub-title {
      font-size: 18px;
      color: #ffffff;
      letter-spacing: 0.01em;
      font-weight: 500;
      -webkit-transition: all 300ms ease;
      transition: all 300ms ease;
    }
  }
}

.offer-section-home4 {
  position: relative;
  background-color: var(--bg-theme-color2);
}
.offer-section-home4:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url(../images/icons/home4-offer-bg.jpg);
  content: "";
}
.offer-section-home4 .content-column .inner-column {
  position: relative;
  padding: 120px 0 100px;
  padding-right: 80px;
  @media (max-width: 575px) {
    padding-right: 0;
  }
}
.offer-section-home4 .content-column .sec-title {
  margin-bottom: 25px;
}
.offer-section-home4 .content-column .info-box {
  position: relative;
  padding-left: 85px;
  min-height: 70px;
  margin-bottom: 30px;
}
.offer-section-home4 .content-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  font-size: 72px;
  color: var(--theme-color1);
  line-height: 1em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.offer-section-home4 .content-column .info-box .title {
  line-height: 34px;
  margin-bottom: 0;
  color: #fff;
}
.list-style-two {
  position: relative;
}
.offer-section-home4 .content-column .list-style-two li {
  color: #878a8f;
}
.list-style-two li {
  position: relative;
  font-size: 18px;
  line-height: 30px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 30px;
  margin-bottom: 5px;
}
.list-style-two li i {
  position: absolute;
  left: 0;
  top: 0;
  color: var(--theme-color1);
  font-size: 18px;
  line-height: 30px;
}
.offer-section-home4 .image-column .image-box {
  position: relative;
  margin-right: -375px;
}
.offer-section-home4 .image-column .image-box .image {
  position: relative;
  margin-bottom: 0;
  width: 100%;
}
.offer-section-home4 .image-column .image-box .image img {
  width: 100%;
  min-height: 570px;
  -o-object-fit: cover;
  object-fit: cover;
}
.offer-section-home4 .image-column .caption-box {
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  width: 100%;
  max-width: 200px;
  background-color: var(--bg-theme-color1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 50px 30px 45px 35px;
}
.offer-section-home4 .image-column .caption-box .icon-box {
  position: relative;
  margin-bottom: 20px;
}
.offer-section-home4 .image-column .caption-box .title {
  color: var(--theme-color1);
  margin-bottom: 0;
}
.play-now-home4 {
  height: 92px;
  width: 92px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
  border: 0.5px solid var(--theme-color2);
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  -webkit-animation: zoom-one 3s infinite linear;
  animation: zoom-one 3s infinite linear;
  &:before {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    height: 62px;
    width: 62px;
    border-radius: 50%;
    background-color: var(--bg-theme-color2);
    content: "";
  }
  i{
    position: relative;
  }
}

/*  Pricing Section  */


.pricing-section-home4{
    position: relative;
    overflow: hidden;
    padding: 120px 0 70px;
    .content-column {
        position: relative;
        .inner-column {
            position: relative;
            padding-right: 70px;
        }
        .sec-title {
            margin-bottom: 30px;
        }
        .info-box {
            position: relative;
            z-index: 9;
            padding-left: 90px;
            margin-bottom: 40px;
            @media (max-width: 767.98px) {
                padding-left: 0;
            }
            .icon {
                @include absolute;
                @include flex-center;
                height: 58px;
                width: 58px;
                color: var(--theme-color2);
                background-color: var(--bg-theme-color1);
                border-radius: 50%;
                @media (max-width: 767.98px) {
                    position: relative;
                    margin-bottom: 20px;
                }
            }

            .title {
                color: var(--theme-color2);
                margin-bottom: 20px;
            }

            .text {
                letter-spacing: .01em;
                transition: all 300ms ease;
            }
        }
    }

    .pricing-column{
        position: relative;
        .inner-column{
            position: relative;
            margin-left: -30px;
            display: flex;
            align-items: center;
        }
    }
}
/*Pricing Table Style */
.tm-pricing-smart-switcher-button .switch-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
  border: 0;
}
.tm-pricing-smart-switcher-button .switch-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
  border: 0;
}
.tm-pricing-smart-switcher-button .switch-buttons li:first-child a {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}
.tm-pricing-smart-switcher-button .switch-buttons li:first-child a {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.tm-pricing-smart-switcher-button .switch-buttons li a:hover, .tm-pricing-smart-switcher-button .switch-buttons li a.active {
  color: var(--text-color-bg-theme-color1);
  border-color: var(--theme-color1);
  background-color: var(--theme-color1);
}
.tm-pricing-smart-switcher-button .switch-buttons li a {
  display: block;
  border: 1px solid #eee;
  outline: none;
  display: inline-block;
  padding: 0.9375rem 2.1875rem;
  cursor: pointer;
  border-radius: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background-color: rgb(254, 253, 254);
  -webkit-box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
  box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
}
.tm-pricing-smart-switcher-button .switch-buttons li a:hover, .tm-pricing-smart-switcher-button .switch-buttons li a.active {
  color: var(--text-color-bg-theme-color1);
  border-color: var(--theme-color1);
  background-color: var(--theme-color1);
}

.tm-pricing-smart-switcher-button .switch-buttons li a {
  display: block;
  border: 1px solid #eee;
  outline: none;
  display: inline-block;
  padding: 0.9375rem 2.1875rem;
  cursor: pointer;
  border-radius: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background-color: rgb(254, 253, 254);
  -webkit-box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
  box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
}
.team-block-home4 {
    position: relative;
    margin-bottom: 40px;
    .inner-box {
        position: relative;
        padding: 0 11px;
        &:hover {
            .image img {
                transform: scale(1.1);
            }
            .social-links {
                transform: scaleY(1);
                opacity: 1;
                visibility: visible;
            }
            .image-box{
                &::before{
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
    }

    .image-box {
        position: relative;
        &::before {
            @include overlay;
            background: linear-gradient(135deg, rgba(23, 23, 23, 0) 0%, rgba(23, 23, 23, 0.65) 65%, var(--theme-color1) 100%);
            content: "";
            transition: all 300ms ease;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            z-index: 2;
        }
        .image {
            position: relative;
            overflow: hidden;
            margin-bottom: 0;
            z-index: 1;
            img {
                width: 100%;
                transition: all 400ms ease;
            }
        }
        
    }

    .info-box {
        position: relative;
        padding: 25px 37px;
        border: 2px solid #171717;
        border-top: 0;
        .name {
            font-weight: 600;
            z-index: 2;
            margin-bottom: 7px;
            &:hover{
                color: var(--theme-color2);
            }
        }
        .designation {
            position: relative;
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #717070;
            z-index: 2;
            line-height: 1em;
            letter-spacing: .1em;
            font-family: var(--title-font);
            text-transform: uppercase;
            transition: all 400ms ease;
        }
    }

    .share-icon {
        position: absolute;
        right: 2px;
        bottom: 0px;
        height: 60px;
        width: 60px;
        line-height: 60px;
        text-align: center;
        font-size: 18px;
        color: var(--theme-color1);
        border-top: 1px solid #eee;
        background-color: #ffffff;
        transition: all 300ms ease;
        z-index: 3;
    }

    .social-links {
        position: absolute;
        right: 2px;
        bottom: 60px;
        padding: 15px 0;
        margin-top: 5px;
        display: flex;
        align-items: center;
        flex-direction: column;
        background: #ffffff;
        transform: scaleY(0);
        transform-origin: bottom;
        z-index: 3;
        visibility: hidden;
        opacity: 0;
        transition: all 400ms ease;
        a {
            position: relative;
            height: 34px;
            width: 60px;
            display: block;
            font-size: 14px;
            line-height: 34px;
            text-align: center;
            color: var(--theme-color1);
            transition: all 300ms ease;

            &:hover {
                color: var(--theme-color2);
            }
        }
    }
}
/*  Home 5 About Section  */
.tm-sc-funfact-home5 {
  background-color: #FFFFFF;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  bottom: 0;
  height: 105px;
  padding: 15px 40px;
  position: absolute;
  right: 0;
  top: 500px;
  width: 230px;
}
.dark-color{
  color: #18191c !important;
}
.home5-about-img .at-home8-2 {
  position: absolute;
  right: 0;
  top: 100px;
  z-index: 1;
}
.home5-about-img .img-2 {
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
}
.home5-about-img .project {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 1.7px;
  line-height: 21px;
  position: absolute;
  right: -70px;
  text-transform: uppercase;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  top: 250px;
}
/*  Home 5 Services  */
.service-item-home5 {
  .inner-box {
    margin-bottom: 30px;
    position: relative;
    padding: 30px 25px;
    margin-top: 90px;
    background-color: #ffffff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    text-align: center;
    -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
    box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
    @include transition(all 300ms linear);
    &:before {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      background-color: #323232;
      content: "";
      @include transition(all 300ms linear);
    }
    .service-icon-area {
      position: relative;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: -90px;
      .icon {
        height: 110px;
        width: 110px;
        font-size: 58px;
        background-color: var(--theme-color1);
        line-height: 120px;
        color: var(--text-color-bg-theme-color1);
        border-radius: 50%;
        margin: 0 auto 25px;
        position: relative;
        overflow: hidden;
        @include transition(all 200ms linear);
        z-index: 1;
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          content: "";
          border-radius: 50%;
          z-index: -1;
          transform: scale(0);
          transform-origin: center;
          transform-style: preserve-3d;
          transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
          background-color: var(--theme-color2);
          color: var(--text-color-bg-theme-color2);
        }
      }
    }
    .title {
      position: relative;
      margin-bottom: 15px;
      color: #fff;
    }
    .text {
      position: relative;
      font-size: 14px;
      line-height: 24px;
      color: #797582;
      font-weight: 500;
      @include transition(all 200ms linear);
    }
    .btn-view-details {
      margin-top: 25px;
      position: relative;
    }
  }
  &:hover {
    .title {
      color: #333;
    }
    .inner-box {
      transform: translateY(-10px);
      &:before {
        height: 0;
        .service-icon-area {
          background-color: var(--theme-color2);
          color: var(--text-color-bg-theme-color2);
          -webkit-transition-delay: 150ms;
          transition-delay: 150ms;
        }
      }
      .service-icon-area {
        .icon {
          color: #fff;
          &:before {
            transform: scaleX(1);
          }
        }
      }
      .text {
        color: #8c8f94;
      }
    }
  }
}

/*  Home 5 Feature Block  */
.features-section-home5{
    position: relative;
    padding: 150px 0 120px;
    z-index: 2;
    &.pull-up{
        margin-top: -120px;
    }
    &:before{
        @include overlay;
        background-color: var(--bg-theme-color1);
        background-image: url(../images/icons/bg-pattern.png);
        background-repeat: no-repeat;
        background-size: cover;
        content: "";
    }
    .bottom-box{
        position: relative;
        text-align: center;
        color: #fff;
        font-size: 18px;
        line-height: 30px;
        .theme-btn{
            font-size: 12px;
            padding: 5px 20px;
            margin-left: 30px;
        }
        @include for-sm {
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 24px;
            .theme-btn{
                margin-left: 0;
                margin-top: 20px;
            }
        }
    }
}
.feature-block-home5{
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
  .inner-box {
      position: relative;
      overflow: hidden;
      height: 100%;
      padding: 35px 40px 10px;
      background-color: #171717;
      z-index: 2;
      transition: all 300ms ease;
      .content{
          display: flex;
          align-items: center;
          justify-content: space-between;
      }        

      &:hover {
          .title,
          .text{
              color: var(--theme-color1);
          }
          .icon {
              color: #ffffff;
              transform: scaleX(-1);
          }
      }
  }
  .title {
    position: relative;
    padding-bottom: 30px;
    margin-bottom: 0px;
    color: #ffffff;
    &:hover {
        color: var(--theme-color1);
    }
  }
  .icon {
    position: relative;
    display: block;
    font-size: 62px;
    color: var(--theme-color1);
    font-weight: 400;
    line-height: 1em;
    transition: all 200ms linear;
    margin-bottom: 14px;
  }
  .read-more{
    position: relative;
    display: block;
    color: #ffffff;
    font-size: 16px;
    line-height: 30px;
    text-align: center;
    font-weight: 600;
    border-top: 3px solid rgba(255, 255, 255, .1);
    padding: 10px 30px 2px;
    letter-spacing: .1em;
    text-transform: uppercase;
    @include title-font;
    transition: all 300ms linear;
  }
}

.testimonial-section-home5 {
    position: relative;
    padding: 120px 0 170px;
    @include for-lg{
        padding: 120px 0;
    }
    .bg{
        &::before{
            @include overlay;
            background-color: var(--bg-theme-color2);
            opacity: .8;
            content: "";
        }
    }
    .testimonials {
        position: relative;
        &::before{
            position: absolute;
            left: -75px;
            top: -120px;
            height: 100px;
            width: 128px;
            background-image: url(../images/icons/shape-1.png);
            content: "";
        }
    }
}
.testimonial-block-home5 {
    position: relative;
    .inner-box{
        position: relative;
        padding-left: 230px;
        @include for-md{
            padding-left: 0;
            text-align: center;
        }
    }
    .text {
        position: relative;
        font-size: 40px;
        line-height: 50px;
        color: #ffffff;
        font-weight: 500;
        margin-bottom: 25px;
        @include for-lg{
            font-size: 32px;
        }
        @include for-sm{
            font-size: 24px;
            line-height: 1.4em;
        }
    }
    .name {
        color: var(--theme-color1);
        margin-top: 70px;
        margin-bottom: 0;
        @include for-lg{
            margin-top: 40px;
        }
        @include for-md{
            margin-top: 20px;
        }
    }

    .image-box{
        position: absolute;
        left: 0;
        top: 10px;
        width: 200px;
        text-align: center;
        z-index: 2;
        @include for-md{
            position: relative;
            display: inline-block;
            top: 0;
        }
    }

    .image {
        position: relative;
        display: inline-block;
        height: 138px;
        width: 138px;
        border-radius: 50%;
        padding: 5px;
        border: 2px solid var(--theme-color1);
        border-radius: 50%;
    }
}
.testimonial-thumbs-home5 {
    position: absolute;
    left: 0px;
    top: 160px;
    width: 200px;
    @include for-md{
        position: relative;
        top: 0;
        margin-top: 50px;
    }
    .testimonial-thumb {
        position: relative;
        cursor: pointer;
        z-index: 9;
        .image {
            position: relative;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            overflow: hidden;
            transition: all 300ms ease;
            img {
                height: 100%;
                width: 100%;
                border-radius: 50%;
                transition: all 300ms ease;
            }
        }
        &.swiper-slide-thumb-active .image {
            background-color: var(--bg-theme-color2);
            img{
                opacity: .7;
            }
        }
    }
}
.testimonial-content-home5 .swiper-slide:not(.swiper-slide-active) {
  opacity: 0 !important;
}

.testimonial-content-home5 .testimonial-block-home5.swiper-slide-active .image-column .image {
  transform: translate(0);
  opacity: 1;
  transition: all 1000ms ease;
}


.features-section-home5-v2 {
  position: relative;
  background-color: var(--bg-theme-color2);
  .features-column{
    .inner-column {
      position: relative;
      padding: 100px 0;
      height: 100%;
    }
    .bg-image {
      position: absolute;
      left: -360px;
      right: 0;
      top: 0;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      &:before {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: linear-gradient(90deg, #171717 0%, #E8A133 100%);
        opacity: 0.9;
        content: "";
      }
    }
  }
  .content-column {
    position: relative;
    padding: 100px 0;
    .inner-column {
      padding-left: 90px;
      @include for-md{
        padding-left: 0;
      }
    }
    .image-box {
      position: relative;
      margin-top: 60px;
    }
    .image {
      margin-bottom: 0;
    }
    img {
      height: 200px;
      -o-object-fit: cover;
       object-fit: cover;
       width: 100%;
    }
    .icon {
      position: absolute;
      right: 0;
      top: -55px;
      height: 130px;
      width: 130px;
      color: #ffffff;
      font-size: 64px;
      background-color: var(--theme-color2);
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      &:before {
        height: 100px;
        width: 100px;
        background-color: var(--bg-theme-color3);
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
      }
    }
  }
}
.feature-block-home5-v2 {
  position: relative;
  &:last-child {
    .inner-box {
      padding-bottom: 0;
      border-bottom: 0;
      margin-bottom: 0;
      min-height: 110px;
    }
  }
  .inner-box {
    position: relative;
    min-height: 160px;
    padding-top: 10px;
    padding-left: 150px;
    padding-bottom: 50px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 50px;
    -webkit-transition: all 300ms ease;
    transition: all 300ms ease;
      @include for-sm{
        padding-left: 0;
      }
    &:hover{
      .icon {
        background-color: var(--bg-theme-color1);
        color: #fff;
        -webkit-transform: rotate(180deg) scale(-1);
        transform: rotate(180deg) scale(-1);
      }
    }
    .icon {
      position: absolute;
      left: 0;
      top: 0;
      height: 110px;
      width: 110px;
      background-color: #ffffff;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      font-size: 64px;
      color: var(--theme-color2);
      border-radius: 50%;
      -webkit-transition: all 300ms ease;
      transition: all 300ms ease;
      @include for-sm{
        margin-bottom: 25px;
        position: relative;
      }
    }
  }
  .title {
    color: #ffffff;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .text {
    max-width: 200px;
    color: #ffdbda;
  }
}

/*  Home 5 Testimonials  */
.testimonial-section-home5-v2{
  position: relative;
  padding: 120px 0 130px;
  .sec-title{
    padding-right: 10px;
  }
}
.testimonial-block-home5-v2{
  position: relative;
  .inner-box {
    position: relative;
    margin-top: 35px;
    &:hover{
      .content-box{
        background-color: var(--bg-theme-color2);
        &:before{
          border-top: 20px solid var(--theme-color1);
        }
        .text{
          color: #ffffff;
        }
      }
    }
  }
  .content-box{
    position: relative;
    display: block;
    background-color: #ffffff;
    padding: 0px 40px 60px;
    box-shadow: 0 0 15px rgba(0,0,0,.05);
    transition: all 300ms ease;
    &:before{
      position: absolute;
      left: 0;
      top: 100%;
      border-top: 20px solid #ffffff;
      border-left: 55px solid transparent;
      transition: all 300ms ease;
      content: "";
    }
  }
  .thumb {
    position: relative;
    display: inline-block;
    height: 69px;
    width: 69px;
    margin-top: -35px;
    border-radius: 50%;
    padding: 5px;
    background-color: #ffffff;
    border: 2px solid var(--theme-color1);
    margin-bottom: 10px;
    img {
      width: 100%;
      border-radius: 50%;
      transition: all 300ms ease;
    }
  }
  .rating{
    font-size: 14px;
    color: var(--theme-color1);
    letter-spacing: 1px;
    margin-bottom: 3px;
  }
  .text {
    position: relative;
    margin-bottom: 0;
    transition: all 300ms ease;
  }
  .info-box{
    position: relative;
    padding:20px 10px 0;
    margin-left: 55px;
    .name {
      margin-bottom: 0;
    }
    .designation {
      position: relative;
      text-transform: uppercase;
      display: block;
      font-size: 11px;
      color: #808287;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: .1em;
    }
  }
}

/*  Contact Section Home 5  */

.contact-section-home5{
  position: relative;
  overflow: hidden;
  padding: 120px 0 70px;
  .bg-image{
    position: absolute;
    right: 60px;
    bottom: 0;
    @include for-xxl{
      right: -60px;
    }
    @include for-xl{
      display: none;
    }
  }
  .sec-title{
    margin-bottom: 40px;
  }
  .title-column{
    position: relative;
    margin-bottom: 50px;
  }
  .form-column{
    margin-bottom: 50px;
    .inner-column{
      padding-right: 130px;
      margin-left: -30px;
      @include for-lg{
        padding-right: 0;
        margin-left: 0;
      }
    }
  }
  &:before {
    background-color: #fff;
    bottom: 0;
    content: "";
    position: absolute;
    right: 0;
    width: 58%;
    top: -50px;
  }
}
// Contact Info Block
.contact-info-block{
    position: relative;
    margin-bottom: 20px;
    .inner{
        position: relative;
        padding-left: 110px;
        min-height: 80px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @include for-xs{
            padding-left: 70px;
            min-height: auto;
        }
        &:hover{
           .icon{
                background-color: var(--bg-theme-color1);
                border-radius: 50%;
           }
        }
    }
    .icon{
        position: absolute;
        left: 0;
        top: 0;
        height: 80px;
        width: 80px;
        background-color: var(--bg-theme-color2);
        color: #fff;
        font-size: 24px;
        line-height: 1em;
        @include flex-center;
        text-align: center;
        transition: all 300ms ease;
        @include for-xs{
            height: 50px;
            width: 50px;
            font-size: 20px;
        }
    }
    .title{
        font-size: 16px;
        text-transform: uppercase;
        color: #717070;
        font-weight: 500;
        margin-bottom: 0;
    }
    .text{
        font-size: 20px;
        line-height: 30px;
        color: #171717;
        font-weight: 500;
        a{
            color: inherit;
            font-weight: inherit;
            &:hover{
                color: var(--theme-color2);
            }
        }
    }
}
.contact-form-home5 {
  border-top: 5px solid var(--theme-color2);
  margin-right: 50px;
  margin-top: -50px;
}
.contact-form-home5 {
  position: relative;
  background-color: #ffffff;
  padding: 50px 60px 50px;
  box-shadow: 0 10px 60px rgba(0,0,0, .07);
  @include for-md{
    padding: 50px 40px;
  }
  @include for-sm{
    padding: 40px 20px;
  }
  &::before{
    position: absolute;
    right: 30px;
    bottom: 30px;
    height: 414px;
    width: 340px;
    border-bottom: 5px solid;
    border-image: linear-gradient(to left, var(--theme-color1), transparent) 1;
    z-index: 2;
    pointer-events: none;
    content: "";
    @include for-md{
      display: none;
    }
  }
  &:after{
    position: absolute;
    right: 30px;
    bottom: 30px;
    height: 414px;
    width: 340px;
    pointer-events: none;
    border-right: 5px solid;
    border-image: linear-gradient( transparent, var(--border-theme-color2)) 1;
    z-index: 2;
    content: "";
    @include for-md {
      display: none;
    }
  }
  .title{
    font-size: 40px;
    text-align: center;
    margin-bottom: 20px;
  }
  .form-group {
    position: relative;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    label {
      font-size: 16px;
      line-height: 20px;
      color: #ffffff;
      font-weight: 500;
      display: block;
      letter-spacing: 1px;
      margin-bottom: 10px;
      &.error {
        display: block;
        font-weight: 500;
        font-size: 12px;
        text-transform: capitalize;
        line-height: 24px;
        color: #ff0000;
        margin-bottom: 0;
      }
    }
    .select2-container--default .select2-selection--single,
    input:not([type="submit"]),
    textarea,
    select {
      position: relative;
      display: block;
      width: 100%;
      height: 60px;
      padding: 15px 30px;
      line-height: 30px;
      font-size: 14px;
      color: #717070;
      font-weight: 500;
      background-color: #f6f6f6;
      border: 1px solid transparent;
      border-radius: 5px;
      margin-bottom: 0;
      transition: all 300ms ease;
    }
    ::-webkit-input-placeholder {color: #6a6a6a;}
    ::-moz-input-placeholder {color: #8c8f94;}
    ::-ms-input-placeholder {color: #8c8f94;}
    input:focus,
    select:focus,
    textarea:focus {
      border-color: var(--border-theme-color2);
    }
    textarea {
      height: 170px;
      resize: none;
    }
    input[type="submit"],
    button {
      margin-top: 0px;
      line-height: 30px;
      padding: 15px 50px;
    }
  }
}
.home5-contact-img {
  background-image: url(../images/background/bg4.jpg);
  background-position: center center;
  background-size: cover;
  height: 594px;
  max-width: 194px;
  width: 100%;
}
.work-block-home5 {
  .icon{
    -webkit-box-align: center;
    -ms-flex-align: center;
    background-color: var(--bg-theme-color2);
    border-radius: 50%;
    font-size: 24px;
    color: var(--theme-color1);
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 60px;
    left: 0;
    position: absolute;
    top: 0;
    width: 60px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-transition: all 300ms ease;
    transition: all 300ms ease;
  }
  .inner-box{
    border-bottom: 1px solid #e2e0e5;
    margin-bottom: 35px;
    padding-left: 80px;
    padding-bottom: 35px;
    position: relative;
    @include for-md{
      padding-left: 0;
      text-align: left;
    }
    &:hover{
      .icon{
        background-color: var(--bg-theme-color1);
        color: var(--theme-color2);
        -webkit-transform: scale(-1) rotate(-180deg);
        transform: scale(-1) rotate(-180deg);
      }
    }
  }
}

.why-choose-us-home5{
  position: relative;
  &:before{
    background-image: url(../images/icons/icon-dots-3.png);
    background-position: center right;
    content: "";
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    width: 50%;
  }
  .content-column {
    position: relative;
    z-index: 1;
    margin-bottom: 50px;
    .inner-column {
      position: relative;
    }
    .sec-title{
      margin-bottom: 40px;
    }
    .feature-box{
      position: relative;
      padding-left: 100px;
      padding-top: 5px;
      min-height: 70px;
      margin-bottom: 30px;
      &:hover{
        .icon{
          color: var(--theme-color1);
          &:after{
            background-color: #ebebeb;
          }
        }
      }
      .icon{
        position: absolute;
        left: 0;
        top: 0;
        font-size: 64px;
        line-height: 1em;
        color: var(--theme-color2);
        transition: all 300ms ease;
        &:after{
          position: absolute;
          left: 25px;
          top: 15px;
          height: 54px;
          width: 54px;
          background-color: var(--bg-theme-color1);
          border-radius: 50%;
          z-index: -2;
          transition: all 300ms ease;
          content: "";
        }
      }
    }
    .theme-btn{
      margin-top: 20px;
    }
  }
  .image-column {
    position: relative;
    margin-bottom: 50px;
    @include for-lg{
      display: none;
    }
    .image-box{
      position: relative;
      margin-left: -24px;
    }    
    .image{
      position: relative;
      display: block;
      background-color: var(--bg-theme-color1);
      margin-bottom: 0;
      img {
        width: 100%;
      }
      &:before{
        background-color: rgba(8, 10, 11, 0.6);
        background-image: url(../images/icons/shape-2.png);
        background-position: center;  
        background-size: cover;
        content: "";
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
      }
    }
    .inner-column {
      position: relative;
      margin-left: -375px;
      padding-right: 100px;
    }
  }
  .info-column{
    @include for-lg{
      width: 100%;
    }
    .inner-column{
      position: relative;
      padding-left: 36px;
      @include for-lg{
        padding-left: 0;
      }
    }

    .info{
      position: relative;
      padding-bottom: 30px;
      border-bottom: 5px solid var(--bg-theme-color2);
      margin-bottom: 40px;
      &:last-child{
        padding-bottom: 0;
        margin-bottom: 0;
        border-bottom: 0;
      }
      .title{
        position: relative;
        top: -5px;
        margin-bottom: 5px;
        @include for-md{
          br{
            display: none;
          }
        }
      }
    }
    .info-box{
      position: relative;
      padding: 30px 50px;
      background-color: var(--bg-theme-color2);
      margin-left: -60px;
      margin-right: 70px;
      margin-top: 30px;
      @include for-lg{
        margin-left: 0;
        margin-right: 0;
      }
      @include for-md{
        padding: 20px 30px;
      }
      &:before{
        position: absolute;
        left: -35px;
        bottom: 0;
        border-right: 35px solid var(--border-theme-color2);
        border-top: 45px solid transparent;
        content: "";
        @include for-lg{
          display: none;
        }
      }
      .icon{
        display: block;
        font-size: 28px;
        color: #fff;
        margin-bottom: 8px;
      }
      .title{
        margin-bottom: 0;
        color: #fff;
        @include for-md{
          br{
            display: none;
          }
        }
      }
    }
  }
}
/*  News Block Home 6  */

.news-block-home6 {
    position: relative;
    margin-bottom: 50px;

    .inner-box {
        position: relative;
        transition: all 300ms ease;
        &:hover {
            .read-more{
                background-color: var(--bg-theme-color2);
                color: #ffffff;
            }
            .image-box .image {
                a:after {
                    left: 0;
                    right: 0;
                    opacity: 0;
                    transition: all 400ms linear;
                }
                img {
                    transform: scale(1.1);
                }
            }
        }
    }

    .image-box {
        position: relative;

        .image {
            position: relative;
            overflow: hidden;
            margin-bottom: 0;

            img {
                display: block;
                width: 100%;
                transition: all 400ms ease;
            }

            a:after {
                background: rgba(255, 255, 255, .3);
                bottom: 0;
                content: "";
                left: 50%;
                position: absolute;
                right: 51%;
                top: 0;
                opacity: 1;
                pointer-events: none;
                transition: all 400ms linear;
            }
        }

        .date {
            position: absolute;
            left: 20px;
            top: 0px;
            background: var(--theme-color1);
            color: #ffffff;
            z-index: 1;
            @include flex-center;
            height: 60px;
            width: 60px;
            text-align: center;
            font-size: 16px;
            line-height: 1.2em;
            font-weight: 500;
            text-transform: uppercase;
            font-family: var(--title-font);
        }
    }

    .lower-content {
        position: relative;
        margin-left: 20px;
        margin-right: 20px;
        padding: 0px 10px 10px;
        margin-top: -32px;
        z-index: 2;
        background-color: #ffffff;
        box-shadow: 0 10px 60px rgba(0,0,0, .05);
        border-top: 4px solid var(--theme-color1);
        text-align: center;
    }

    .author-thumb {
        position: relative;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        margin-top: -30px;
        overflow: hidden;
        border: 2px solid var(--theme-color1);
        display: inline-block;
        margin-bottom: 5px;
    }

    .post-info {
        position: relative;
        display: flex;
        justify-content: center;
        margin-bottom: 5px;
        li {
            position: relative;
            font-size: 14px;
            line-height: 25px;
            color: var(--theme-color2);
            font-weight: 500;
            letter-spacing: .1em;
            text-transform: uppercase;
            font-family: var(--title-font);
            margin: 0 5px;
            i {
                margin-right: 5px;
                color: var(--theme-color1);
                font-size: 14px;
            }
        }
    }

    .title {
        padding: 0 15px;
        line-height: 1.4em;
        font-weight: 600;
        &:hover {
            color: var(--theme-color1);
        }
    }

    .read-more {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        line-height: 25px;
        padding: 10px 25px;
        text-transform: uppercase;
        font-weight: 600;
        color: #717070;
        font-family: var(--title-font);
        background-color: #f6f6f6;
        letter-spacing: .1em;
        border-radius: 3px;
        transition: all 300ms ease;
    }
}
/* Call To Action At Home 6  */
.call-to-action-home6 {
  position: relative;
  padding: 120px 0;
  z-index: 2;
  .outer-box {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #0F0F0F;
    overflow: hidden;
    &:before {
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: 50%;
      background-image: url(../images/icons/bg-pattern1.png);
      background-position: center right;
      background-repeat: no-repeat;
      content: "";
    }
  }
  .content-box {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
    padding: 80px;
    @include for-md{
      display: block;
        padding: 40px 30px;
    }
  }
}