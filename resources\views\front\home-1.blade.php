@extends('front.layouts.master')

@section('seo_title', $global_setting->home_seo_title)
@section('seo_meta_description', $global_setting->home_seo_meta_description)

@section('content')
<style>
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .animation-delay-2000 {
        animation-delay: 2s;
    }

    .animation-delay-4000 {
        animation-delay: 4s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .float-animation {
        animation: float 3s ease-in-out infinite;
    }

    /* Custom Flaticon Styles */
    .flaticon-web-development:before,
    .flaticon-design:before,
    .flaticon-targeted-marketing:before,
    .flaticon-diplomat:before,
    .flaticon-teaching:before,
    .flaticon-laptop:before,
    .flaticon-health-check:before,
    .flaticon-bank:before,
    .flaticon-success:before,
    .flaticon-recommend:before,
    .flaticon-marketing:before,
    .flaticon-job-promotion:before,
    .flaticon-completed-task:before,
    .flaticon-settings:before,
    .flaticon-rating:before,
    .flaticon-group:before,
    .flaticon-learning:before,
    .flaticon-cloud:before,
    .flaticon-digital-services:before,
    .flaticon-graphic-design:before,
    .flaticon-technology:before,
    .flaticon-phone-call:before,
    .flaticon-visitor:before,
    .flaticon-promotion:before {
        font-size: inherit !important;
        line-height: 1;
    }

    /* Icon hover effects */
    .service-icon-container:hover .service-icon {
        transform: scale(1.1) rotate(5deg);
    }
</style>

<!-- Hero Section -->
<section class="min-h-screen bg-gray-100" id="aboutUs">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl">
                @if($sliders->count() > 0 && $sliders->first()->text != '')
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($sliders->first()->text))) !!}
                @else
                    TBL TECH شركة متخصصة في
                    <div class="relative inline-flex">
                        <span class="absolute inset-x-0 bottom-0 border-b-[30px] border-purple-500"></span>
                        <h1 class="relative text-4xl font-bold text-black sm:text-6xl lg:text-7xl">
                            هندسة البرمجيات.
                        </h1>
                    </div>
                @endif
            </h1>

            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                @if($welcome_one_items && $welcome_one_items->text)
                    {{ $welcome_one_items->text }}
                @else
                    نقدم حلولًا مبتكرة في تطوير الأنظمة وتصميم وبرمجة المواقع الإلكترونية والتطبيقات والمتاجر الإلكترونية.
                @endif
            </p>

            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                نلتزم دائمآ بتقديم خدمات عالية الجودة تلبي احتياجات العملاء، مع التركيز على تقديم أفضل أداء، و أعلى مستوى من الأمان والحماية ، ونعمل دومآ على تحسين تجربة المستخدم لتوفير حلول تقنية تدعم نمو أعمالهم وتحقق أهدافهم الرقمية.
            </p>

            @if($sliders->count() > 0 && $sliders->first()->button_text != '')
                <a href="{{ $sliders->first()->button_url }}" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    {{ $sliders->first()->button_text }}
                </a>
            @else
                <a href="#contact" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    اطلب موقعك الان
                </a>
            @endif
        </div>
        <div class="lg:mt-0 mt-10 lg:col-span-5 lg:flex">
            @if($sliders->count() > 0 && $sliders->first()->photo != '')
                <img src="{{ asset('uploads/'.$sliders->first()->photo) }}" alt="Hero Image" />
            @else
                <img src="https://cdn.rareblocks.xyz/collection/celebration/images/hero/1/hero-img.png" alt="Hero Image" />
            @endif
        </div>
    </div>
</section>
<!-- End Hero Section -->

<!-- Services Section -->
<section class="bg-gradient-to-r from-[#351735] to-[#101545]" id="service">
    <div class="text-right container grid min-h-screen px-6 py-12 mx-auto">
        <div>
            <h1 class="text-3xl font-semibold capitalize lg:text-4xl text-white">
                @if($home_1_page_items && $home_1_page_items->service_heading)
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_1_page_items->service_heading))) !!}
                @else
                    خدماتنا
                @endif
            </h1>
            <div class="">
                <span class="inline-block w-8/12 md:w-3/12 h-1 bg-purple-500 rounded-full"></span>
                <span class="inline-block w-3 h-1 ml-1 bg-purple-500 rounded-full"></span>
                <span class="inline-block w-1 h-1 ml-1 bg-purple-500 rounded-full"></span>
            </div>
        </div>

        <div class="min-h-full mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 py-20 gap-y-5 gap-x-6 content-center">
            @if($services->count() > 0)
                @php
                    $service_count = ($home_1_page_items && $home_1_page_items->service_how_many) ? $home_1_page_items->service_how_many : 4;
                    $gradients = [
                        'from-[#1a2338] to-[#12213c]',
                        'from-[#311633] to-[#12131e]',
                        'from-[#101625] to-[#12213c]',
                        'from-[#311633] to-[#12131e]'
                    ];
                    $icons = [
                        'img/web1.png',
                        'img/system1.png',
                        'img/app2.png',
                        'img/el1.png'
                    ];
                @endphp
                @foreach($services->take($service_count) as $index => $service)
                <div class="mb-12 lg:mb-0 shrink-0 w-120 relative">
                    @if(isset($icons[$index]) && file_exists(public_path('tbl-tech/'.$icons[$index])))
                        <img src="{{ asset('tbl-tech/'.$icons[$index]) }}" class="h-20 absolute top-[-50px] left-1/3" alt="" />
                    @elseif($service->photo)
                        <img src="{{ asset('uploads/'.$service->photo) }}" class="h-20 absolute top-[-50px] left-1/3 object-contain" alt="" />
                    @endif

                    <div class="rounded-lg shadow-lg h-full block bg-gradient-to-b {{ $gradients[$index % count($gradients)] }}">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-300 mb-4 mt-10">
                                {{ $service->name }}
                            </h3>

                            <div class="text-lg font-medium mb-4 space-y-1">
                                <div class="text-gray-400 text-sm">
                                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($service->short_description))) !!}
                                </div>
                            </div>
                            <div class="flex-card flex content-end justify-end">
                                <a class="text-white font-bold py-3 px-2 hover:text-purple-500 focus:text-purple-500" href="{{ route('service',$service->slug) }}">لمعرفة المزيد</a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            @else
                <!-- Default Services if none exist -->
                <div class="mb-12 lg:mb-0 shrink-0 w-120 relative">
                    <img src="{{ asset('tbl-tech/img/web1.png') }}" class="h-20 absolute top-[-50px] left-1/3" alt="" />
                    <div class="rounded-lg shadow-lg h-full block bg-gradient-to-b from-[#1a2338] to-[#12213c]">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-300 mb-4 mt-10">
                                تصميم وبرمجة المواقع الالكترونية
                            </h3>
                            <div class="text-lg font-medium mb-4 space-y-1">
                                <div class="text-gray-400 text-sm">
                                    تصميم الأسواق الالكترونية والمواقع التفاعلية بأحدث التقنيات والمعايير العالمية
                                </div>
                            </div>
                            <div class="flex-card flex content-end justify-end">
                                <a class="text-white font-bold py-3 px-2 hover:text-purple-500 focus:text-purple-500" href="{{ route('services') }}">لمعرفة المزيد</a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
<!-- End Services Section -->

<!-- Get Your System Section -->
@if($welcome_one_items)
<section class="relative py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div class="absolute top-20 right-0 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-0 left-1/3 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Content Column -->
            <div class="text-white space-y-8">
                <!-- Section Badge -->
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $welcome_one_items->subheading }}
                </div>

                <div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                        {{ $welcome_one_items->heading }}
                    </h2>
                    <p class="text-xl text-gray-200 leading-relaxed mb-8">
                        {{ $welcome_one_items->text }}
                    </p>
                </div>

                <!-- Features Grid -->
                @if($welcome_one_item_elements->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    @foreach($welcome_one_item_elements->take(4) as $element)
                    <div class="flex items-start space-x-4 group">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="{{ $element->icon }} text-white text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2">{{ $element->heading }}</h3>
                            <p class="text-gray-300 text-sm">{{ $element->text }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- CTA Button -->
                @if($welcome_one_items->button_text && $welcome_one_items->button_url)
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ $welcome_one_items->button_url }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl group">
                        <span>{{ $welcome_one_items->button_text }}</span>
                        <svg class="w-5 h-5 mr-3 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                    <a href="{{ route('contact') }}" class="inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-2xl hover:bg-white/20 transition-all duration-300 border border-white/20">
                        تواصل معنا
                    </a>
                </div>
                @endif
            </div>

            <!-- Image Column -->
            <div class="relative">
                @if($welcome_one_items->photo1)
                <div class="relative">
                    <!-- Main Image -->
                    <div class="relative z-10">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo1) }}" alt="نظام إدارة المشاريع" class="w-full h-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-purple-900/20 to-transparent rounded-2xl"></div>
                    </div>

                    <!-- Secondary Image -->
                    @if($welcome_one_items->photo2)
                    <div class="absolute -bottom-8 -left-8 z-20 w-48 h-48">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo2) }}" alt="تطبيق الهاتف" class="w-full h-full object-cover rounded-xl shadow-xl border-4 border-white/20 backdrop-blur-sm">
                    </div>
                    @endif

                    <!-- Floating Elements -->
                    <div class="absolute top-8 right-8 w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endif
<!-- End Get Your System Section -->

<!-- Pricing Section -->
<section class="py-20 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
                </svg>
                خطط الأسعار
            </div>
            <h2 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900 bg-clip-text text-transparent mb-6">
                اختر الخطة المناسبة لك
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                خطط مرنة ومناسبة لجميع أحجام الأعمال، ابدأ مجاناً واختر ما يناسب احتياجاتك
            </p>
        </div>

        <!-- Pricing Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Static Pricing Card 1 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">مجاني</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة التجريبية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">تجربة مجانية لمدة 14 يوم</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>جربه مجاناً</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Static Pricing Card 2 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 ring-2 ring-purple-500 scale-105 mt-8">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                    <div class="bg-gradient-to-r from-purple-500 to-blue-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                        الأكثر شعبية
                    </div>
                </div>
                <div class="p-8 pt-12 relative z-20">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">4400 ر.س</span>
                            <span class="text-gray-500 ml-2">/ شهرياً</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة الشهرية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">جميع المميزات متاحة</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>اشترك الآن</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Static Pricing Card 3 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">44800 ر.س</span>
                            <span class="text-gray-500 ml-2">/ سنوياً</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة السنوية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">وفر 20% مع الاشتراك السنوي</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>اشترك سنوياً</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">جميع الخطط تشمل دعم فني مجاني ومتابعة مستمرة</p>
            <a href="{{ route('pricing_plans') }}" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold">
                مقارنة تفصيلية للخطط
                <svg class="w-4 h-4 mr-2 transition-transform hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
<!-- End Pricing Section -->

@endsection
