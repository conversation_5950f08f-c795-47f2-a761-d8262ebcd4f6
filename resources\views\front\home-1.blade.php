@extends('front.layouts.master')

@section('seo_title', $global_setting->home_seo_title)
@section('seo_meta_description', $global_setting->home_seo_meta_description)

@section('content')
<style>
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .animation-delay-2000 {
        animation-delay: 2s;
    }

    .animation-delay-4000 {
        animation-delay: 4s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .float-animation {
        animation: float 3s ease-in-out infinite;
    }

    /* Custom Flaticon Styles */
    .flaticon-web-development:before,
    .flaticon-design:before,
    .flaticon-targeted-marketing:before,
    .flaticon-diplomat:before,
    .flaticon-teaching:before,
    .flaticon-laptop:before,
    .flaticon-health-check:before,
    .flaticon-bank:before,
    .flaticon-success:before,
    .flaticon-recommend:before,
    .flaticon-marketing:before,
    .flaticon-job-promotion:before,
    .flaticon-completed-task:before,
    .flaticon-settings:before,
    .flaticon-rating:before,
    .flaticon-group:before,
    .flaticon-learning:before,
    .flaticon-cloud:before,
    .flaticon-digital-services:before,
    .flaticon-graphic-design:before,
    .flaticon-technology:before,
    .flaticon-phone-call:before,
    .flaticon-visitor:before,
    .flaticon-promotion:before {
        font-size: inherit !important;
        line-height: 1;
    }

    /* Icon hover effects */
    .service-icon-container:hover .service-icon {
        transform: scale(1.1) rotate(5deg);
    }
</style>

<!-- Hero Section -->
<section class="min-h-screen bg-gray-100" id="aboutUs">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl">
                @if($sliders->count() > 0 && $sliders->first()->text != '')
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($sliders->first()->text))) !!}
                @else
                    {{ $global_setting->footer_text ?? 'TBL TECH شركة متخصصة في' }}
                    <div class="relative inline-flex">
                        <span class="absolute inset-x-0 bottom-0 border-b-[30px] border-purple-500"></span>
                        <h1 class="relative text-4xl font-bold text-black sm:text-6xl lg:text-7xl">
                            هندسة البرمجيات.
                        </h1>
                    </div>
                @endif
            </h1>

            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                @if($welcome_one_items && $welcome_one_items->text)
                    {{ $welcome_one_items->text }}
                @else
                    {{ $global_setting->footer_text ?? 'نقدم حلولًا مبتكرة في تطوير الأنظمة وتصميم وبرمجة المواقع الإلكترونية والتطبيقات والمتاجر الإلكترونية.' }}
                @endif
            </p>

            @if($welcome_one_items && $welcome_one_items->service_subheading)
            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                {{ $welcome_one_items->service_subheading }}
            </p>
            @endif

            @if($sliders->count() > 0 && $sliders->first()->button_text != '')
                <a href="{{ $sliders->first()->button_url }}" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    {{ $sliders->first()->button_text }}
                </a>
            @else
                <a href="#contact" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    {{ $global_other_page_items->page_contact_send_mail_subheading ?? 'اطلب موقعك الان' }}
                </a>
            @endif
        </div>
        <div class="lg:mt-0 mt-10 lg:col-span-5 lg:flex">
            @if($sliders->count() > 0 && $sliders->first()->photo != '')
                <img src="{{ asset('uploads/'.$sliders->first()->photo) }}" alt="Hero Image" />
            @else
                <img src="https://cdn.rareblocks.xyz/collection/celebration/images/hero/1/hero-img.png" alt="Hero Image" />
            @endif
        </div>
    </div>
</section>
<!-- End Hero Section -->

<!-- Services Section -->
<section class="bg-gradient-to-r from-[#351735] to-[#101545]" id="service">
    <div class="text-right container grid min-h-screen px-6 py-12 mx-auto">
        <div>
            <h1 class="text-3xl font-semibold capitalize lg:text-4xl text-white">
                @if($home_1_page_items && $home_1_page_items->service_heading)
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_1_page_items->service_heading))) !!}
                @else
                    {{ $global_other_page_items->page_services_title ?? 'خدماتنا' }}
                @endif
            </h1>
            <div class="">
                <span class="inline-block w-8/12 md:w-3/12 h-1 bg-purple-500 rounded-full"></span>
                <span class="inline-block w-3 h-1 ml-1 bg-purple-500 rounded-full"></span>
                <span class="inline-block w-1 h-1 ml-1 bg-purple-500 rounded-full"></span>
            </div>
        </div>

        <div class="min-h-full mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 py-20 gap-y-5 gap-x-6 content-center">
            @if($services->count() > 0)
                @php
                    $service_count = ($home_1_page_items && $home_1_page_items->service_how_many) ? $home_1_page_items->service_how_many : 4;
                    $gradients = [
                        'from-[#1a2338] to-[#12213c]',
                        'from-[#311633] to-[#12131e]',
                        'from-[#101625] to-[#12213c]',
                        'from-[#311633] to-[#12131e]'
                    ];
                    $icons = [
                        'img/web1.png',
                        'img/system1.png',
                        'img/app2.png',
                        'img/el1.png'
                    ];
                @endphp
                @foreach($services->take($service_count) as $index => $service)
                <div class="mb-12 lg:mb-0 shrink-0 w-120 relative">
                    @if(isset($icons[$index]) && file_exists(public_path('tbl-tech/'.$icons[$index])))
                        <img src="{{ asset('tbl-tech/'.$icons[$index]) }}" class="h-20 absolute top-[-50px] left-1/3" alt="" />
                    @elseif($service->photo)
                        <img src="{{ asset('uploads/'.$service->photo) }}" class="h-20 absolute top-[-50px] left-1/3 object-contain" alt="" />
                    @endif

                    <div class="rounded-lg shadow-lg h-full block bg-gradient-to-b {{ $gradients[$index % count($gradients)] }}">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-300 mb-4 mt-10">
                                {{ $service->name }}
                            </h3>

                            <div class="text-lg font-medium mb-4 space-y-1">
                                <div class="text-gray-400 text-sm">
                                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($service->short_description))) !!}
                                </div>
                            </div>
                            <div class="flex-card flex content-end justify-end">
                                <a class="text-white font-bold py-3 px-2 hover:text-purple-500 focus:text-purple-500" href="{{ route('service',$service->slug) }}">لمعرفة المزيد</a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            @else
                <!-- Default Services if none exist -->
                <div class="mb-12 lg:mb-0 shrink-0 w-120 relative">
                    <img src="{{ asset('tbl-tech/img/web1.png') }}" class="h-20 absolute top-[-50px] left-1/3" alt="" />
                    <div class="rounded-lg shadow-lg h-full block bg-gradient-to-b from-[#1a2338] to-[#12213c]">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-300 mb-4 mt-10">
                                تصميم وبرمجة المواقع الالكترونية
                            </h3>
                            <div class="text-lg font-medium mb-4 space-y-1">
                                <div class="text-gray-400 text-sm">
                                    تصميم الأسواق الالكترونية والمواقع التفاعلية بأحدث التقنيات والمعايير العالمية
                                </div>
                            </div>
                            <div class="flex-card flex content-end justify-end">
                                <a class="text-white font-bold py-3 px-2 hover:text-purple-500 focus:text-purple-500" href="{{ route('services') }}">لمعرفة المزيد</a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
<!-- End Services Section -->

<!-- Get Your System Section -->
@if($welcome_one_items)
<section class="relative py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div class="absolute top-20 right-0 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-0 left-1/3 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Content Column -->
            <div class="text-white space-y-8">
                <!-- Section Badge -->
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $welcome_one_items->subheading }}
                </div>

                <div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                        {{ $welcome_one_items->heading }}
                    </h2>
                    <p class="text-xl text-gray-200 leading-relaxed mb-8">
                        {{ $welcome_one_items->text }}
                    </p>
                </div>

                <!-- Features Grid -->
                @if($welcome_one_item_elements->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    @foreach($welcome_one_item_elements->take(4) as $element)
                    <div class="flex items-start space-x-4 group">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="{{ $element->icon }} text-white text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2">{{ $element->heading }}</h3>
                            <p class="text-gray-300 text-sm">{{ $element->text }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- CTA Button -->
                @if($welcome_one_items->button_text && $welcome_one_items->button_url)
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ $welcome_one_items->button_url }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl group">
                        <span>{{ $welcome_one_items->button_text }}</span>
                        <svg class="w-5 h-5 mr-3 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                    <a href="{{ route('contact') }}" class="inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-2xl hover:bg-white/20 transition-all duration-300 border border-white/20">
                        تواصل معنا
                    </a>
                </div>
                @endif
            </div>

            <!-- Image Column -->
            <div class="relative">
                @if($welcome_one_items->photo1)
                <div class="relative">
                    <!-- Main Image -->
                    <div class="relative z-10">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo1) }}" alt="نظام إدارة المشاريع" class="w-full h-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-purple-900/20 to-transparent rounded-2xl"></div>
                    </div>

                    <!-- Secondary Image -->
                    @if($welcome_one_items->photo2)
                    <div class="absolute -bottom-8 -left-8 z-20 w-48 h-48">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo2) }}" alt="تطبيق الهاتف" class="w-full h-full object-cover rounded-xl shadow-xl border-4 border-white/20 backdrop-blur-sm">
                    </div>
                    @endif

                    <!-- Floating Elements -->
                    <div class="absolute top-8 right-8 w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endif
<!-- End Get Your System Section -->

<!-- Contact Section -->
<section class="min-h-screen bg-gray-900 sm:py-16 lg:py-24" id="contact_us">
    <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:items-stretch md:grid-cols-2 gap-x-12 lg:gap-x-20 gap-y-10">
            <div class="flex flex-col justify-between py-10 lg:py-5">
                <div>
                    <h2 class="text-3xl sm:pt-10 font-bold leading-tight text-white sm:text-4xl lg:leading-tight lg:text-5xl">
                        {{ $global_other_page_items->page_contact_title ?? 'تواصل معنا' }}
                    </h2>
                    <p class="max-w-xl mx-auto mt-4 text-base sm:pt-10 leading-relaxed text-white">
                        {{ $global_other_page_items->page_contact_info_text ?? 'للمزيد من المعلومات وعروض الاسعار تواصل معنا على' }}
                    </p>

                    <img class="relative z-10 max-w-xs mx-auto -mb-16 md:hidden"
                        src="https://cdn.rareblocks.xyz/collection/celebration/images/contact/4/curve-line-mobile.svg" alt="" />

                    <img class="hidden w-full translate-x-24 translate-y-8 md:block"
                        src="https://cdn.rareblocks.xyz/collection/celebration/images/contact/4/curve-line.svg" alt="" />
                </div>

                <div class="hidden md:mt-auto md:block">
                    <div class="flex items-center">
                        @for($i = 0; $i < 5; $i++)
                        <svg class="w-6 h-6 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        @endfor
                    </div>

                    <div class="flex items-center mt-8">
                        <!-- Whatsapp -->
                        <a href="">
                            <svg class="h-7 w-7" fill="currentColor" style="color: #128c7e" viewBox="0 0 24 24">
                                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z" />
                            </svg>
                        </a>
                        <div class="mr-10">
                            <a href="#" class="text-base font-semibold text-white">
                                {{ $global_setting->phone ?? '779500091 976+' }}
                            </a>
                        </div>
                    </div>

                    <div class="flex items-center mt-4">
                        <a href="">
                            <svg fill="white" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                            </svg>
                        </a>
                        <div class="mr-10">
                            <a href="#" class="text-base font-semibold text-white">
                                {{ $global_setting->phone ?? '779500091 976+' }}
                            </a>
                        </div>
                    </div>

                    <div class="flex items-center mt-4">
                        <a href="">
                            <svg width="28" height="19" viewBox="0 0 28 19" class="fill-current hover:scale-125 transition-transform">
                                <path fill="#c71610" d="M25.3636 0H2.63636C1.18182 0 0 1.16785 0 2.6052V16.3948C0 17.8322 1.18182 19 2.63636 19H25.3636C26.8182 19 28 17.8322 28 16.3948V2.6052C28 1.16785 26.8182 0 25.3636 0ZM25.3636 1.5721C25.5909 1.5721 25.7727 1.61702 25.9545 1.75177L14.6364 8.53428C14.2273 8.75886 13.7727 8.75886 13.3636 8.53428L2.04545 1.75177C2.22727 1.66194 2.40909 1.5721 2.63636 1.5721H25.3636ZM25.3636 17.383H2.63636C2.09091 17.383 1.59091 16.9338 1.59091 16.3499V3.32388L12.5 9.8818C12.9545 10.1513 13.4545 10.2861 13.9545 10.2861C14.4545 10.2861 14.9545 10.1513 15.4091 9.8818L26.3182 3.32388V16.3499C26.4091 16.9338 25.9091 17.383 25.3636 17.383Z" />
                            </svg>
                        </a>
                        <div class="mr-10">
                            <a href="#" class="text-base font-semibold text-white">
                                {{ $global_setting->email ?? '<EMAIL>' }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:pl-12">
                <div class="overflow-hidden bg-white rounded-md">
                    <div class="p-6 sm:p-10">
                        <h3 class="text-3xl font-semibold text-black">
                            {{ $global_other_page_items->page_contact_send_mail_heading ?? 'احصل على عرض سعر' }}
                        </h3>

                        <form action="{{ route('contact_send_message') }}" method="POST" class="mt-4">
                            @csrf
                            <div class="space-y-6">
                                <div>
                                    <label for="name" class="text-base font-medium text-gray-900">
                                        الاسم
                                    </label>
                                    <div class="mt-2.5 relative">
                                        <input type="text" name="name" id="name" placeholder="الاسم" required
                                            class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500" />
                                    </div>
                                </div>

                                <div>
                                    <label for="email" class="text-base font-medium text-gray-900">
                                        الايميل
                                    </label>
                                    <div class="mt-2.5 relative">
                                        <input type="email" name="email" id="email" placeholder="<EMAIL>" required
                                            class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500" />
                                    </div>
                                </div>

                                <div>
                                    <label for="message" class="text-base font-medium text-gray-900">
                                        الرسالة
                                    </label>
                                    <div class="mt-2.5 relative">
                                        <textarea name="message" id="message" placeholder="الرسالة" required
                                            class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md resize-y focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500"
                                            rows="4"></textarea>
                                    </div>
                                </div>

                                <div>
                                    <button type="submit"
                                        class="inline-flex items-center justify-center w-full px-4 py-4 text-base font-semibold text-white transition-all duration-200 bg-purple-700 border border-transparent rounded-md focus:outline-none hover:bg-purple-400 focus:bg-purple-500">
                                        ارسال
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="md:hidden">
                <div class="flex items-center">
                    @for($i = 0; $i < 5; $i++)
                    <svg class="w-6 h-6 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    @endfor
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Contact Section -->

<!-- Testimonials Section -->
<section class="py-10 bg-gray-100 sm:py-16 lg:py-24">
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="max-w-2xl mx-auto text-center">
            <h2 class="text-2xl font-bold leading-tight text-black sm:text-4xl lg:text-4xl">
                @if($home_1_page_items && $home_1_page_items->testimonial_heading)
                    {{ $home_1_page_items->testimonial_heading }}
                @else
                    {{ $global_other_page_items->page_testimonials_title ?? 'ما يقولة عملائنا عنا' }}
                @endif
            </h2>
        </div>

        <div class="grid grid-cols-1 gap-6 px-4 mt-12 sm:px-0 xl:mt-20 xl:grid-cols-4 sm:grid-cols-2">
            @if($testimonials && $testimonials->count() > 0)
                @foreach($testimonials->take(4) as $testimonial)
                <div class="overflow-hidden bg-white hover:scale-105 hover:shadow-xl rounded-md">
                    <div class="px-5 py-6">
                        <div class="flex items-center justify-between">
                            @if($testimonial->photo)
                                <img class="flex-shrink-0 object-cover w-10 h-10 rounded-full" src="{{ asset('uploads/'.$testimonial->photo) }}" alt="{{ $testimonial->name }}" />
                            @else
                                <img class="flex-shrink-0 object-cover w-10 h-10 rounded-full" src="{{ asset('tbl-tech/img/client.png') }}" alt="{{ $testimonial->name }}" />
                            @endif
                            <div class="min-w-0 mr-3 ml-auto">
                                <p class="text-base font-semibold text-black truncate">
                                    {{ $testimonial->name }}
                                </p>
                                <p class="text-sm text-gray-600 truncate">{{ $testimonial->designation }}</p>
                            </div>
                            <a href="#" title="" class="inline-block text-sky-500">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z" />
                                </svg>
                            </a>
                        </div>
                        <blockquote class="mt-5">
                            <p class="text-base text-gray-800">
                                {{ $testimonial->comment }}
                                <span class="block text-sky-500">#TBLTech</span>
                            </p>
                        </blockquote>
                    </div>
                </div>
                @endforeach
            @else
                <!-- Default testimonials -->
                <div class="overflow-hidden bg-white hover:scale-105 hover:shadow-xl rounded-md">
                    <div class="px-5 py-6">
                        <div class="flex items-center justify-between">
                            <img class="flex-shrink-0 object-cover w-10 h-10 rounded-full" src="{{ asset('tbl-tech/img/client.png') }}" alt="" />
                            <div class="min-w-0 mr-3 ml-auto">
                                <p class="text-base font-semibold text-black truncate">ابو اسامة</p>
                                <p class="text-sm text-gray-600 truncate">تطبيق نوافذ</p>
                            </div>
                            <a href="#" title="" class="inline-block text-sky-500">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z" />
                                </svg>
                            </a>
                        </div>
                        <blockquote class="mt-5">
                            <p class="text-base text-gray-800">
                                لقد استخدمته لفترة وجدته سهل الاستخدام ومفيد للغاية. الآن يمكنني وتجديد وبيع باقات الهاتف بكل سهولة ويسر للعملاء.
                                <span class="block text-sky-500">#نوافذ</span>
                                <span class="block text-sky-500">#TBLTech</span>
                            </p>
                        </blockquote>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
<!-- End Testimonials Section -->

<!-- Clients Section -->
<section class="py-10 bg-white min-h-screen sm:py-16 lg:py-24" id="our_client">
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="max-w-2xl mx-auto text-center">
            <h2 class="text-2xl font-bold leading-tight text-black sm:text-4xl lg:text-4xl">
                {{ $global_other_page_items->page_about_team_members_heading ?? 'عملائنا' }}
            </h2>
            <p class="max-w-xl mx-auto mt-4 text-base leading-relaxed text-gray-600">
                {{ $global_other_page_items->page_about_team_members_subheading ?? 'عملاء TBL Tech الذين نفخر بهم' }}
            </p>
        </div>

        <div class="clients-grid grid items-center grid-cols-2 mx-auto mt-12 md:mt-20 md:grid-cols-4 lg:grid-cols-6 gap-x-6 gap-y-16">
            @if($clients && $clients->count() > 0)
                @foreach($clients as $client)
                <div class="hover:scale-110 duration-500 text-center">
                    @if($client->url)
                        <a href="{{ $client->url }}" target="_blank">
                            <img class="object-contain mx-auto" src="{{ asset('uploads/'.$client->photo) }}" alt="{{ $client->name }}" />
                        </a>
                    @else
                        <img class="object-contain mx-auto" src="{{ asset('uploads/'.$client->photo) }}" alt="{{ $client->name }}" />
                    @endif
                    <p class="mt-2 text-sm text-gray-700">{{ $client->name }}</p>
                </div>
                @endforeach
            @else
                <!-- Default clients -->
                <div class="hover:scale-110 duration-500 text-center">
                    <a href="">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/clients/cacbank.jpg') }}" alt="cacbank-website" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">كاك بنك</p>
                </div>

                <div class="hover:scale-110 duration-500 text-center">
                    <a href="">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/clients/cacbank.png') }}" alt="cacbank-website" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">معهد كاك بنك التعليمي</p>
                </div>

                <div class="hover:scale-110 duration-500 text-center">
                    <a href="https://pinpaiss.net/">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/clients/Pinpaiss LOGO.png') }}" alt="Pinpaiss-shop" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">متجر Pinpaiss</p>
                </div>

                <div class="hover:scale-110 duration-500 text-center">
                    <a href="https://el-lantech.com/">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/lantech-logo.png') }}" alt="lantech-logo" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">منصة لانتيك أونلاين التعليمية</p>
                </div>

                <div class="hover:scale-110 duration-500 text-center">
                    <a href="https://trips4travels.com/">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/trips-logo.png') }}" alt="trips for travel and tourism" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">تريبس للسفريات والسياحة</p>
                </div>

                <div class="hover:scale-110 duration-500 text-center">
                    <a href="">
                        <img class="object-contain mx-auto" src="{{ asset('tbl-tech/img/nawafith.png') }}" alt="nawafith-app" />
                    </a>
                    <p class="mt-2 text-sm text-gray-700">تطبيق نوافذ</p>
                </div>
            @endif
        </div>
    </div>
</section>
<!-- End Clients Section -->

@endsection
