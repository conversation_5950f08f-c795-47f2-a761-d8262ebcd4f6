@extends('front.layouts.master')

@section('seo_title', $global_setting->home_seo_title)
@section('seo_meta_description', $global_setting->home_seo_meta_description)

@section('content')
<style>
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .animation-delay-2000 {
        animation-delay: 2s;
    }

    .animation-delay-4000 {
        animation-delay: 4s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .float-animation {
        animation: float 3s ease-in-out infinite;
    }

    /* Custom Flaticon Styles */
    .flaticon-web-development:before,
    .flaticon-design:before,
    .flaticon-targeted-marketing:before,
    .flaticon-diplomat:before,
    .flaticon-teaching:before,
    .flaticon-laptop:before,
    .flaticon-health-check:before,
    .flaticon-bank:before,
    .flaticon-success:before,
    .flaticon-recommend:before,
    .flaticon-marketing:before,
    .flaticon-job-promotion:before,
    .flaticon-completed-task:before,
    .flaticon-settings:before,
    .flaticon-rating:before,
    .flaticon-group:before,
    .flaticon-learning:before,
    .flaticon-cloud:before,
    .flaticon-digital-services:before,
    .flaticon-graphic-design:before,
    .flaticon-technology:before,
    .flaticon-phone-call:before,
    .flaticon-visitor:before,
    .flaticon-promotion:before {
        font-size: inherit !important;
        line-height: 1;
    }

    /* Icon hover effects */
    .service-icon-container:hover .service-icon {
        transform: scale(1.1) rotate(5deg);
    }
</style>

<!-- Hero Section -->
<section class="py-16 lg:py-20 bg-gray-100" id="aboutUs">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-12 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl">
                @if($sliders->count() > 0 && $sliders->first()->text != '')
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($sliders->first()->text))) !!}
                @else
                    TBL TECH شركة متخصصة في
                    <span class="relative inline-flex">
                        <span class="absolute inset-x-0 bottom-0 border-b-[30px] border-purple-500"></span>
                        <span class="relative text-4xl font-bold text-black sm:text-6xl lg:text-7xl">
                            هندسة البرمجيات.
                        </span>
                    </span>
                @endif
            </h1>
            
            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                @if($welcome_one_items && $welcome_one_items->text)
                    {{ $welcome_one_items->text }}
                @else
                    {{ $global_setting->footer_text ?? 'نقدم حلولًا مبتكرة في تطوير الأنظمة وتصميم وبرمجة المواقع الإلكترونية والتطبيقات والمتاجر الإلكترونية.' }}
                @endif
            </p>
            
            @if($sliders->count() > 0 && $sliders->first()->button_text != '')
                <a href="{{ $sliders->first()->button_url }}" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    {{ $sliders->first()->button_text }}
                </a>
            @else
                <a href="{{ route('contact') }}" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    اطلب موقعك الان
                </a>
            @endif
        </div>
        <div class="lg:mt-0 mt-10 lg:col-span-5 lg:flex">
            @if($sliders->count() > 0 && $sliders->first()->photo != '')
                <img src="{{ asset('uploads/'.$sliders->first()->photo) }}" alt="Hero Image" class="w-full h-auto" />
            @else
                <img src="{{ asset('tbl-tech/img/home-img.svg') }}" alt="Hero Image" class="w-full h-auto" />
            @endif
        </div>
    </div>
</section>
<!-- End Hero Section -->

<!-- Services Section -->
<section class="relative py-20 bg-gradient-to-br from-gray-50 via-white to-purple-50 overflow-hidden" id="service">
    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-10 left-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div class="absolute top-40 right-10 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-10 left-1/2 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <!-- Section Badge -->
            <div class="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                خدماتنا المتميزة
            </div>

            <h2 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900 bg-clip-text text-transparent mb-6">
                @if($home_1_page_items && $home_1_page_items->service_heading)
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_1_page_items->service_heading))) !!}
                @else
                    خدماتنا التقنية
                @endif
            </h2>

            @if($home_1_page_items && $home_1_page_items->service_subheading)
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {{ $home_1_page_items->service_subheading }}
            </p>
            @else
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                نقدم حلولاً تقنية متطورة ومبتكرة تساعدك على تحقيق أهدافك الرقمية بأعلى معايير الجودة والاحترافية
            </p>
            @endif

            <!-- Decorative Line -->
            <div class="flex justify-center mt-8">
                <div class="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @if($services->count() > 0)
                @php
                    $service_count = ($home_1_page_items && $home_1_page_items->service_how_many) ? $home_1_page_items->service_how_many : 6;
                @endphp
                @foreach($services->take($service_count) as $service)
                <div class="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 border border-gray-100 hover:border-purple-200">
                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Service Content -->
                    <div class="relative p-8 text-center">
                        <!-- Large Icon Background Circle -->
                        <div class="relative mb-8 service-icon-container">
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full transform group-hover:scale-110 transition-transform duration-500"></div>
                            <div class="relative inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full shadow-2xl group-hover:shadow-purple-500/50 transition-all duration-500 mb-2">
                                @if($service->icon)
                                    <i class="{{ $service->icon }} service-icon text-white text-3xl transition-transform duration-300"></i>
                                @else
                                    <i class="fas fa-code service-icon text-white text-3xl transition-transform duration-300"></i>
                                @endif
                            </div>
                            <!-- Icon Glow Effect -->
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500 blur-lg"></div>
                        </div>

                        <!-- Service Image (Background) -->
                        @if($service->photo)
                            <div class="absolute inset-0 opacity-3 group-hover:opacity-8 transition-opacity duration-500 rounded-3xl overflow-hidden">
                                <img src="{{ asset('uploads/'.$service->photo) }}" alt="{{ $service->name }}" class="w-full h-full object-cover">
                            </div>
                        @endif

                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-700 transition-colors duration-300 relative z-10">
                            {{ $service->name }}
                        </h3>

                        <p class="text-gray-600 mb-6 leading-relaxed line-clamp-3 relative z-10">
                            {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($service->short_description))) !!}
                        </p>

                        <!-- Action Button -->
                        <a href="{{ route('service',$service->slug) }}" class="relative z-10 inline-flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group/btn">
                            <span>استكشف الخدمة</span>
                            <svg class="w-5 h-5 mr-2 transition-transform group-hover/btn:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-6 right-6 w-3 h-3 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
                    <div class="absolute top-12 right-4 w-2 h-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 group-hover:opacity-50 transition-opacity duration-500"></div>
                    <div class="absolute bottom-6 left-6 w-4 h-4 bg-gradient-to-br from-purple-300 to-blue-400 rounded-full opacity-25 group-hover:opacity-55 transition-opacity duration-500"></div>
                </div>
                @endforeach
            @else
                <!-- Default Services if none exist -->
                <div class="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 border border-gray-100 hover:border-purple-200">
                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Service Content -->
                    <div class="relative p-8 text-center">
                        <!-- Large Icon Background Circle -->
                        <div class="relative mb-8 service-icon-container">
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full transform group-hover:scale-110 transition-transform duration-500"></div>
                            <div class="relative inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full shadow-2xl group-hover:shadow-purple-500/50 transition-all duration-500 mb-2">
                                <i class="flaticon-web-development service-icon text-white text-3xl transition-transform duration-300"></i>
                            </div>
                            <!-- Icon Glow Effect -->
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500 blur-lg"></div>
                        </div>

                        <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-700 transition-colors duration-300 relative z-10">
                            تصميم وبرمجة المواقع الالكترونية
                        </h3>

                        <p class="text-gray-600 mb-6 leading-relaxed line-clamp-3 relative z-10">
                            تصميم الأسواق الالكترونية والمواقع التفاعلية بأحدث التقنيات والمعايير العالمية
                        </p>

                        <!-- Action Button -->
                        <a href="{{ route('services') }}" class="relative z-10 inline-flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group/btn">
                            <span>استكشف الخدمة</span>
                            <svg class="w-5 h-5 mr-2 transition-transform group-hover/btn:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-6 right-6 w-3 h-3 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
                    <div class="absolute top-12 right-4 w-2 h-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 group-hover:opacity-50 transition-opacity duration-500"></div>
                    <div class="absolute bottom-6 left-6 w-4 h-4 bg-gradient-to-br from-purple-300 to-blue-400 rounded-full opacity-25 group-hover:opacity-55 transition-opacity duration-500"></div>
                </div>
            @endif
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <a href="{{ route('services') }}" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl">
                <span>عرض جميع الخدمات</span>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
<!-- End Services Section -->

<!-- Get Your System Section -->
@if($welcome_one_items)
<section class="relative py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div class="absolute top-20 right-0 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-0 left-1/3 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Content Column -->
            <div class="text-white space-y-8">
                <!-- Section Badge -->
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $welcome_one_items->subheading }}
                </div>

                <div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                        {{ $welcome_one_items->heading }}
                    </h2>
                    <p class="text-xl text-gray-200 leading-relaxed mb-8">
                        {{ $welcome_one_items->text }}
                    </p>
                </div>

                <!-- Features Grid -->
                @if($welcome_one_item_elements->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    @foreach($welcome_one_item_elements->take(4) as $element)
                    <div class="flex items-start space-x-4 group">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="{{ $element->icon }} text-white text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2">{{ $element->heading }}</h3>
                            <p class="text-gray-300 text-sm">{{ $element->text }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- CTA Button -->
                @if($welcome_one_items->button_text && $welcome_one_items->button_url)
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ $welcome_one_items->button_url }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl group">
                        <span>{{ $welcome_one_items->button_text }}</span>
                        <svg class="w-5 h-5 mr-3 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                    <a href="{{ route('contact') }}" class="inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-2xl hover:bg-white/20 transition-all duration-300 border border-white/20">
                        تواصل معنا
                    </a>
                </div>
                @endif
            </div>

            <!-- Image Column -->
            <div class="relative">
                @if($welcome_one_items->photo1)
                <div class="relative">
                    <!-- Main Image -->
                    <div class="relative z-10">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo1) }}" alt="نظام إدارة المشاريع" class="w-full h-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-purple-900/20 to-transparent rounded-2xl"></div>
                    </div>

                    <!-- Secondary Image -->
                    @if($welcome_one_items->photo2)
                    <div class="absolute -bottom-8 -left-8 z-20 w-48 h-48">
                        <img src="{{ asset('uploads/'.$welcome_one_items->photo2) }}" alt="تطبيق الهاتف" class="w-full h-full object-cover rounded-xl shadow-xl border-4 border-white/20 backdrop-blur-sm">
                    </div>
                    @endif

                    <!-- Floating Elements -->
                    <div class="absolute top-8 right-8 w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endif
<!-- End Get Your System Section -->

<!-- Pricing Section -->
<section class="py-20 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
                </svg>
                خطط الأسعار
            </div>
            <h2 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900 bg-clip-text text-transparent mb-6">
                اختر الخطة المناسبة لك
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                خطط مرنة ومناسبة لجميع أحجام الأعمال، ابدأ مجاناً واختر ما يناسب احتياجاتك
            </p>
        </div>

        <!-- Pricing Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Static Pricing Card 1 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">مجاني</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة التجريبية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">تجربة مجانية لمدة 14 يوم</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>جربه مجاناً</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Static Pricing Card 2 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 ring-2 ring-purple-500 scale-105 mt-8">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                    <div class="bg-gradient-to-r from-purple-500 to-blue-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                        الأكثر شعبية
                    </div>
                </div>
                <div class="p-8 pt-12 relative z-20">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">4400 ر.س</span>
                            <span class="text-gray-500 ml-2">/ شهرياً</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة الشهرية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">جميع المميزات متاحة</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>اشترك الآن</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Static Pricing Card 3 -->
            <div class="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <span class="text-4xl font-bold text-gray-900">44800 ر.س</span>
                            <span class="text-gray-500 ml-2">/ سنوياً</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">الخطة السنوية</h3>
                    </div>
                    <div class="mb-8">
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700">وفر 20% مع الاشتراك السنوي</span>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <a href="#" class="w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <span>اشترك سنوياً</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">جميع الخطط تشمل دعم فني مجاني ومتابعة مستمرة</p>
            <a href="{{ route('pricing_plans') }}" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold">
                مقارنة تفصيلية للخطط
                <svg class="w-4 h-4 mr-2 transition-transform hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
<!-- End Pricing Section -->

@endsection
