@extends('front.layouts.master')

@section('seo_title', $global_other_page_items->page_about_seo_title)
@section('seo_meta_description', $global_other_page_items->page_about_seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-[#351735] to-[#101545] py-20">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">
                {{ $global_other_page_items->page_about_title }}
            </h1>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-purple-300 hover:text-white transition duration-300">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-purple-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-white">{{ $global_other_page_items->page_about_title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

@if($global_other_page_items->page_about_welcome_status == 'Show')
<!-- About Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Content Column -->
            <div class="order-2 lg:order-1">
                <div class="space-y-6">
                    <div>
                        <span class="text-purple-600 font-semibold text-lg">{{ $welcome_one_items->subheading }}</span>
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mt-2 mb-4">
                            {{ $welcome_one_items->heading }}
                        </h2>
                        <div class="text-gray-600 text-lg leading-relaxed">
                            {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($welcome_one_items->text))) !!}
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="space-y-4">
                        @foreach($welcome_one_item_elements as $item)
                        <div class="flex items-start space-x-4 p-4 bg-white rounded-lg shadow-sm">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="{{ $item->icon }} text-purple-600 text-xl"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="text-lg font-semibold text-gray-900 mb-2">{{ $item->heading }}</h5>
                                <div class="text-gray-600">
                                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($item->text))) !!}
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Author Info & Button -->
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 pt-6">
                        <div class="flex items-center space-x-4">
                            <img src="{{ asset('uploads/'.$welcome_one_items->person_photo) }}" alt="{{ $welcome_one_items->person_name }}" class="w-16 h-16 rounded-full object-cover">
                            <div>
                                <h5 class="font-semibold text-gray-900">{{ $welcome_one_items->person_name }}</h5>
                                <span class="text-gray-600">{{ $welcome_one_items->person_designation }}</span>
                            </div>
                        </div>
                        <a href="{{ $welcome_one_items->button_url }}" class="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition duration-300">
                            {{ $welcome_one_items->button_text }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Image Column -->
            <div class="order-1 lg:order-2">
                <div class="relative">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="space-y-4">
                            <img src="{{ asset('uploads/'.$welcome_one_items->photo1) }}" alt="About Image 1" class="w-full h-64 object-cover rounded-lg shadow-lg">
                        </div>
                        <div class="space-y-4 pt-8">
                            <img src="{{ asset('uploads/'.$welcome_one_items->photo2) }}" alt="About Image 2" class="w-full h-64 object-cover rounded-lg shadow-lg">
                            <!-- Experience Box -->
                            <div class="bg-purple-600 text-white p-6 rounded-lg text-center">
                                <div class="text-3xl font-bold">{{ $welcome_one_items->experience_year }}</div>
                                <div class="text-purple-200">{{ __('Work Experience') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif


@if($global_other_page_items->page_about_service_status == 'Show')
<!-- Services Section -->
<section class="py-20 bg-gradient-to-r from-[#351735] to-[#101545]">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-16">
            <div class="lg:col-span-7">
                <span class="text-purple-300 font-semibold text-lg">{{ $global_other_page_items->page_about_service_subheading }}</span>
                <h2 class="text-3xl md:text-4xl font-bold text-white mt-2">
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_service_heading))) !!}
                </h2>
            </div>
            <div class="lg:col-span-5">
                <div class="text-gray-300 text-lg leading-relaxed">
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_service_text))) !!}
                </div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($services->take($global_other_page_items->page_about_service_how_many) as $service)
            <div class="group relative bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <!-- Service Image -->
                <div class="relative h-48 overflow-hidden">
                    <img src="{{ asset('uploads/'.$service->photo) }}" alt="{{ $service->name }}" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>

                <!-- Service Content -->
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="{{ $service->icon }} text-purple-600 text-xl"></i>
                        </div>
                        <h5 class="text-lg font-semibold text-gray-900">{{ $service->name }}</h5>
                    </div>

                    <!-- Hover Content -->
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="text-gray-600 mb-4">{{ $service->short_description }}</div>
                        <a href="{{ route('service',$service->slug) }}" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold">
                            {{ __('Learn More') }}
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif


@if($global_other_page_items->page_about_team_members_status == 'Show')
<!-- Team Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <span class="text-purple-600 font-semibold text-lg">{{ $global_other_page_items->page_about_team_members_subheading }}</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mt-2">
                {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_team_members_heading))) !!}
            </h2>
        </div>

        <!-- Team Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($team_members->take($global_other_page_items->page_about_team_members_how_many) as $item)
            <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300">
                <!-- Team Member Image -->
                <div class="relative h-80 overflow-hidden">
                    <a href="{{ route('team_member',$item->slug) }}">
                        <img src="{{ asset('uploads/'.$item->photo) }}" alt="{{ $item->name }}" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                    </a>

                    <!-- Social Links Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex justify-center space-x-3">
                                @if($item->facebook != '')
                                    <a href="{{ $item->facebook }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                @endif
                                @if($item->twitter != '')
                                    <a href="{{ $item->twitter }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                                @if($item->linkedin != '')
                                    <a href="{{ $item->linkedin }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                @endif
                                @if($item->instagram != '')
                                    <a href="{{ $item->instagram }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                @endif
                                @if($item->youtube != '')
                                    <a href="{{ $item->youtube }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-youtube"></i>
                                    </a>
                                @endif
                                @if($item->pinterest != '')
                                    <a href="{{ $item->pinterest }}" target="_blank" class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors duration-300">
                                        <i class="fab fa-pinterest-p"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Member Info -->
                <div class="p-6 text-center">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">
                        <a href="{{ route('team_member',$item->slug) }}" class="hover:text-purple-600 transition-colors duration-300">
                            {{ $item->name }}
                        </a>
                    </h4>
                    <span class="text-purple-600 font-medium">{{ $item->designation }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

@endsection