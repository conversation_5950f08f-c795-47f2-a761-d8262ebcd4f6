{"name": "laravel/pint", "description": "An opinionated code formatter for PHP.", "keywords": ["php", "format", "formatter", "lint", "linter"], "homepage": "https://laravel.com", "type": "project", "license": "MIT", "support": {"issues": "https://github.com/laravel/pint/issues", "source": "https://github.com/laravel/pint"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1.0", "ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "ext-xml": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.34.1", "illuminate/view": "^10.26.2", "laravel-zero/framework": "^10.1.2", "mockery/mockery": "^1.6.6", "nunomaduro/larastan": "^2.6.4", "nunomaduro/termwind": "^1.15.1", "pestphp/pest": "^2.20.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Scripts\\": "scripts/", "Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.1.0"}, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true, "bin": ["builds/pint"]}