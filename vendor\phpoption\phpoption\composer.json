{"name": "phpoption/phpoption", "description": "Option Type for PHP", "keywords": ["php", "option", "language", "type"], "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.32 || ^9.6.3 || ^10.0.12"}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "autoload-dev": {"psr-4": {"PhpOption\\Tests\\": "tests/PhpOption/Tests/"}}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true}, "preferred-install": "dist"}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "minimum-stability": "dev", "prefer-stable": true}