{"name": "spatie/backtrace", "description": "A better backtrace", "keywords": ["spatie", "backtrace"], "homepage": "https://github.com/spatie/backtrace", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^7.3|^8.0"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": "^9.3", "spatie/phpunit-snapshot-assertions": "^4.2", "symfony/var-dumper": "^5.1"}, "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Backtrace\\Tests\\": "tests"}}, "scripts": {"psalm": "vendor/bin/psalm", "test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes"}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/spatie"}, {"type": "other", "url": "https://spatie.be/open-source/support-us"}]}