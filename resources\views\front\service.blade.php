@extends('front.layouts.master')

@section('seo_title', $service->seo_title)
@section('seo_meta_description', $service->seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-purple-600 to-indigo-600 py-20 overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $service->name }}</h1>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 text-white">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-purple-200 transition-colors">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('services') }}" class="hover:text-purple-200 transition-colors">
                                {{ $global_other_page_items->page_services_title }}
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-purple-200">{{ $service->name }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Services List -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 text-right">خدماتنا الأخرى</h3>
                    <ul class="space-y-3">
                        @foreach($services as $item)
                        <li>
                            <a href="{{ route('service',$item->slug) }}"
                               class="flex items-center justify-between p-3 rounded-lg transition-all duration-300
                                      {{ $item->slug == $service->slug ? 'bg-purple-100 text-purple-700 border-r-4 border-purple-500' : 'hover:bg-gray-100 text-gray-700' }}">
                                <span class="text-right">{{ $item->name }}</span>
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>

                @if($service->phone != null)
                <!-- Contact Card -->
                <div class="bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg shadow-lg p-6 text-white mb-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2">{{ __('Contact with us for any advice') }}</h3>
                        <p class="text-purple-100 mb-4">{{ __('Need help? Talk to an expert') }}</p>
                        <a href="tel:{{ $service->phone }}"
                           class="inline-block bg-white text-purple-600 font-bold py-2 px-6 rounded-lg hover:bg-purple-50 transition-colors">
                            {{ $service->phone }}
                        </a>
                    </div>
                </div>
                @endif

                @if($service->pdf != null)
                <!-- PDF Download -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <a href="{{ asset('uploads/'.$service->pdf) }}"
                       class="flex items-center justify-center w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        {{ __('Download PDF File') }}
                    </a>
                </div>
                @endif
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Service Image -->
                @if($service->banner != null)
                <div class="mb-8">
                    <img src="{{ asset('uploads/'.$service->banner) }}"
                         alt="{{ $service->name }}"
                         class="w-full h-64 md:h-80 object-cover rounded-lg shadow-lg">
                </div>
                @endif

                <!-- Service Overview -->
                <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6 text-right">{{ __('Service Overview') }}</h2>
                    <div class="prose prose-lg max-w-none text-right">
                        <div class="text-gray-700 leading-relaxed">
                            {!! clean($service->description) !!}
                        </div>
                    </div>
                </div>

                <!-- FAQ Section -->
                @if($faqs->count() > 0)
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-right">{{ __('Frequently Asked Questions') }}</h2>
                    <div class="space-y-4">
                        @foreach($faqs as $index => $faq)
                        <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <div class="relative">
                                <input type="checkbox" id="faq-{{ $index }}" class="peer absolute top-0 inset-x-0 w-full h-16 opacity-0 z-10 cursor-pointer">
                                <div class="bg-gray-50 hover:bg-gray-100 transition-colors duration-300 h-16 pl-5 flex items-center justify-between px-6">
                                    <h3 class="text-lg font-semibold text-gray-900 text-right flex-1">{{ $faq->question }}</h3>
                                    <div class="text-purple-600 transition-transform duration-300 rotate-0 peer-checked:rotate-180 ml-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="bg-white overflow-hidden transition-all duration-500 max-h-0 peer-checked:max-h-96">
                                    <div class="p-6 border-t border-gray-100">
                                        <div class="text-gray-700 text-right leading-relaxed">
                                            {!! clean($faq->answer) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="bg-gradient-to-r from-purple-600 to-indigo-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">هل تحتاج إلى هذه الخدمة؟</h2>
        <p class="text-xl text-purple-100 mb-8">تواصل معنا الآن للحصول على استشارة مجانية</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact') }}"
               class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-purple-600 bg-white hover:bg-purple-50 transition-colors">
                تواصل معنا
            </a>
            @if($global_setting->whatsapp != null)
            <a href="https://wa.me/{{ $global_setting->whatsapp }}"
               target="_blank"
               class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-purple-600 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                </svg>
                واتساب
            </a>
            @endif
        </div>
    </div>
</section>
@endsection