/* 

Template Name: TBL pronixs
Version: 1.0.0

/************ TABLE OF CONTENTS ***************
1. Fonts
2. Reset
3. Global Settings
4. Main Header / TwO
5. <PERSON>y Header 
6. Mobile Menu
7. Section Title
8. Main Slider
9. Banner Section
10. Project Section
11. Features Section / TwO / Three / Four / Five
12. About Section / Two
13. Services Section / Two
14. Call To Action / Two
15. FAQ's Sectiom
16.  Marquee Section
17. Fun Fact Section
18. Testimonial Section / Two
19. team Section 
20, Contact Section / Two
21. Why Choose Us / Two
22. News Section / Two
23. Video Section
24. Map Section
25. Clients Section
26. Main Footer
**********************************************/

@import "scss-bootstrap/functions";
@import "scss-bootstrap/variables";
@import "scss-bootstrap/mixins";
@import "common/root.scss";
@import "common/mixing.scss";
@import "common/loader.scss";

// @import url('https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@import url('animate.css');
@import url('owl.css');
@import url('swiper.min.css');
@import url('jquery.fancybox.min.css');
@import url('linear.css');
@import url('fontawesome.css');
@import url('flaticon.css');
@import url('tm-bs-mp.css');
@import url('tm-utility-classes.css');

// import SCSS components one by one
@import "reset.scss";
@import "button.scss";
@import "animate-icon.scss";
@import "search-popup.scss";
@import "header.scss";
@import "mobile-menu.scss";
@import "section-title.scss";
@import "main-slider.scss";
@import "banner.scss";
@import "projects.scss";
@import "features.scss";
@import "about.scss";
@import "services.scss";
@import "call-to-action.scss";
@import "faqs.scss";
@import "marquee.scss";
@import "funfact.scss";
@import "testimonials.scss";
@import "team.scss";
@import "contact.scss";
@import "why-choose-us.scss";
@import "news.scss";
@import "video.scss";
@import "new-homes.scss";
@import "map.scss";
@import "clients.scss";
@import "pricing.scss";

//inner
@import "shop/loader.scss";

@import "inner/page-title.scss";
@import "inner/404.scss";
@import "inner/service-details.scss";
@import "inner/project-details.scss";
@import "inner/news-details.scss";
@import "inner/news-sidebar.scss";
@import "inner/news-comment.scss";
@import "inner/team-details.scss";
@import "inner/contact-page.scss";

@import "footer.scss";