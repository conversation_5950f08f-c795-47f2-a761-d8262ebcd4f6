/* 

Template Name: TBL pronixs
Version: Laravel

/************ TABLE OF CONTENTS ***************
1. Fonts
2. Reset
3. Global Settings
4. Main Header / TwO
5. <PERSON>y Header 
6. Mobile Menu
7. Section Title
8. Main Slider
9. Banner Section
10. Project Section
11. Features Section / TwO / Three / Four / Five
12. About Section / Two
13. Services Section / Two
14. Call To Action / Two
15. FAQ's Sectiom
16.  Marquee Section
17. Fun Fact Section
18. Testimonial Section / Two
19. team Section 
20, Contact Section / Two
21. Why Choose Us / Two
22. News Section / Two
23. Video Section
24. Map Section
25. Clients Section
26. Main Footer
**********************************************/
/* @import url("https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700;800;900&display=swap"); */
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url("animate.css");
@import url("owl.css");
@import url("swiper.min.css");
@import url("jquery.fancybox.min.css");
@import url("linear.css");
@import url("fontawesome.css");
@import url("flaticon.css");
@import url("tm-bs-mp.css");
@import url("tm-utility-classes.css");
:root {
  --theme-color-light: #ffffff;
  --theme-color-dark: #18191c;
  --theme-color1: #fec63f;
  --bg-theme-color1: var(--theme-color1);
  --theme-color2: #1A4D6A;
  --bg-theme-color2: var(--theme-color2);
  --text-color-bg-theme-color1: #222;
  --text-color-bg-theme-color2: #fff;
  --text-color: #6f7174;
  --headings-color: var(--theme-color2);
  --link-color: var(--theme-color2);
  --link-hover-color: var(--theme-color2);
  --text-font: "Tajawal", sans-serif;
  --title-font: "Tajawal", sans-serif;
  --body-font-size: 15px;
  --body-line-height: 30px;
  --body-font-weight: 400;
  --line-height-heading-h1: 1em;
  --line-height-heading: 1.2em;
  --line-height-heading-small: 1.4em;
  --h1-font-size: 100px;
  --h2-font-size: 50px;
  --h3-font-size: 36px;
  --h4-font-size: 24px;
  --h5-font-size: 20px;
  --h6-font-size: 18px;
  --h1-font-weight: 500;
  --h2-font-weight: 600;
  --h3-font-weight: 600;
  --h4-font-weight: 600;
  --h5-font-weight: 600;
  --h6-font-weight: 600;
  --sec-title-subtitle-color: var(--text-color);
  --sec-title-subtitle-font-size: var(--body-font-size);
  --sec-title-subtitle-font-family: var(--text-font);
  --sec-title-subtitle-font-weight: 500;
  --sec-title-subtitle-line-height: 20px;
  --sec-title-color: var(--theme-color2);
  --sec-title-font-size: var(--h2-font-size);
  --sec-title-font-family: var(--title-font);
  --sec-title-font-weight: 600;
  --theme-light-background: #f8f6f1;
  --theme-light-background-text-color: var(--headings-color);
  --theme-black: #131313;
  --container-width: 1200px;
  --small-container-width: 1000px;
  --large-container-width: 1310px;
  --container-pt: 120px;
  --container-pb: 120px;
}

/*
 * typography.scss
 * -----------------------------------------------
*/
::-moz-selection {
  background: var(--theme-color2);
  color: #fff;
  text-shadow: none;
}
::selection {
  background: var(--theme-color2);
  color: #fff;
  text-shadow: none;
}

:active,
:focus {
  outline: none !important;
}

::-webkit-input-placeholder {
  color: #7c858c;
}

::-moz-input-placeholder {
  color: #7c858c;
}

::-ms-input-placeholder {
  color: #7c858c;
}

body {
  background-color: #fff;
  background-attachment: fixed;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
  counter-reset: my-sec-counter;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  color: var(--text-color);
  font-size: var(--body-font-size);
  font-family: var(--text-font);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
}

p, .text {
  color: var(--text-color);
  font-size: var(--body-font-size);
  font-family: var(--text-font);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
}

/* -------- Headings ---------- */
h1, h2, h3, h4, h5, h6 {
  color: var(--headings-color);
  font-family: var(--title-font);
  position: relative;
  line-height: var(--line-height-heading-);
  letter-spacing: -0.04em;
}
h1 small,
h1 .small, h2 small,
h2 .small, h3 small,
h3 .small, h4 small,
h4 .small, h5 small,
h5 .small, h6 small,
h6 .small {
  font-weight: normal;
  line-height: 1;
  color: var(--headings-color);
}
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
  color: inherit;
  font-weight: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 1rem;
}

h1 {
  font-size: var(--h1-font-size);
  font-weight: var(--h1-font-weight);
  line-height: var(--line-height-heading-h1);
}

h2 {
  font-size: var(--h2-font-size);
  font-weight: var(--h2-font-weight);
  line-height: var(--line-height-heading);
}

h3 {
  font-size: var(--h3-font-size);
  font-weight: var(--h3-font-weight);
  line-height: var(--line-height-heading);
}

h4 {
  font-size: var(--h4-font-size);
  font-weight: var(--h4-font-weight);
  line-height: var(--line-height-heading);
}

h5 {
  font-size: var(--h5-font-size);
  font-weight: var(--h5-font-weight);
  line-height: var(--line-height-heading);
}

h6 {
  font-size: var(--h6-font-size);
  font-weight: var(--h6-font-weight);
  line-height: var(--line-height-heading-small);
}

/* -------- Body Text ---------- */
table p {
  margin-bottom: 0;
}

p {
  margin-bottom: 20px;
}
p a:not(.button):not(.btn):hover, p a:not(.button):not(.btn):focus {
  text-decoration: underline;
}

/* -------- other ---------- */
a {
  color: var(--link-color);
  text-decoration: none;
  font-weight: var(--body-font-weight);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
a:hover, a:focus {
  color: inherit;
  text-decoration: none;
  outline: none;
}
a b, a strong {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
a img {
  border: none;
}

pre,
ul,
ol,
dl,
dd,
blockquote,
address,
table,
fieldset {
  margin-bottom: 10px;
}

ol, ul {
  list-style-position: inside;
  margin: 0;
  padding: 0;
}

b, strong {
  color: #333;
  font-weight: var(--body-font-weight-bold);
}

iframe {
  border: none !important;
}

/*
 * container.scss
 * -----------------------------------------------
*/
.container .container {
  width: 100%;
}

.container .container,
.container .container-fluid,
.container-fluid .container,
.container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

section > .container,
section > .container-fluid {
  padding-top: var(--container-pt);
  padding-bottom: var(--container-pt);
}

@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: var(--container-width);
  }
}
/*=== Default Form ===*/
.form-control, .input-text {
  height: calc(2.25rem + 27px);
  padding: 14px 30px;
  outline: 0;
  background-color: #f4f5f8;
  border: 1px solid #f4f5f8;
  color: #686a6f;
  font-size: 0.9rem;
  width: 100%;
}
.form-control::-webkit-input-placeholder, .input-text::-webkit-input-placeholder {
  color: #686a6f;
  opacity: 1;
}
.form-control::-moz-placeholder, .input-text::-moz-placeholder {
  color: #686a6f;
  opacity: 1;
}
.form-control:-ms-input-placeholder, .input-text:-ms-input-placeholder {
  color: #686a6f;
  opacity: 1;
}
.form-control::-ms-input-placeholder, .input-text::-ms-input-placeholder {
  color: #686a6f;
  opacity: 1;
}
.form-control::placeholder, .input-text::placeholder {
  color: #686a6f;
  opacity: 1;
}
.form-control:-ms-input-placeholder, .input-text:-ms-input-placeholder {
  color: #686a6f;
}
.form-control::-ms-input-placeholder, .input-text::-ms-input-placeholder {
  color: #686a6f;
}

textarea.form-control {
  height: auto;
  padding-top: 15px;
  padding-bottom: 15px;
}

/*
 *	boxed-layout.scss
 * -----------------------------------------------
*/
.tm-boxed-layout {
  background-color: #444;
  padding-top: 40px;
  padding-bottom: 40px;
  padding-left: 15px;
  padding-right: 15px;
}
.tm-boxed-layout.layer-overlay:before {
  z-index: -1;
}
.tm-boxed-layout .page-wrapper {
  margin: 0 auto;
  overflow: hidden;
}
@media (min-width: 1200px) {
  .tm-boxed-layout .page-wrapper {
    width: 1170px;
  }
}
.tm-boxed-layout .container .container {
  width: 100%;
}
@media (min-width: 1200px) {
  .tm-boxed-layout .container {
    width: 1140px;
  }
}
@media (min-width: 1000px) {
  .tm-boxed-layout.tm-container-970px .page-wrapper {
    width: 970px;
  }
}
.tm-boxed-layout.tm-container-970px .container,
.tm-boxed-layout.tm-container-970px .container-fluid {
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-970px .container .container,
.tm-boxed-layout.tm-container-970px .container .container-fluid,
.tm-boxed-layout.tm-container-970px .container-fluid .container,
.tm-boxed-layout.tm-container-970px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1300px) {
  .tm-boxed-layout.tm-container-1230px .page-wrapper {
    width: 1230px;
  }
}
.tm-boxed-layout.tm-container-1230px .container,
.tm-boxed-layout.tm-container-1230px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1230px .container .container,
.tm-boxed-layout.tm-container-1230px .container .container-fluid,
.tm-boxed-layout.tm-container-1230px .container-fluid .container,
.tm-boxed-layout.tm-container-1230px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1400px) {
  .tm-boxed-layout.tm-container-1300px .page-wrapper {
    width: 1300px;
  }
}
.tm-boxed-layout.tm-container-1300px .container,
.tm-boxed-layout.tm-container-1300px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1300px .container .container,
.tm-boxed-layout.tm-container-1300px .container .container-fluid,
.tm-boxed-layout.tm-container-1300px .container-fluid .container,
.tm-boxed-layout.tm-container-1300px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1400px) {
  .tm-boxed-layout.tm-container-1340px .page-wrapper {
    width: 1340px;
  }
}
.tm-boxed-layout.tm-container-1340px .container,
.tm-boxed-layout.tm-container-1340px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1340px .container .container,
.tm-boxed-layout.tm-container-1340px .container .container-fluid,
.tm-boxed-layout.tm-container-1340px .container-fluid .container,
.tm-boxed-layout.tm-container-1340px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1400px) {
  .tm-boxed-layout.tm-container-1440px .page-wrapper {
    width: 1440px;
  }
}
.tm-boxed-layout.tm-container-1440px .container,
.tm-boxed-layout.tm-container-1440px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1440px .container .container,
.tm-boxed-layout.tm-container-1440px .container .container-fluid,
.tm-boxed-layout.tm-container-1440px .container-fluid .container,
.tm-boxed-layout.tm-container-1440px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1560px) {
  .tm-boxed-layout.tm-container-1500px .page-wrapper {
    width: 1500px;
  }
}
.tm-boxed-layout.tm-container-1500px .container,
.tm-boxed-layout.tm-container-1500px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1500px .container .container,
.tm-boxed-layout.tm-container-1500px .container .container-fluid,
.tm-boxed-layout.tm-container-1500px .container-fluid .container,
.tm-boxed-layout.tm-container-1500px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 1660px) {
  .tm-boxed-layout.tm-container-1600px .page-wrapper {
    width: 1600px;
  }
}
.tm-boxed-layout.tm-container-1600px .container,
.tm-boxed-layout.tm-container-1600px .container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
.tm-boxed-layout.tm-container-1600px .container .container,
.tm-boxed-layout.tm-container-1600px .container .container-fluid,
.tm-boxed-layout.tm-container-1600px .container-fluid .container,
.tm-boxed-layout.tm-container-1600px .container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}
.tm-boxed-layout.tm-container-100pr .page-wrapper {
  width: 95%;
}
.tm-boxed-layout.tm-container-100pr .page-wrapper .container,
.tm-boxed-layout.tm-container-100pr .page-wrapper .container-fluid {
  width: 100%;
}
.tm-boxed-layout.container-shadow .page-wrapper {
  -webkit-box-shadow: 0 0 25px 0 #777;
          box-shadow: 0 0 25px 0 #777;
}
.tm-boxed-layout .header,
.tm-boxed-layout section,
.tm-boxed-layout .footer {
  background-color: #fff;
}
.tm-boxed-layout .header .navbar-default {
  background-color: #fff;
  border-color: #fff;
  padding: 0;
}
.tm-boxed-layout .header .navbar-default .navbar-nav > li > a {
  padding: 30px 5px;
}
.tm-boxed-layout .header .navbar-default .navbar-collapse {
  background-color: #fff;
  border-color: #fff;
  margin-right: 30px;
}
.tm-boxed-layout .header #header-logo {
  margin: 0;
  padding-top: 27px;
}
.tm-boxed-layout .banner-section .slide-item {
  min-height: 540px;
}
.tm-boxed-layout .banner-section .slide-item .content-box {
  padding: 150px 0 220px;
}
.tm-boxed-layout .banner-section-two .content-box {
  padding: 250px 0 150px;
}
.tm-boxed-layout .banner-section-three .content-box {
  padding: 250px 0 150px;
}
.tm-boxed-layout .banner-section-four .content-box {
  padding: 130px 0;
}

@media (max-width: 1200px) {
  .tm-boxed-layout {
    padding-top: 15px;
  }
  .tm-boxed-layout .header .navbar-default .navbar-nav > li > a {
    padding: 10px;
  }
  .tm-boxed-layout .header .navbar-default .navbar-collapse {
    margin-right: 15px;
  }
  .tm-boxed-layout .navbar-header {
    padding: 15px 0;
  }
  .tm-boxed-layout .navbar-collapse .navbar-nav li a .caret {
    margin-right: 0;
  }
}
/* -------- Dark Theme Styling ---------- */
.dark-layout {
  background-color: var(--theme-color2) !important;
  --sec-title-color: var(--theme-color-light);
}
.dark-layout .sticky-header .main-menu .navigation > li > a,
.dark-layout h1, .dark-layout h2, .dark-layout h3, .dark-layout h4, .dark-layout h5, .dark-layout h6 {
  color: var(--theme-color-light);
}
.dark-layout .preloader {
  background-color: var(--theme-color2);
}
.dark-layout .sticky-header {
  background-color: var(--theme-color2);
}
.dark-layout .hidden-bar .upper-box {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dark-layout .hidden-bar .social-links {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.dark-layout .hidden-bar .social-links li {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

/*** 

====================================================================
Reset
====================================================================

***/
* {
  margin: 0px;
  padding: 0px;
  border: none;
  outline: none;
  font-size: 100%;
}

/*** 

====================================================================
Global Settings
====================================================================

***/
textarea {
  overflow: hidden;
  resize: none;
}

button {
  outline: none !important;
  cursor: pointer;
}

img {
  display: inline-block;
  max-width: 100%;
  height: auto;
}

ul,
li {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.title a {
  color: inherit;
}

.color1 {
  color: var(--theme-color1);
}

.color2 {
  color: var(--theme-color2);
}

.color3 {
  color: var(--theme-color3);
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
  z-index: 99;
  background-color: #ffffff;
}

.large-container {
  position: static;
  max-width: var(--large-container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.auto-container {
  position: static;
  max-width: var(--container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.small-container {
  position: static;
  max-width: var(--small-container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.dropdown-toggle::after {
  display: none;
}

/*=======================
    Preloader
=======================*/
.preloader {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 999999;
  background-color: #ffffff;
}

.preloader:after {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 150px;
  margin-left: -75px;
  margin-top: -30px;
  height: 50px;
  background-position: center center;
  background-repeat: no-repeat;
  -webkit-animation: pulse 1s infinite linear;
          animation: pulse 1s infinite linear;
  content: "";
}

.preloader:before {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  width: 100%;
  text-align: center;
  margin: 0 auto;
  margin-top: 20px;
  color: var(--theme-color2);
  font-weight: 600;
  font-size: 14px;
  font-family: var(--title-font);
  letter-spacing: 2px;
  text-transform: uppercase;
  content: "Loading";
  -webkit-transition: none;
  transition: none;
}

/*=======================
Scroll To Top style
=======================*/
.scroll-to-top {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 40px;
  font-size: 16px;
  line-height: 40px;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  background-color: var(--theme-color1);
  z-index: 100;
  display: none;
  border-radius: 50%;
  margin: 0 auto;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.scroll-to-top:hover {
  background: var(--theme-color3);
  color: #ffffff;
}

.link-style-one {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  line-height: 20px;
  font-weight: 600;
  overflow: hidden;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  font-family: var(--title-font);
  color: var(--theme-color2);
}
.link-style-one:before {
  position: absolute;
  left: 0;
  right: 18px;
  bottom: 2px;
  height: 1px;
  background-color: var(--bg-theme-color1);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.link-style-one i {
  position: relative;
  top: 1px;
  display: block;
  font-size: 14px;
  margin-left: 5px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.link-style-one:hover {
  color: var(--theme-color1);
}
.link-style-one:hover:before {
  right: 100%;
}

/*=== List Style One ===*/
.list-style-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px 0;
}
.list-style-one li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
  color: #75767a;
  margin-right: 20px;
}
.list-style-one li i {
  position: relative;
  top: 1px;
  color: var(--theme-color1);
  font-size: 13px;
  line-height: 20px;
  margin-right: 10px;
}
.list-style-one li a {
  display: block;
  color: #848484;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-one li a:hover {
  color: var(--theme-color-light);
}

/*=== List Style Two ===*/
.list-style-two {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 30px;
}
.list-style-two li {
  position: relative;
  font-size: 20px;
  color: var(--theme-color2);
  line-height: 30px;
  font-weight: 400;
  letter-spacing: -0.02em;
  padding-left: 40px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  margin-bottom: 10px;
}
.list-style-two li i {
  position: absolute;
  left: 0px;
  top: 0px;
  color: var(--theme-color1);
  font-size: 20px;
  line-height: 30px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-two li a {
  display: inline-block;
  font-weight: inherit;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-two li a:hover {
  color: var(--theme-color1);
}
.list-style-two.light li {
  color: var(--theme-color-light);
}
.list-style-two.light li i {
  color: #42d9be;
}
.list-style-two.two-col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
.list-style-two.two-col li {
  max-width: 50%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  padding-right: 20px;
}
@media (max-width: 767.98px) {
  .list-style-two.two-col li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
  }
}

/*=== List Style Three ===*/
.list-style-three {
  position: relative;
}
.list-style-three li {
  position: relative;
  padding: 15px 30px;
  padding-left: 55px;
  font-size: 16px;
  line-height: 30px;
  font-weight: 700;
  color: var(--theme-color2);
  letter-spacing: -0.04em;
  background-color: #ebf1f5;
  margin-bottom: 8px;
}
.list-style-three li i {
  position: absolute;
  left: 30px;
  top: 15px;
  font-size: 15px;
  line-height: 30px;
  color: var(--theme-color1);
}
.list-style-three.dark li {
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 14px;
  line-height: 28px;
  color: #fff;
  font-weight: 400;
}
.list-style-three.dark li i {
  font-size: 12px;
}
.list-style-three.two-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.list-style-three.two-column li {
  max-width: 50%;
  margin-right: 30px;
}
@media (max-width: 575.98px) {
  .list-style-three.two-column li {
    max-width: 100%;
    margin-right: 0;
    width: 100%;
  }
}

/*=== List Style Four ===*/
.list-style-four {
  position: relative;
}
.list-style-four li {
  position: relative;
  font-size: 20px;
  line-height: 26px;
  font-weight: 600;
  color: #2e2d2d;
  padding-left: 45px;
  margin-bottom: 18px;
}
.list-style-four li:before {
  position: relative;
  height: 30px;
  width: 30px;
  position: absolute;
  left: 0;
  top: 0;
  content: "\f00c";
  color: #ffffff;
  text-align: center;
  font-size: 14px;
  line-height: 30px;
  font-weight: 700;
  font-family: "Font Awesome 6 Pro";
  background: var(--gradient-1);
  border-radius: 50%;
}
.list-style-four li a {
  display: inline-block;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-four li a:hover {
  color: #ffffff;
}

/*Social Icon One*/
.social-icon-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.social-icon-one li {
  position: relative;
  margin-left: 27px;
}
.social-icon-one li:first-child {
  margin-left: 0;
}
.social-icon-one li a {
  position: relative;
  display: block;
  line-height: 40px;
  height: 40px;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-one li a:hover {
  color: var(--theme-color1);
}

/*Social Icon Two*/
.social-icon-two {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.social-icon-two li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-right: 7px;
}
.social-icon-two li:last-child {
  margin-right: 0;
}
.social-icon-two li a {
  position: relative;
  display: block;
  height: 44px;
  width: 44px;
  line-height: 44px;
  border-radius: 50%;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
  background-color: #151518;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-two li a i {
  position: relative;
}
.social-icon-two li a::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  -webkit-transform: scale(0);
          transform: scale(0);
  background-color: var(--theme-color1);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  border-radius: 50px;
}
.social-icon-two li a:hover {
  color: #fff;
}
.social-icon-two li a:hover:before {
  -webkit-transform: scale(1);
          transform: scale(1);
}

/*Social Icon Three*/
.social-icon-three {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.social-icon-three li {
  position: relative;
  margin-right: 30px;
}
.social-icon-three li a {
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 24px;
  color: #ffffff;
  letter-spacing: 0.05em;
  background: transparent;
  font-weight: 400;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  font-family: var(--title-font);
}
.social-icon-three li a:before {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 2px;
  width: 0;
  background-color: #ffffff;
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-three li a:hover:before {
  left: 0;
  width: 100%;
}

/*Social Icon Four*/
.social-icon-four {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.social-icon-four li {
  position: relative;
  margin: 0 12px;
}
.social-icon-four li a {
  position: relative;
  display: block;
  font-size: 14px;
  line-height: 30px;
  color: var(--theme-color2);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-four li a:hover {
  color: var(--theme-color3);
}

/*Social Icon Five*/
.social-icon-five {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.social-icon-five li {
  position: relative;
  margin-right: 7px;
}
.social-icon-five li a {
  position: relative;
  display: block;
  height: 34px;
  width: 34px;
  border: 1px solid rgba(135, 140, 143, 0.32);
  font-size: 12px;
  text-align: center;
  line-height: 32px;
  color: #bcb5b5;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-five li a:hover {
  color: var(--theme-color3);
}

.bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.bg-pattern-1 {
  background-image: url(../images/icons/pattern-1.jpg);
}

.bg-pattern-2 {
  background-image: url(../images/icons/pattern-2.jpg);
}

.bg-pattern-3 {
  background-image: url(../images/icons/pattern-3.jpg);
}

.bg-pattern-4 {
  background-image: url(../images/icons/pattern-4.jpg);
}

.bg-pattern-5 {
  background-image: url(../images/icons/pattern-5.jpg);
}

.bg-pattern-6 {
  background-image: url(../images/icons/pattern-6.jpg);
}

.bg-pattern-7 {
  background-image: url(../images/icons/pattern-7.jpg);
}

.bg-pattern-8 {
  background-image: url(../images/icons/pattern-8.jpg);
}

.bg-pattern-9 {
  background-image: url(../images/icons/pattern-9.jpg);
}

.bg-pattern-10 {
  background-image: url(../images/icons/pattern-10.jpg);
}

.bg-pattern-11 {
  background-image: url(../images/icons/pattern-11.jpg);
}

.bg-pattern-12 {
  background-image: url(../images/icons/pattern-12.jpg);
}

.bg-pattern-13 {
  background-image: url(../images/icons/pattern-13.jpg);
}

.bg-pattern-14 {
  background-image: url(../images/icons/pattern-14.jpg);
}

.devider {
  position: absolute;
  padding: 0;
  margin: 0;
  width: 80%;
  left: 0;
  right: 0;
  top: 0;
  margin: 0 auto;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
@media (max-width: 1199.98px) {
  .devider {
    width: 100%;
  }
}

/*======================
    Tabs Box
======================*/
.tabs-box {
  position: relative;
}

.tabs-box .tab {
  display: none;
}

.tabs-box .active-tab {
  display: block;
}

.play-btn {
  position: relative;
  display: inline-block;
}
.play-btn:hover .icon {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
  -webkit-transform: rotate(-180deg) scale(-1);
          transform: rotate(-180deg) scale(-1);
}
.play-btn .icon {
  height: 110px;
  width: 110px;
  border-radius: 50%;
  font-size: 28px;
  color: var(--theme-color2);
  background-color: var(--bg-theme-color1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-btn .circle-text img {
  position: relative;
  display: inline-block;
  -webkit-animation: fa-spin 60s infinite linear;
          animation: fa-spin 60s infinite linear;
}

/*======================
    Media Play Button 
======================*/
.play-now {
  position: relative;
  display: block;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.play-now .icon {
  position: relative;
  display: inline-block;
  height: 85px;
  width: 85px;
  text-align: center;
  line-height: 85px;
  background-color: #ffffff;
  color: var(--bg-theme-color2);
  z-index: 1;
  font-size: 18px;
  display: block;
  border-radius: 50%;
  -webkit-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
          box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -webkit-transform-origin: center;
          transform-origin: center;
}

.play-now .ripple,
.play-now .ripple:before,
.play-now .ripple:after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
          box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
          animation: ripple 3s infinite;
}
.play-now .ripple.light,
.play-now .ripple:before.light,
.play-now .ripple:after.light {
  -webkit-box-shadow: 0 0 0 0 rgb(255, 255, 255);
          box-shadow: 0 0 0 0 rgb(255, 255, 255);
}

.play-now .ripple.light,
.play-now .ripple.light:before,
.play-now .ripple.light:after {
  -webkit-box-shadow: 0 0 0 0 rgb(255, 255, 255);
          box-shadow: 0 0 0 0 rgb(255, 255, 255);
}

.play-now .ripple:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}

.play-now .ripple:after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

@-webkit-keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 50px rgba(233, 35, 47, 0);
    box-shadow: 0 0 0 50px rgba(233, 35, 47, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(233, 35, 47, 0);
    box-shadow: 0 0 0 0 rgba(233, 35, 47, 0);
  }
}

@keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 50px rgba(233, 35, 47, 0);
    box-shadow: 0 0 0 50px rgba(233, 35, 47, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(233, 35, 47, 0);
    box-shadow: 0 0 0 0 rgba(233, 35, 47, 0);
  }
}
.play-now-two {
  height: 150px;
  width: 150px;
  background-color: rgba(21, 21, 21, 0.5);
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 34px;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  -webkit-animation: zoom-one 3s infinite linear;
          animation: zoom-one 3s infinite linear;
}
.play-now-two:hover {
  color: #ff9205;
  background-color: #ffffff;
}

/*========================
  Select2 Dropdown Plugin
========================*/
.select2-dropdown {
  border: 1px solid #eee;
}

.select2-results__option {
  padding: 0 10px;
  color: #7c858c;
  border: 0;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #7c858c;
  padding-left: 0;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--bg-theme-color1);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  height: 30px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  bottom: 1px;
  height: auto;
  width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 12px;
  color: #7c858c;
}

.select2-container--default .select2-selection--single .select2-selection__arrow:before {
  position: relative;
  content: "\f107";
  font-family: "Font Awesome 6 Pro";
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.select2-container--default .select2-selection--single {
  border-radius: 0;
}

.default-navs .owl-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.default-navs .owl-next,
.default-navs .owl-prev {
  display: block;
  width: 52px;
  height: 52px;
  font-size: 16px;
  font-weight: 700;
  line-height: 52px;
  color: var(--theme-color2);
  background: #fff;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 50%;
  margin-right: 10px;
}
.default-navs .owl-next:hover,
.default-navs .owl-prev:hover {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}

.default-dots .owl-dots {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.default-dots .owl-dots .owl-dot {
  height: 5px;
  width: 40px;
  margin: 0 5px;
  border: 1px solid #d7d7d7;
}
@media (max-width: 575.98px) {
  .default-dots .owl-dots .owl-dot {
    width: 20px;
  }
}
.default-dots .owl-dots .owl-dot.active {
  background-color: var(--theme-color1);
  border: 1px solid var(--theme-color1);
}

/*==========================
	Nav Style One
===========================*/
.nav-style-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 9;
}
.nav-style-one .next,
.nav-style-one .prev {
  margin-right: 38px;
  padding: 18px 25px;
  padding-left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.nav-style-one .next .icon,
.nav-style-one .prev .icon {
  position: relative;
  display: inline-block;
}
.nav-style-one .next .icon:before,
.nav-style-one .prev .icon:before {
  position: absolute;
  top: 0;
  margin-top: -20px;
  right: -25px;
  height: 52px;
  width: 52px;
  border: 1px dotted #fff;
  border-radius: 100px;
  content: "";
  z-index: -1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.nav-style-one .next:hover .icon:before,
.nav-style-one .prev:hover .icon:before {
  background-color: rgba(255, 255, 255, 0.15);
  width: 125px;
}
.nav-style-one .prev {
  margin-right: 0;
  padding-left: 25px;
  padding-right: 0;
}
.nav-style-one .prev .icon:before {
  left: -25px;
  right: auto;
}
.nav-style-one .swiper-button-disabled {
  opacity: 0.5;
  pointer-events: none;
}
.nav-style-one.dark .next .icon:before,
.nav-style-one.dark .prev .icon:before {
  border: 1px solid #797979;
}

/*==========================
	Default Tabs
===========================*/
.default-tabs {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

.default-tabs .tab-buttons {
  position: relative;
  margin-bottom: 30px;
}

.default-tabs .tab-buttons li {
  position: relative;
  float: left;
  font-weight: 600;
  font-size: 18px;
  padding: 15px 35px;
  color: var(--theme-color1);
  line-height: 20px;
  border-radius: 5px;
  background-color: #ffffff;
  cursor: pointer;
  margin-right: 20px;
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.default-tabs .tab-buttons li:last-child {
  margin-right: 0;
}

.default-tabs .tab-buttons li.active-btn {
  background: var(--gradient-1);
  color: #ffffff;
}

.default-tabs .tabs-content {
  position: relative;
  width: 100%;
}

.blockquote-style-one {
  position: relative;
  font-size: 16px;
  line-height: 24px;
  color: var(--theme-color2);
  padding: 13px 25px;
  background-color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-family: var(--title-font);
  font-weight: 800;
  margin-bottom: 30px;
  -webkit-box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}
.blockquote-style-one:before {
  position: absolute;
  left: 0;
  top: 10px;
  bottom: 10px;
  width: 4px;
  z-index: 2;
  background-color: var(--theme-color1);
  content: "";
}

/*================================
    Progress Bar
=================================*/
.skills {
  position: relative;
  margin-bottom: 50px;
}
.skills .skill-item {
  position: relative;
  margin-bottom: 25px;
}
.skills .skill-item:last-child {
  margin-bottom: 0px;
}
.skills .skill-item .skill-header {
  position: relative;
  margin-bottom: 0px;
}
.skills .skill-item .skill-header .skill-title {
  font-weight: 600;
  color: var(--theme-color2);
  letter-spacing: 0;
  margin-bottom: 10px;
}
.skills .skill-item .skill-bar {
  position: relative;
  width: 100%;
  height: 14px;
  border-radius: 10px;
}
.skills .skill-item .skill-bar .bar-inner {
  position: relative;
  width: 100%;
  height: 14px;
  background: #f2f3f6;
  border-radius: 10px;
}
.skills .skill-item .skill-bar .bar-inner .bar {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 14px;
  width: 0px;
  -webkit-transition: all 3000ms ease;
  transition: all 3000ms ease;
  border-radius: 10px;
  background: var(--bg-theme-color1);
}
.skills .skill-item .skill-bar .bar-inner .skill-percentage {
  position: absolute;
  right: 0;
  bottom: 100%;
  font-weight: 400;
  color: #6f7174;
  line-height: 25px;
  margin-bottom: 10px;
}

.default-navs .owl-next,
.default-navs .owl-prev {
  display: block;
  margin: 0px 5px;
  height: 54px;
  width: 54px;
  color: var(--theme-color1);
  background-color: #ffffff;
  border-radius: 50%;
  font-size: 16px;
  line-height: 54px;
  font-weight: 700;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.default-navs .owl-next:hover,
.default-navs .owl-prev:hover {
  background: var(--bg-theme-color1);
  color: var(--theme-color2);
}

.default-dots .owl-dots {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 18px 45px;
  background-color: var(--bg-theme-color4);
  border-radius: 50px;
  margin-top: 30px;
}
.default-dots .owl-dots .owl-dot {
  height: 4px;
  width: 10px;
  border-radius: 5px;
  background-color: var(--bg-theme-color2);
  margin: 0 5px;
  opacity: 0.25;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.default-dots .owl-dots .owl-dot.active {
  opacity: 1;
  width: 46px;
  background: var(--gradient-1);
}

.theme-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-family: var(--title-font);
}
.theme-btn .btn-title {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

/*Btn Style One*/
.btn-style-one {
  position: relative;
  font-size: 15px;
  line-height: 20px;
  padding: 20px 50px;
  font-weight: 600;
  overflow: hidden;
  z-index: 0;
  color: var(--theme-color2);
  background: var(--theme-color1);
}
.btn-style-one:before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: auto;
  width: 7px;
  height: 100%;
  background: var(--theme-color2);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  opacity: 0.15;
  content: "";
  z-index: -1;
}
.btn-style-one i {
  position: relative;
  top: 1px;
  display: block;
  margin-left: 10px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.btn-style-one:hover:before {
  width: 100%;
  opacity: 1;
}
.btn-style-one:hover {
  color: #ffffff;
}
.btn-style-one.dark-bg {
  background-color: var(--theme-color2);
}
.btn-style-one.dark-bg::before {
  background-color: var(--bg-theme-color1);
}
.btn-style-one.light-bg {
  background-color: var(--theme-color-light);
  color: var(--theme-color2);
}
.btn-style-one.light-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-one.light-bg::before {
  background-color: var(--theme-color2);
  opacity: 0.4;
}
.btn-style-one.hover-light:hover {
  color: var(--theme-color2);
}
.btn-style-one.hover-light:before {
  background-color: var(--theme-color-light);
}

.theme-btn.small {
  padding: 10px 30px;
  line-height: 20px;
  font-size: 10px;
}

/*** 

====================================================================
  Anim Icons
====================================================================

***/
.anim-icons {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  max-width: 1170px;
  margin: 0 auto;
}

[text-split] {
  opacity: 0;
}

.word {
  overflow: hidden;
  padding-bottom: 0.1em;
  margin-bottom: -0.1em;
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
}

.anim-icons.full-width {
  max-width: 100%;
}

.anim-icons .icon {
  position: absolute;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
}

.icon-circle {
  height: 250px;
  width: 250px;
  background-image: url(../images/icons/icon-circle.png);
}

.icon-dots {
  height: 385px;
  width: 103px;
  background-image: url(../images/icons/icon-dots.png);
}

.icon-dots2 {
  height: 141px;
  width: 145px;
  background-image: url(../images/icons/icon-dots2.png);
}

.icon-line {
  width: 105px;
  height: 124px;
  background-image: url(../images/icons/icon-line.png);
}

.icon-line2 {
  width: 107px;
  height: 44px;
  background-image: url(../images/icons/icon-line2.png);
}

.icon-line3 {
  width: 77px;
  height: 103px;
  background-image: url(../images/icons/icon-line3.png);
}

.icon-line4 {
  width: 121px;
  height: 160px;
  background-image: url(../images/icons/icon-line4.png);
}

.icon-line5 {
  width: 415px;
  height: 352px;
  background-image: url(../images/icons/icon-line5.png);
}

.icon-arrow1 {
  width: 93px;
  height: 97px;
  background-image: url(../images/icons/icon-arrow1.png);
}

.icon-arrow2 {
  width: 108px;
  height: 77px;
  background-image: url(../images/icons/icon-arrow2.png);
}

.icon-arrow3 {
  width: 45px;
  height: 62px;
  background-image: url(../images/icons/icon-arrow3.png);
}

.icon-speaker {
  width: 160px;
  height: 277px;
  background-image: url(../images/icons/icon-speaker.png);
}

.bounce-y {
  -webkit-animation: bounce-y 10s infinite linear;
          animation: bounce-y 10s infinite linear;
}

.bounce-x {
  -webkit-animation: bounce-x 10s infinite linear;
          animation: bounce-x 10s infinite linear;
}

.zoom-one {
  -webkit-animation: zoom-one 10s infinite linear;
          animation: zoom-one 10s infinite linear;
}

.zoom-two {
  -webkit-animation: zoom-two 5s infinite linear;
          animation: zoom-two 5s infinite linear;
}

@-webkit-keyframes float {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

@keyframes float {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes bounce-y {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes bounce-y {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
            transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
            transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@-webkit-keyframes zoom-one {
  0% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
}
@keyframes zoom-one {
  0% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
}
@-webkit-keyframes zoom-two {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes zoom-two {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.overlay-anim {
  position: relative;
}
.overlay-anim:after {
  background: rgba(255, 255, 255, 0.3);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 0;
  opacity: 1;
  z-index: 9;
  pointer-events: none;
}
.overlay-anim:hover:after {
  height: 100%;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}

.circle {
  position: fixed;
  width: 10px;
  height: 10px;
  left: -10px;
  top: -10px;
  border-radius: 100%;
  z-index: 1;
  pointer-events: none;
  z-index: 10000;
  -webkit-transform: scale(1);
          transform: scale(1);
}
@supports (mix-blend-mode: difference) {
  .circle {
    background-color: white;
    mix-blend-mode: difference;
  }
}
@media only screen and (max-width: 1023px) {
  .circle {
    display: none !important;
  }
}

.circle-follow {
  position: fixed;
  mix-blend-mode: difference;
  width: 30px;
  height: 30px;
  left: -21px;
  top: -21px;
  border-radius: 100%;
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
  z-index: 10000;
  -webkit-transform: scale(1);
          transform: scale(1);
}
@supports (mix-blend-mode: difference) {
  .circle-follow {
    border: 1px solid #fff;
    mix-blend-mode: difference;
  }
}
@media only screen and (max-width: 1023px) {
  .circle-follow {
    display: none !important;
  }
}

/*** 

====================================================================
  Search Popup
====================================================================

***/
.search-popup {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  overflow: hidden;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.search-popup .search-back-drop {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--theme-color2);
  opacity: 0.95;
}
.search-popup .close-search {
  position: absolute;
  top: 30px;
  right: 30px;
  font-size: 26px;
  color: var(--theme-color-light);
  z-index: 3;
  border-radius: 50%;
  background-color: transparent;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .search-inner {
  position: relative;
  display: block;
  top: 40%;
  height: auto;
  z-index: 1;
  width: calc(100% - 60px);
  max-width: 800px;
  margin: auto;
  opacity: 0;
  -webkit-transform: translateY(-50px);
          transform: translateY(-50px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.search-popup .form-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.search-popup .form-group input[type=search],
.search-popup .form-group input[type=text] {
  position: relative;
  display: block;
  line-height: 20px;
  font-size: 16px;
  width: 100%;
  height: 50px;
  border: 1px solid #e1e6dc;
  padding: 15px 20px;
  color: #707070;
  background: #ffffff;
  border-radius: 5px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .form-group input[type=search]:focus,
.search-popup .form-group input[type=text]:focus {
  border-color: var(--border-theme-color2);
}
.search-popup .form-group button {
  position: absolute;
  right: 5px;
  top: 5px;
  height: 40px;
  width: 40px;
  display: block;
  font-size: 18px;
  color: var(--theme-color2);
  line-height: 40px;
  border-radius: 5px;
  font-weight: normal;
  background: #ffffff;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .form-group button:hover {
  color: var(--theme-color2);
}
.search-popup textarea::-webkit-input-placeholder, .search-popup input::-webkit-input-placeholder {
  color: inherit;
}
.search-popup textarea::-moz-placeholder, .search-popup input::-moz-placeholder {
  color: inherit;
}
.search-popup textarea:-ms-input-placeholder, .search-popup input:-ms-input-placeholder {
  color: inherit;
}
.search-popup textarea::-ms-input-placeholder, .search-popup input::-ms-input-placeholder {
  color: inherit;
}
.search-popup textarea::placeholder,
.search-popup input::placeholder {
  color: inherit;
}

.moblie-search-active .search-popup {
  opacity: 1;
  visibility: visible;
  -webkit-transform: scale(1);
          transform: scale(1);
  border-radius: 0%;
}
.moblie-search-active .search-popup .search-inner {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
}

/*** 

====================================================================
Main Header
====================================================================

***/
.header-span {
  position: relative;
  height: 110px;
  display: block;
  width: 100%;
}

.main-header {
  position: relative;
  width: 100%;
  z-index: 999;
}

.header-top {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.header-top .inner-container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  padding: 0 80px;
}
@media (max-width: 1399.98px) {
  .header-top .inner-container {
    padding: 0 20px;
  }
}
.header-top .top-left {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.header-top .top-right {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.header-top .top-right .social-icon-one {
  margin-left: 41px;
}
.header-top .useful-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 12px 0;
}
.header-top .useful-links li {
  position: relative;
  padding-left: 15px;
  margin-left: 15px;
  font-size: 12px;
  color: #75767a;
  line-height: 20px;
}
.header-top .useful-links li:first-child {
  margin-left: 0;
  padding-left: 0;
}
.header-top .useful-links li:first-child:before {
  display: none;
}
.header-top .useful-links li:before {
  position: absolute;
  left: -3px;
  top: 0;
  content: "/";
}
.header-top .useful-links li a {
  color: #808287;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-top .useful-links li a:hover {
  color: #ffffff;
}

.main-header .header-lower {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-header .header-lower .logo-box {
  position: relative;
  padding: 20px 0;
}

.main-header {
  width: 100%;
  z-index: 999;
}
.main-header .logo {
  position: relative;
  display: block;
}
.main-header .logo img {
  max-width: 100%;
  height: auto;
}
.main-header .main-box {
  position: relative;
  left: 0px;
  top: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-header .main-box .nav-outer {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}

.main-menu {
  position: relative;
}
@media (max-width: 991.98px) {
  .main-menu {
    display: none;
  }
}

.main-menu .navbar-header {
  display: none;
}

.main-menu .navbar-collapse {
  padding: 0px;
}

.main-menu .navigation {
  position: relative;
  margin: 0px;
}

.main-menu .navigation > li {
  position: relative;
  float: left;
  padding: 25px 0px;
  margin-right: 70px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li:before {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: 10px;
  height: 1px;
  width: 0%;
  background-color: var(--bg-theme-color1);
  z-index: 3;
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li:last-child {
  margin-right: 0;
}
.main-menu .navigation > li:hover:before, .main-menu .navigation > li.current:before {
  left: 0;
  width: 100%;
}
.main-menu .navigation > li > a {
  position: relative;
  display: block;
  text-align: center;
  opacity: 1;
  color: #ffffff;
  font-size: 18px;
  line-height: 30px;
  font-weight: 300;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li > a .icon {
  position: relative;
  font-size: 22px;
  line-height: 24px;
  margin-left: 10px;
}
.main-menu .navigation > li > a:hover {
  color: #ffffff;
}
.main-menu .navigation > li.current > a {
  color: var(--theme-color-light);
  font-weight: 500;
}
.main-menu .navigation > li.dropdown > a {
  padding-right: 14px;
  margin-right: -14px;
}
.main-menu .navigation > li.dropdown > a:after {
  content: "\f107";
  position: absolute;
  right: 0;
  top: 50%;
  width: 10px;
  height: 20px;
  display: block;
  line-height: 24px;
  font-size: 12px;
  z-index: 5;
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  margin-top: -2px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.main-menu .navigation > li.dropdown:hover > ul {
  visibility: visible;
  opacity: 1;
  top: 100%;
  margin-top: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-menu .navigation > li > ul {
  position: absolute;
  left: 0px;
  top: 100%;
  width: 220px;
  z-index: 100;
  padding: 10px 0 0;
  background-color: #ffffff;
  margin-top: 30px;
  opacity: 0;
  display: none;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
          box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
}
.main-menu .navigation > li > ul.from-right {
  left: auto;
  right: 0px;
}
.main-menu .navigation > li > ul > li {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #ebf1f5;
}
.main-menu .navigation > li > ul > li:last-child {
  border-bottom: none;
}
.main-menu .navigation > li > ul > li > a {
  position: relative;
  display: block;
  padding: 10px 0px;
  line-height: 29px;
  font-weight: 400;
  font-size: 16px;
  color: var(--theme-color2);
  text-align: left;
  margin: 0 30px;
  text-transform: capitalize;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.main-menu .navigation > li > ul > li:hover > a {
  color: var(--theme-color1);
}
.main-menu .navigation > li > ul > li.dropdown > a:after {
  font-family: "Font Awesome 6 Pro";
  content: "\f105";
  position: absolute;
  right: 0;
  top: 11px;
  display: block;
  line-height: 24px;
  font-size: 14px;
  font-weight: 900;
  z-index: 5;
}
.main-menu .navigation > li > ul > li.dropdown:hover > ul {
  visibility: visible;
  opacity: 1;
  top: 0px;
  margin-top: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-menu .navigation > li > ul > li > ul {
  position: absolute;
  left: 100%;
  top: 0px;
  width: 220px;
  z-index: 100;
  display: none;
  background-color: #ffffff;
  opacity: 0;
  padding: 10px 0 0;
  margin-top: 10px;
  -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
  -webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
          box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
}
.main-menu .navigation > li > ul > li > ul > li {
  position: relative;
  border-bottom: 1px solid #ebf1f5;
  width: 100%;
}
.main-menu .navigation > li > ul > li > ul > li:last-child {
  border-bottom: none;
}
.main-menu .navigation > li > ul > li > ul > li > a {
  position: relative;
  display: block;
  padding: 10px 0;
  line-height: 24px;
  font-weight: 400;
  font-size: 16px;
  color: var(--theme-color2);
  text-align: left;
  margin: 0 30px;
  text-transform: capitalize;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li > ul > li > ul > li > a:hover {
  color: var(--theme-color1);
}

.main-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: 10px;
  top: 8px;
  width: 34px;
  height: 30px;
  border: 1px solid #ffffff;
  text-align: center;
  font-size: 16px;
  line-height: 26px;
  color: #ffffff;
  cursor: pointer;
  z-index: 5;
  display: none;
}

.main-header .outer-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
}
.main-header .outer-box .theme-btn {
  margin-left: 40px;
  font-size: 12px;
  text-transform: uppercase;
}

.main-header .ui-btn-outer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 6px 0;
  border-left: 1px solid rgba(255, 255, 255, 0.5);
  padding-left: 17px;
  position: relative;
  right: 117px;
}

.main-header .ui-btn {
  position: relative;
  display: block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  text-align: center;
  background: none;
  font-size: 26px;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-header .ui-btn:hover {
  color: var(--theme-color1);
}

.main-header .info-btn {
  position: relative;
  font-size: 16px;
  padding-left: 44px;
  color: #ffffff;
  text-align: left;
  font-weight: 500;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 40px;
}
.main-header .info-btn small {
  display: block;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--theme-color1);
}
.main-header .info-btn .icon {
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -12px;
  line-height: 40px;
  height: 40px;
  font-size: 24px;
  line-height: 1em;
  margin-right: 20px;
  padding-right: 20px;
}
.main-header .info-btn:hover {
  color: var(--theme-color-light);
}

/*** 

====================================================================
Header Style One
====================================================================

***/
.header-style-one {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}
.header-style-one .header-top {
  margin-left: 260px;
}
@media (max-width: 1399.98px) {
  .header-style-one .header-top {
    margin-left: 0;
  }
}
@media (max-width: 991.98px) {
  .header-style-one .header-top {
    display: none;
  }
}
.header-style-one .header-top .inner-container {
  padding-left: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  border-left: 1px solid rgba(255, 255, 255, 0.06);
}
@media (max-width: 1399.98px) {
  .header-style-one .header-top .inner-container {
    padding-left: 20px;
  }
}
.header-style-one .header-lower {
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}
@media (max-width: 991.98px) {
  .header-style-one .header-lower {
    background-color: rgba(0, 0, 0, 0.5);
  }
}
.header-style-one .header-lower .logo-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 260px;
  height: 134px;
  margin-top: -44px;
  margin-right: 40px;
  padding-left: 60px;
}
@media (max-width: 1399.98px) {
  .header-style-one .header-lower .logo-box {
    margin-top: 0;
    height: auto;
    padding-left: 20px;
    min-width: 180px;
  }
}
.header-style-one .main-box {
  padding-right: 80px;
}
@media (max-width: 1399.98px) {
  .header-style-one .main-box {
    padding-right: 20px;
  }
}
@media only screen and (max-width: 1699px) {
  .header-style-one .main-box .main-menu .navigation > li {
    margin-right: 45px;
  }
  .header-style-one .main-box .main-menu .navigation > li:last-child {
    margin-right: 0;
  }
}
@media (max-width: 1399.98px) {
  .header-style-one .main-box .outer-box .info-btn {
    display: none;
  }
}
.header-style-one .main-box .outer-box .search-btn {
  padding: 27px 40px;
  padding-left: 0;
  height: auto;
  width: auto;
  line-height: 30px;
  border-right: 1px solid rgba(255, 255, 255, 0.06);
}
@media (max-width: 1399.98px) {
  .header-style-one .main-box .outer-box .search-btn {
    border-radius: 0;
    padding-right: 0;
  }
}

/*** 

====================================================================
    Header Style Two
====================================================================

***/
.header-style-two {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}
.header-style-two .header-top {
  background-color: var(--theme-color2);
}
.header-style-two .header-top .inner-container {
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  border-left: 1px solid rgba(255, 255, 255, 0.06);
}
@media (max-width: 991.98px) {
  .header-style-two .header-top {
    display: none;
  }
}
.header-style-two .header-lower {
  background-color: rgba(24, 25, 28, 0.2);
}
@media (max-width: 991.98px) {
  .header-style-two .header-lower {
    background-color: rgba(0, 0, 0, 0.5);
  }
}
.header-style-two .header-lower .logo-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 327px;
  height: 100px;
  margin-right: 40px;
  padding-left: 80px;
}
@media only screen and (max-width: 1699px) {
  .header-style-two .header-lower .logo-box {
    min-width: 280px;
  }
}
@media (max-width: 1399.98px) {
  .header-style-two .header-lower .logo-box {
    padding-left: 20px;
    min-width: 200px;
    height: auto;
  }
}
.header-style-two .main-box {
  padding-right: 80px;
}
@media (max-width: 1399.98px) {
  .header-style-two .main-box {
    padding-right: 20px;
  }
}
@media only screen and (max-width: 1699px) {
  .header-style-two .main-box .main-menu .navigation > li {
    margin-right: 50px;
  }
  .header-style-two .main-box .main-menu .navigation > li:last-child {
    margin-right: 0;
  }
}
@media (max-width: 1399.98px) {
  .header-style-two .main-box .outer-box .info-btn {
    display: none;
  }
}
.header-style-two .main-box .outer-box .search-btn {
  padding: 27px 40px;
  padding-left: 0;
  height: auto;
  width: auto;
  line-height: 30px;
  border-right: 1px solid rgba(255, 255, 255, 0.06);
}
@media (max-width: 1399.98px) {
  .header-style-two .main-box .outer-box .search-btn {
    padding-right: 0;
  }
}

/*** 

====================================================================
Sticky Header
====================================================================

***/
.sticky-header {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  left: 0px;
  top: 0px;
  width: 100%;
  padding: 0px 0px;
  z-index: 99999;
  background: #ffffff;
  -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.sticky-header.fixed-header {
  opacity: 1;
  z-index: 9999;
  visibility: visible;
}

.sticky-header .logo {
  padding: 10px 0;
}
.sticky-header .logo img {
  max-height: 40px;
}
.sticky-header .nav-outer {
  position: relative;
  background: none;
}
.sticky-header .inner-container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.sticky-header .main-menu .navigation > li {
  margin: 0;
  margin-left: 60px;
  padding: 20px 0;
}

.sticky-header .main-menu .navigation > li > a {
  color: var(--theme-color2);
}

.sticky-header .main-menu .navigation > li.current > a,
.sticky-header .main-menu .navigation > li:hover > a {
  color: var(--theme-color2);
}

.sticky-header .outer-box,
.sticky-header .navbar-header {
  display: none;
}

.sticky-header .mobile-nav-toggler {
  color: var(--theme-color2);
}

@media only screen and (min-width: 768px) {
  .main-menu .navigation > li > ul,
  .main-menu .navigation > li > ul > li > ul {
    display: block !important;
    visibility: hidden;
    opacity: 0;
  }
}
/*** 

====================================================================
      Mobile Menu
====================================================================

***/
.mobile-nav-toggler {
  position: relative;
  font-size: 28px;
  line-height: 20px;
  cursor: pointer;
  color: var(--theme-color1);
  display: none;
  top: -3px;
  margin-left: 20px;
  -webkit-box-ordinal-group: 9;
      -ms-flex-order: 8;
          order: 8;
}
@media (max-width: 991.98px) {
  .mobile-nav-toggler {
    display: block;
  }
}

.mobile-menu {
  position: fixed;
  right: 0;
  top: 0;
  width: 300px;
  max-width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  z-index: 999999;
}
.mobile-menu .menu-backdrop {
  position: fixed;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  visibility: hidden;
}
.mobile-menu .upper-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  padding: 20px 20px;
}
.mobile-menu .close-btn {
  position: relative;
  top: -10px;
  text-align: center;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  z-index: 10;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  -webkit-transform: translateY(-50px);
          transform: translateY(-50px);
}
.mobile-menu .close-btn:hover {
  opacity: 0.5;
}
.mobile-menu .nav-logo {
  position: relative;
  text-align: left;
  width: 100%;
}
.mobile-menu .nav-logo img {
  max-height: 40px;
}
.mobile-menu .menu-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  background: var(--theme-color2);
  padding: 0px 0px;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  border-radius: 0px;
  -webkit-transform: translateX(101%);
          transform: translateX(101%);
}

.mobile-menu-visible {
  overflow: hidden;
}
.mobile-menu-visible .mobile-menu {
  opacity: 1;
  visibility: visible;
}
.mobile-menu-visible .mobile-menu .menu-backdrop {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.mobile-menu-visible .mobile-menu .menu-box {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.4s ease 200ms;
  transition: all 0.4s ease 200ms;
  -webkit-transform: translateX(0%);
          transform: translateX(0%);
}
.mobile-menu-visible .mobile-menu .close-btn {
  -webkit-transform: translateY(0px);
          transform: translateY(0px);
}
.mobile-menu-visible .scroll-to-top {
  opacity: 0;
  visibility: hidden;
}

.mobile-menu .navigation {
  position: relative;
  display: block;
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li {
  position: relative;
  display: block;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li > ul > li:last-child {
  border-bottom: none;
}
.mobile-menu .navigation li > ul > li:first-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li > a {
  position: relative;
  display: block;
  line-height: 24px;
  padding: 10px 20px;
  font-size: 16px;
  color: #ffffff;
  font-weight: 400;
  text-transform: capitalize;
}
.mobile-menu .navigation li:hover > a, .mobile-menu .navigation li.current > a {
  color: #ffffff;
}
.mobile-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 44px;
  height: 44px;
  text-align: center;
  font-size: 16px;
  line-height: 44px;
  color: #ffffff;
  cursor: pointer;
  z-index: 5;
}
.mobile-menu .navigation li.dropdown .dropdown-btn:after {
  content: "";
  position: absolute;
  left: 0px;
  top: 10px;
  width: 1px;
  height: 24px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li.dropdown .dropdown-btn.active i:before {
  content: "\f106";
}

.mobile-menu .navigation li > ul,
.mobile-menu .navigation li > ul > li > ul {
  display: none;
}

.mobile-menu .navigation li > ul > li {
  padding-left: 20px;
}

.contact-list-one {
  position: relative;
  padding: 30px 20px 20px;
}
.contact-list-one li {
  position: relative;
  margin-bottom: 20px;
}
.contact-list-one li:last-child {
  margin-right: 0;
}
.contact-list-one li .contact-info-box {
  position: relative;
  padding-left: 54px;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
}
.contact-list-one li .contact-info-box a {
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-list-one li .contact-info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 34px;
  line-height: 50px;
  color: #ffffff;
}
.contact-list-one li .contact-info-box .title {
  display: block;
  font-size: 12px;
  color: #b2c1c0;
  font-weight: 400;
  text-transform: uppercase;
}

.mobile-menu .social-links {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background: var(--theme-color2);
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}
.mobile-menu .social-links li {
  position: relative;
  text-align: center;
  width: 100%;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .social-links li a {
  position: relative;
  display: block;
  line-height: 50px;
  height: 50px;
  font-size: 14px;
  text-align: center;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.mobile-menu .social-links li a:hover {
  color: var(--theme-color2);
}

/*** 

====================================================================
Section Title
====================================================================

***/
.sec-title {
  position: relative;
  margin-bottom: 50px;
}
.sec-title .sub-title {
  position: relative;
  display: block;
  font-size: var(--sec-title-subtitle-font-size);
  line-height: var(--sec-title-subtitle-line-height);
  font-weight: var(--sec-title-subtitle-font-weight);
  font-family: var(--sec-title-subtitle-font-family);
  color: var(--sec-title-subtitle-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 5px;
}
.sec-title h1 {
  position: relative;
  font-size: var(--h1-font-size);
  line-height: 1em;
  letter-spacing: -0.08em;
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .sec-title h1 {
    font-size: 84px;
  }
}
@media (max-width: 991.98px) {
  .sec-title h1 {
    font-size: 72px;
  }
}
@media (max-width: 767.98px) {
  .sec-title h1 {
    font-size: 68px;
  }
}
@media (max-width: 575.98px) {
  .sec-title h1 {
    font-size: 54px;
  }
}
.sec-title h2 {
  position: relative;
  font-size: var(--sec-title-font-size);
  color: var(--sec-title-color);
  font-family: var(--sec-title-font-family);
  font-weight: var(--sec-title-font-weight);
  letter-spacing: -0.04em;
  line-height: 1.2em;
  margin-bottom: 0;
  z-index: 0;
}
@media (max-width: 575.98px) {
  .sec-title h2 {
    font-size: 36px;
  }
  .sec-title h2 br {
    display: none;
  }
}
.sec-title h2:before {
  position: absolute;
  left: 5px;
  top: 32px;
  height: 30px;
  width: 38px;
  border-radius: 5px 5px 5px 0;
  background-color: var(--bg-theme-color1);
  -webkit-transform: skew(-15deg);
          transform: skew(-15deg);
  z-index: -1;
  content: "";
}
@media (max-width: 575.98px) {
  .sec-title h2:before {
    height: 20px;
    width: 28px;
    top: 25px;
  }
}
.sec-title .text {
  margin-top: 30px;
}
.sec-title.light .text {
  color: #75767a;
}
.sec-title.light h2,
.sec-title.light h1 {
  color: #fff;
}
.sec-title.text-center h2:before {
  left: 50%;
  margin-left: -15px;
}

/*** 

====================================================================
  Main Slider
====================================================================

***/
.main-slider {
  position: relative;
  text-align: center;
}
.main-slider h1 {
  position: relative;
  display: block;
  font-size: 100px;
  line-height: 1em;
  color: #ffffff;
  letter-spacing: -0.04em;
  z-index: 3;
  padding: 30px 0 0;
}
.main-slider h1 .selected {
  position: relative;
}
.main-slider h1 .selected:after {
  position: absolute;
  left: -22px;
  top: 12px;
  width: 364px;
  height: 117px;
  background-image: url(../images/icons/icon-circle2.png);
  content: "";
}
.main-slider h1 .selected:before {
  position: absolute;
  left: 10px;
  top: -10px;
  z-index: -1;
  width: 364px;
  height: 118px;
  background-image: url(../images/icons/icon-circle3.png);
  content: "";
}
@media (max-width: 1399.98px) {
  .main-slider h1 {
    font-size: 80px;
    padding-top: 0;
  }
  .main-slider h1 .selected::before, .main-slider h1 .selected:after {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .main-slider h1 {
    font-size: 72px;
  }
}
@media (max-width: 767.98px) {
  .main-slider h1 {
    font-size: 64px;
  }
}
@media (max-width: 575.98px) {
  .main-slider h1 {
    font-size: 52px;
  }
}
@media only screen and (max-width: 576px) {
  .main-slider h1 {
    font-size: 46px;
  }
}
@media only screen and (max-width: 424px) {
  .main-slider h1 {
    font-size: 36px;
  }
}
@media (max-width: 575.98px) {
  .main-slider .theme-btn {
    line-height: 20px;
    padding: 10px 30px;
  }
}

/*** 

====================================================================
    Banner Section
====================================================================

***/
.banner-section {
  position: relative;
  overflow: hidden;
}
.banner-section .slide-item {
  position: relative;
  min-height: 840px;
}
@media (max-width: 767.98px) {
  .banner-section .slide-item {
    min-height: auto;
  }
}
.banner-section .slide-item .bg-image {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.banner-section .slide-item .bg-image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url(../images/main-slider/shape.png);
  background-color: rgba(24, 25, 28, 0.7);
  content: "";
}
.banner-section .content-box {
  position: relative;
  padding: 300px 0 120px;
}
@media (max-width: 767.98px) {
  .banner-section .content-box {
    padding: 200px 0 150px;
    text-align: center;
  }
}
.banner-section .content-box .title {
  font-size: 80px;
  line-height: 90px;
  color: var(--theme-color-light);
  font-weight: 600;
  letter-spacing: 0;
  margin-bottom: 60px;
}
@media (max-width: 991.98px) {
  .banner-section .content-box .title {
    font-size: 72px;
    line-height: 1.2em;
  }
}
@media (max-width: 767.98px) {
  .banner-section .content-box .title {
    font-size: 62px;
  }
}
@media (max-width: 575.98px) {
  .banner-section .content-box .title {
    font-size: 36px;
    margin-bottom: 30px;
  }
}
.banner-section .content-box .btn-box .theme-btn {
  margin: 0 5px;
}
@media (max-width: 575.98px) {
  .banner-section .content-box .btn-box .theme-btn {
    font-size: 14px;
    line-height: 20px;
    padding: 14px 30px;
  }
}

/* Animate 1 */
.owl-carousel .animate-7,
.owl-carousel .animate-6,
.owl-carousel .animate-5,
.owl-carousel .animate-4,
.owl-carousel .animate-3,
.owl-carousel .animate-2,
.owl-carousel .animate-1 {
  opacity: 0;
  -webkit-transform: translateY(100px);
          transform: translateY(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .animate-x {
  opacity: 0;
  -webkit-transform: translateX(100px);
          transform: translateX(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .active .animate-7,
.owl-carousel .active .animate-6,
.owl-carousel .active .animate-5,
.owl-carousel .active .animate-4,
.owl-carousel .active .animate-3,
.owl-carousel .active .animate-2,
.owl-carousel .active .animate-1 {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.owl-carousel .active .animate-2 {
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}
.owl-carousel .active .animate-3 {
  -webkit-transition-delay: 600ms;
          transition-delay: 600ms;
}
.owl-carousel .active .animate-4 {
  -webkit-transition-delay: 900ms;
          transition-delay: 900ms;
}
.owl-carousel .active .animate-5 {
  -webkit-transition-delay: 1200ms;
          transition-delay: 1200ms;
}
.owl-carousel .active .animate-6 {
  -webkit-transition-delay: 1500ms;
          transition-delay: 1500ms;
}
.owl-carousel .active .animate-7 {
  -webkit-transition-delay: 1800ms;
          transition-delay: 1800ms;
}

.banner-carousel .owl-nav {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  max-width: 1200px;
  padding: 0 15px;
  margin: 0px auto 0;
  height: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.banner-carousel .owl-nav .owl-next,
.banner-carousel .owl-nav .owl-prev {
  margin-top: 10px;
}
@media (max-width: 991.98px) {
  .banner-carousel .owl-nav {
    display: none;
  }
}

/***

====================================================================
    Projects Section
====================================================================

***/
.projects-section {
  position: relative;
  padding: 120px 0 90px;
  z-index: 3;
}
.projects-section .outer-box {
  margin: 0 -260px;
}
@media only screen and (max-width: 1699px) {
  .projects-section .outer-box {
    margin: 0 -100px;
  }
}
@media (max-width: 1399.98px) {
  .projects-section .outer-box {
    margin: 0;
  }
}

.project-block {
  position: relative;
  margin-bottom: 30px;
}
.project-block .inner-box {
  position: relative;
  overflow: hidden;
}
.project-block .inner-box:hover .content-box {
  opacity: 1;
  left: 0;
  visibility: visible;
}
.project-block .inner-box:hover .content-box .icon {
  opacity: 1;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}
.project-block .inner-box:hover .content-box .cat {
  opacity: 1;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
}
.project-block .inner-box:hover .content-box .title {
  opacity: 1;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 400ms;
          transition-delay: 400ms;
}
.project-block .image-box {
  position: relative;
  overflow: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block .image-box .image {
  position: relative;
  margin-bottom: 0px;
}
.project-block .image-box .image a {
  display: block;
  width: 100%;
}
.project-block .image-box .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.project-block .content-box {
  position: absolute;
  left: -20px;
  bottom: 0;
  padding: 40px 40px 30px;
  padding-left: 50px;
  z-index: 3;
  max-width: 280px;
  min-height: 250px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block .content-box::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  left: -120px;
  width: 400px;
  content: "";
  opacity: 0.9;
  border-radius: 30px 90px 20px 30px;
  -webkit-transform: skew(-22deg);
          transform: skew(-22deg);
  background-color: var(--bg-theme-color1);
}
.project-block .content-box .icon {
  position: relative;
  display: block;
  height: 48px;
  width: 48px;
  font-size: 16px;
  color: var(--theme-color-light);
  background-color: var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  margin-bottom: 35px;
  opacity: 0;
  -webkit-transform: translateY(-20px);
          transform: translateY(-20px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block .content-box .icon:hover {
  background-color: var(--theme-color-light);
  color: var(--theme-color2);
}
.project-block .content-box .cat {
  position: relative;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.05em;
  color: var(--theme-color2);
  display: block;
  text-transform: uppercase;
  margin-bottom: 2px;
  opacity: 0;
  -webkit-transform: translateY(-20px);
          transform: translateY(-20px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block .content-box .title {
  color: var(--theme-color2);
  margin-bottom: 0;
  opacity: 0;
  -webkit-transform: translateY(-20px);
          transform: translateY(-20px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block .content-box .title a:hover {
  text-decoration: underline;
}

/***

====================================================================
    Features Section
====================================================================

***/
.features-section {
  position: relative;
  padding: 120px 0 70px;
}
.features-section .content-column {
  margin-bottom: 50px;
}
.features-section .content-column .inner-column {
  position: relative;
  max-width: 570px;
}
.features-section .content-column .sec-title {
  margin-bottom: 40px;
}
@media (max-width: 1199.98px) {
  .features-section .content-column .sec-title br {
    display: none;
  }
}
.features-section .features-column {
  position: relative;
  margin-bottom: 20px;
}
.features-section .features-column .inner-column {
  padding-left: 60px;
}
@media (max-width: 1199.98px) {
  .features-section .features-column .inner-column {
    padding-left: 0;
  }
}
.features-section .features-column .row {
  margin: 0 -5px;
}
.features-section .features-column .row > div {
  padding: 0 5px;
}

.feature-block {
  position: relative;
  margin-bottom: 10px;
}
.feature-block .inner-box {
  position: relative;
  padding: 35px 40px;
  background-color: #0e0f11;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.feature-block .inner-box:hover .icon {
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
}
.feature-block .icon {
  display: inline-block;
  font-size: 64px;
  color: var(--theme-color1);
  margin-bottom: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.feature-block .title {
  display: block;
  margin-bottom: 0;
  color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/***

====================================================================
    Features Section Two
====================================================================

***/
.features-section-two {
  position: relative;
  padding: 90px 0;
}
.features-section-two .outer-box {
  position: relative;
  background-color: var(--theme-color-light);
  border-radius: 10px;
  overflow: hidden;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
}
.features-section-two .title-column .inner-column {
  position: relative;
  background-color: var(--bg-theme-color1);
  padding: 55px 30px 120px 60px;
  height: 100%;
  margin-right: -15px;
}
@media (max-width: 1199.98px) {
  .features-section-two .title-column .inner-column {
    padding: 30px 30px 100px;
  }
}
@media (max-width: 991.98px) {
  .features-section-two .title-column .inner-column {
    text-align: center;
  }
}
.features-section-two .title-column .inner-column .title {
  line-height: 36px;
  margin-bottom: 45px;
}
.features-section-two .title-column .inner-column .image {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  margin-bottom: 0;
}
.features-section-two .title-column .inner-column .image img {
  width: 100%;
  height: 120px;
  -o-object-fit: cover;
     object-fit: cover;
}

.feature-block-two {
  position: relative;
}
.feature-block-two .inner-box {
  position: relative;
  padding: 50px 55px 55px;
  background-color: var(--theme-color-light);
  border-right: 1px solid #dae3e9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 1199.98px) {
  .feature-block-two .inner-box {
    padding: 30px 30px;
  }
}
@media (max-width: 991.98px) {
  .feature-block-two .inner-box {
    text-align: center;
  }
}
.feature-block-two .inner-box:hover .icon {
  background-color: var(--theme-color2);
  color: var(--theme-color1);
}
.feature-block-two .icon {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 72px;
  width: 72px;
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
  font-size: 32px;
  border-radius: 50%;
  margin-bottom: 25px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 991.98px) {
  .feature-block-two .icon {
    margin: 0 auto 25px;
  }
}
.feature-block-two .icon:after {
  position: absolute;
  left: 100%;
  top: 15px;
  margin-left: -20px;
  height: 44px;
  width: 107px;
  background-image: url(../images/icons/icon-line2.png);
  content: "";
}
.feature-block-two .title {
  display: block;
  color: var(--theme-color2);
  margin-bottom: 15px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/*** 

====================================================================
	Features Section Three
====================================================================

***/
.features-section-three {
  position: relative;
  z-index: 2;
  padding: 120px 0 70px;
  background-color: var(--theme-color2);
}
.features-section-three.pull-up .bg {
  bottom: 0;
  top: -140px;
}
.features-section-three .title-column .inner-column {
  padding-right: 20px;
}
@media (max-width: 991.98px) {
  .features-section-three .title-column .inner-column {
    padding-right: 0;
  }
}
.features-section-three .sec-title {
  margin-bottom: 40px;
}
.features-section-three .info-box {
  position: relative;
  padding-left: 200px;
  min-height: 110px;
  margin-bottom: 50px;
}
@media (max-width: 575.98px) {
  .features-section-three .info-box {
    padding-left: 0;
  }
}
.features-section-three .info-box .image {
  position: absolute;
  left: 0;
  top: 0;
  max-width: 170px;
  border-radius: 10px;
  overflow: hidden;
}
@media (max-width: 575.98px) {
  .features-section-three .info-box .image {
    position: relative;
  }
}
.features-section-three .info-column {
  margin-bottom: 40px;
}
.features-section-three .info-list {
  position: relative;
}
.features-section-three .info-list li {
  position: relative;
  font-size: 18px;
  line-height: 30px;
  padding: 30px 30px;
  padding-right: 100px;
  color: #fff;
  background-color: rgba(174, 174, 174, 0.08);
  margin-bottom: 10px;
  border-radius: 10px;
}
.features-section-three .info-list li:before {
  position: absolute;
  right: 30px;
  top: 20px;
  text-align: center;
  line-height: 50px;
  height: 50px;
  width: 50px;
  background-color: #ffffff;
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color1);
  content: "\f00c";
  font-family: "Font Awesome 6 Pro";
  font-weight: 400;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .features-section-three .info-list li {
    padding: 20px;
    padding-right: 80px;
  }
  .features-section-three .info-list li:before {
    right: 20px;
    top: 50%;
    margin-top: -20px;
    height: 40px;
    width: 40px;
    line-height: 40px;
    font-size: 18px;
  }
}
.features-section-three .info-list li:hover::before {
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
}

/***

==================================================================
    Features Section Four
==================================================================

***/
.features-section-four {
  position: relative;
  overflow: hidden;
  padding: 120px 0 70px;
}
.features-section-four .bg {
  max-width: 81%;
}
@media (max-width: 991.98px) {
  .features-section-four .bg {
    max-width: 100%;
  }
}
.features-section-four .outer-box {
  position: relative;
}
.features-section-four .content-column {
  position: relative;
  margin-bottom: 50px;
}
.features-section-four .content-column .inner-column {
  position: relative;
}
.features-section-four .content-column .sec-title {
  margin-bottom: 73px;
}
.features-section-four .content-column .info-box {
  position: relative;
  padding-left: 70px;
  min-height: 50px;
  margin-bottom: 50px;
}
.features-section-four .content-column .info-box::before {
  position: absolute;
  left: 25px;
  top: 60px;
  width: 2px;
  background-color: var(--theme-color2);
  bottom: -40px;
  content: "";
}
.features-section-four .content-column .info-box:last-child {
  margin-bottom: 0;
}
.features-section-four .content-column .info-box:last-child:before {
  display: none;
}
.features-section-four .content-column .info-box .count {
  position: absolute;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 2px;
  height: 48px;
  width: 50px;
  background-color: #000000;
  line-height: 1em;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.features-section-four .content-column .info-box .title {
  margin-bottom: 25px;
}
.features-section-four .content-column .info-box .text {
  margin-bottom: 0;
}
.features-section-four .content-column .info-box:hover .count {
  -webkit-transform: rotate(180deg) scale(-1);
          transform: rotate(180deg) scale(-1);
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
}
.features-section-four .image-column {
  position: relative;
  z-index: 1;
  margin-bottom: 50px;
}
.features-section-four .image-column .inner-column {
  position: relative;
}
.features-section-four .image-column .image-box {
  position: relative;
  margin-right: -370px;
}
@media (max-width: 991.98px) {
  .features-section-four .image-column .image-box {
    margin-right: 0;
  }
}
.features-section-four .image-column .image-box .image {
  position: relative;
  margin-bottom: 0;
}
.features-section-four .image-column .image-box .image img {
  position: relative;
  width: 100%;
}
.features-section-four .image-column .image-box .caption {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: var(--bg-theme-color1);
  padding: 45px 50px;
  font-size: 22px;
  line-height: 1.2em;
  color: var(--theme-color2);
  font-weight: 500;
  max-width: 370px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .features-section-four .image-column .image-box .caption {
    position: relative;
    max-width: 100%;
    padding: 30px 30px;
  }
}

/***

==================================================================
    Features Section Five
==================================================================

***/
.features-section-five {
  position: relative;
  overflow: hidden;
  padding: 100px 0 0;
}
.features-section-five .bg {
  height: 540px;
}
.features-section-five .icon-arrow1 {
  position: absolute;
  right: 0;
  bottom: 60px;
  z-index: 3;
}
.features-section-five .sec-title {
  margin-bottom: 85px;
}
.features-section-five .content-column {
  position: relative;
  margin-bottom: 75px;
  z-index: 2;
}
.features-section-five .content-column .inner-column {
  position: relative;
  margin-left: -325px;
  background-color: #fff;
  margin-top: 210px;
  padding: 85px 0px 80px 100px;
}
@media (max-width: 991.98px) {
  .features-section-five .content-column .inner-column {
    margin-left: 0;
    margin-top: 0;
    padding: 50px 0 100px;
  }
}
.features-section-five .content-column .inner-column .sec-title {
  margin-bottom: 40px;
}
.features-section-five .content-column .inner-column .sec-title .text {
  margin-top: 20px;
}
.features-section-five .content-column .info-box {
  position: relative;
  padding-left: 70px;
  min-height: 50px;
  margin-bottom: 50px;
}
.features-section-five .content-column .info-box::before {
  position: absolute;
  left: 25px;
  top: 60px;
  width: 2px;
  background-color: var(--theme-color2);
  bottom: -40px;
  content: "";
}
.features-section-five .content-column .info-box:last-child {
  margin-bottom: 0;
}
.features-section-five .content-column .info-box:last-child:before {
  display: none;
}
.features-section-five .content-column .info-box .count {
  position: absolute;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 2px;
  height: 48px;
  width: 50px;
  background-color: #000000;
  line-height: 1em;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.features-section-five .content-column .info-box .title {
  margin-bottom: 25px;
}
.features-section-five .content-column .info-box .text {
  margin-bottom: 0;
}
.features-section-five .content-column .info-box:hover .count {
  -webkit-transform: rotate(180deg) scale(-1);
          transform: rotate(180deg) scale(-1);
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
}
.features-section-five .image-column {
  position: relative;
  margin-bottom: 50px;
}
.features-section-five .image-column .inner-column {
  position: relative;
}
.features-section-five .image-column .image-box {
  position: relative;
}
.features-section-five .image-column .image-box .image {
  position: relative;
  margin-bottom: 0;
}
.features-section-five .image-column .image-box .image img {
  position: relative;
  width: 100%;
}
.features-section-five .image-column .image-box .caption {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: var(--bg-theme-color1);
  padding: 45px 50px;
  font-size: 22px;
  line-height: 1.2em;
  color: var(--theme-color2);
  font-weight: 500;
  max-width: 370px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.features-section-five .image-column .rounded-text {
  position: absolute;
  right: -83px;
  top: -70px;
}
@media (max-width: 991.98px) {
  .features-section-five .image-column .rounded-text {
    display: none;
  }
}
.features-section-five .image-column .rounded-text .letter {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  line-height: 1em;
  font-size: 72px;
  color: var(--theme-color-light);
  font-weight: 600;
}
.features-section-five .image-column .rounded-text img {
  -webkit-animation: fa-spin 30s infinite linear;
          animation: fa-spin 30s infinite linear;
}

/*** 

====================================================================
		About Section
====================================================================

***/
.about-section {
  position: relative;
  padding: 100px 0 50px;
}
.about-section:before {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 35%;
  background-image: url(../images/icons/gradient.jpg);
  content: "";
}
.about-section .content-column {
  position: relative;
  margin-bottom: 50px;
}
.about-section .content-column .inner-column {
  position: relative;
  padding-left: 70px;
}
@media (max-width: 1199.98px) {
  .about-section .content-column .inner-column {
    padding-left: 0;
  }
}
.about-section .content-column .sec-title {
  margin-bottom: 35px;
}
.about-section .content-column .info-box {
  position: relative;
  padding-left: 105px;
  margin-bottom: 30px;
}
@media (max-width: 575.98px) {
  .about-section .content-column .info-box {
    padding-left: 0;
    text-align: center;
  }
}
.about-section .content-column .info-box:hover .icon {
  color: var(--theme-color2);
}
.about-section .content-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0px;
  color: var(--theme-color1);
  line-height: 1em;
  font-size: 62px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .about-section .content-column .info-box .icon {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
  }
}
.about-section .content-column .info-box .icon:after {
  position: absolute;
  left: 20px;
  top: 20px;
  height: 52px;
  width: 52px;
  z-index: -1;
  background-color: #f2f3f6;
  content: "";
}
.about-section .content-column .info-box .title {
  margin-bottom: 5px;
}
.about-section .content-column .other-info {
  position: relative;
  padding-right: 230px;
  margin-top: 42px;
}
@media (max-width: 767.98px) {
  .about-section .content-column .other-info {
    padding-right: 0;
  }
}
.about-section .content-column .other-info .theme-btn {
  position: absolute;
  right: 0;
  top: 10px;
}
@media (max-width: 767.98px) {
  .about-section .content-column .other-info .theme-btn {
    position: relative;
    margin-top: 30px;
    top: 0;
  }
}
.about-section .content-column .author-info {
  position: relative;
  padding-left: 110px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 88px;
}
.about-section .content-column .author-info .thumb {
  position: absolute;
  left: 0;
  top: 0;
  height: 88px;
  width: 88px;
  padding: 2px;
  background: -webkit-gradient(linear, left bottom, left top, from(var(--theme-color2)), to(var(--theme-color1)));
  background: linear-gradient(to top, var(--theme-color2), var(--theme-color1));
  border-radius: 50%;
}
.about-section .content-column .author-info .thumb img {
  border-radius: 50%;
  border: 9px solid #fff;
}
.about-section .content-column .author-info .name {
  margin-bottom: 0;
}
.about-section .content-column .author-info .designation {
  font-size: 13px;
  line-height: 15px;
  margin-bottom: 0;
}
.about-section .image-column {
  position: relative;
  margin-bottom: 50px;
}
@media (max-width: 991.98px) {
  .about-section .image-column {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3;
  }
}
.about-section .image-column .image-box {
  position: relative;
  padding-bottom: 120px;
}
@media (max-width: 767.98px) {
  .about-section .image-column .image-box {
    padding-bottom: 0;
  }
}
.about-section .image-column .image-box .icon-circle {
  position: absolute;
  left: 85px;
  bottom: 40px;
}
@media (max-width: 991.98px) {
  .about-section .image-column .image-box .icon-circle {
    display: none;
  }
}
.about-section .image-column .image-box .icon-dots {
  position: absolute;
  right: 20px;
  bottom: 70px;
}
.about-section .image-column .image-1 {
  margin: 0;
}
.about-section .image-column .image-2 {
  position: absolute;
  right: -23px;
  bottom: 0;
  border: 10px solid #fff;
  margin-bottom: 0;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.1);
}
@media (max-width: 1199.98px) {
  .about-section .image-column .image-2 {
    right: 0;
  }
}
@media (max-width: 767.98px) {
  .about-section .image-column .image-2 {
    display: none;
  }
}
.about-section .image-column .exp-box {
  position: absolute;
  left: -80px;
  top: 90px;
  padding: 40px 40px 60px;
  background-image: url(../images/icons/shape2.png);
  height: 170px;
  width: 303px;
}
@media (max-width: 1199.98px) {
  .about-section .image-column .exp-box {
    left: 15px;
  }
}
@media (max-width: 767.98px) {
  .about-section .image-column .exp-box {
    display: none;
  }
}
.about-section .image-column .exp-box .inner {
  position: relative;
  padding-left: 85px;
}
.about-section .image-column .exp-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 62px;
  color: var(--theme-color2);
}
.about-section .image-column .exp-box .count {
  font-size: 48px;
  font-weight: 600;
  line-height: 1em;
  color: var(--theme-color2);
}
.about-section .image-column .exp-box .text {
  font-size: 15px;
  color: var(--theme-color2);
  line-height: 1em;
  font-weight: 600;
}

/*** 

====================================================================
		About Section Two
====================================================================

***/
.about-section-two {
  position: relative;
  padding: 120px 0 70px;
  overflow: hidden;
}
.about-section-two .icon-line4 {
  top: 90px;
  left: -130px;
}
.about-section-two .icon-line5 {
  right: -475px;
  top: -56px;
}
.about-section-two .icon-arrow1 {
  left: -190px;
  top: 380px;
}
.about-section-two .icon-speaker {
  left: -375px;
  top: 430px;
}
.about-section-two .content-column {
  position: relative;
  margin-bottom: 50px;
  z-index: 1;
}
.about-section-two .content-column .inner-column {
  position: relative;
}
.about-section-two .sec-title {
  margin-bottom: 35px;
}
.about-section-two .info-box {
  position: relative;
  margin-bottom: 30px;
}
.about-section-two .info-box .inner {
  position: relative;
}
.about-section-two .info-box .inner:hover .icon {
  color: var(--theme-color1);
}
.about-section-two .info-box .icon {
  font-size: 14px;
  color: var(--theme-color1);
  margin-right: 5px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.about-section-two .info-box .title {
  margin-bottom: 20px;
}
.about-section-two .info-box .text {
  margin-bottom: 0;
}
.about-section-two .skills {
  margin-bottom: 45px;
}
.about-section-two .image-column {
  position: relative;
  margin-bottom: 30px;
}
@media (max-width: 991.98px) {
  .about-section-two .image-column {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}
.about-section-two .image-column .inner-column {
  position: relative;
}
.about-section-two .image-column .image-box {
  position: relative;
  padding-top: 100px;
}
.about-section-two .image-column .image-box .icon-dots2 {
  position: absolute;
  left: 70px;
  top: 40px;
}
@media (max-width: 991.98px) {
  .about-section-two .image-column .image-box {
    text-align: center;
  }
}
.about-section-two .image-column .image-1 {
  position: relative;
  margin-bottom: 0;
}
.about-section-two .image-column .image-2 {
  position: absolute;
  right: 45px;
  top: 0;
  margin-bottom: 20px;
}
.about-section-two .image-column .image-2 img {
  width: 100%;
}
@media (max-width: 991.98px) {
  .about-section-two .image-column .image-2 {
    display: none;
  }
}
.about-section-two .image-column .exp-box {
  position: absolute;
  left: 220px;
  bottom: 55px;
  width: 275px;
  background-color: var(--theme-color2);
  padding: 10px;
  text-align: left;
}
@media (max-width: 1199.98px) {
  .about-section-two .image-column .exp-box {
    left: 15px;
    bottom: 15px;
  }
}
@media (max-width: 575.98px) {
  .about-section-two .image-column .exp-box {
    position: relative;
    left: 0;
    bottom: 0;
    width: 100%;
  }
}
.about-section-two .image-column .exp-box .inner {
  position: relative;
  padding: 20px 30px 25px;
  padding-left: 110px;
  background-color: var(--theme-color-light);
  border: 4px solid var(--theme-color1);
}
.about-section-two .image-column .exp-box .icon {
  position: absolute;
  left: 25px;
  top: 25px;
  display: block;
  font-size: 64px;
  font-weight: 400;
  color: var(--theme-color1);
  z-index: 2;
  margin-bottom: 5px;
}
.about-section-two .image-column .exp-box .count {
  font-size: 46px;
  line-height: 1em;
  color: var(--theme-color2);
  font-weight: 700;
  margin-bottom: 10px;
}
.about-section-two .image-column .exp-box .title {
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0;
}

/*** 

====================================================================
    Services Section
====================================================================

***/
.services-section {
  position: relative;
  padding: 120px 0 80px;
}
.services-section .bg-pattern-1 {
  max-height: 435px;
}
@media (max-width: 767.98px) {
  .services-section .bg-pattern-1 {
    min-height: 700px;
  }
}
.services-section .bg-shape {
  position: absolute;
  width: 377px;
  height: 614px;
  right: 0;
  top: 140px;
  background-image: url(../images/icons/shape1.png);
}
.services-section .sec-title {
  margin-bottom: 45px;
}
.services-section .sec-title .text {
  margin-top: 40px;
}

.service-block {
  margin-bottom: 30px;
}
.service-block .inner-box {
  position: relative;
  background: #fff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  max-height: 4px;
  z-index: 2;
  background-color: var(--bg-theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  content: "";
}
.service-block .inner-box:hover .image-box .image:before,
.service-block .inner-box:hover .content-box {
  opacity: 0;
  visibility: hidden;
}
.service-block .inner-box:hover .hover-content {
  opacity: 1;
  visibility: visible;
  -webkit-transform: scale(1);
          transform: scale(1);
}
.service-block .inner-box:hover .hover-content .icon {
  opacity: 1;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}
.service-block .inner-box:hover .hover-content .title {
  opacity: 1;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 400ms;
          transition-delay: 400ms;
}
.service-block .inner-box:hover .hover-content .text {
  opacity: 0.7;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
}
.service-block .inner-box .image-box {
  position: relative;
  min-height: 304px;
}
.service-block .inner-box .image-box .image {
  position: relative;
  margin-bottom: 0;
  background-color: var(--theme-color2);
}
.service-block .inner-box .image-box .image:before {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 40px;
  width: 100%;
  background-color: #fff;
  content: "";
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.service-block .inner-box .image-box .image img {
  width: 100%;
  min-height: 350px;
  -o-object-fit: cover;
     object-fit: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .content-box {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 20px;
  background-color: #fff;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.1);
  padding: 16px 30px;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.service-block .inner-box .content-box .icon {
  position: absolute;
  right: 0;
  bottom: 100%;
  height: 64px;
  width: 64px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--theme-color1);
  color: var(--theme-color2);
  font-size: 32px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .content-box .title {
  color: var(--theme-color2);
  margin-bottom: 0;
}
.service-block .inner-box .hover-content {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  padding: 35px 30px 20px;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
}
.service-block .inner-box .hover-content::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #0e0f11;
  opacity: 0.9;
  content: "";
  border-bottom: 4px solid var(--theme-color1);
}
.service-block .inner-box .hover-content .icon {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 64px;
  width: 64px;
  color: var(--theme-color2);
  font-size: 32px;
  background-color: var(--theme-color1);
  margin-bottom: 25px;
  opacity: 0;
  -webkit-transform: translateY(20px);
          transform: translateY(20px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .hover-content .title {
  color: var(--theme-color-light);
  margin-bottom: 18px;
  -webkit-transform: translateY(20px);
          transform: translateY(20px);
  opacity: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .hover-content .text {
  color: var(--theme-color-light);
  -webkit-transform: translateY(20px);
          transform: translateY(20px);
  opacity: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/*** 

====================================================================
    Services Section Two
====================================================================

***/
.services-section-two {
  position: relative;
  padding: 120px 0 80px;
}

.service-block-two {
  margin-bottom: 30px;
}
.service-block-two .inner-box {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .image-box {
  position: relative;
}
.service-block-two .inner-box .image-box .image {
  position: relative;
  margin-bottom: 0;
  background-color: var(--theme-color2);
}
.service-block-two .inner-box .image-box .image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(-45deg, transparent, var(--theme-color2));
  opacity: 0.9;
  content: "";
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.service-block-two .inner-box .image-box .image img {
  width: 100%;
  min-height: 304px;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .title-box {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  padding: 25px 30px;
}
.service-block-two .inner-box .title-box .title {
  color: var(--theme-color-light);
  margin-bottom: 18px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box {
  position: absolute;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background-color: #0e0f11;
  padding: 20px 30px;
  padding-left: 125px;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  padding-left: 100px;
}
.service-block-two .inner-box .content-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 95px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--theme-color1);
  color: var(--theme-color2);
  font-size: 62px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  width: 70px;
  font-size: 42px;
}
.service-block-two .inner-box .content-box .text {
  font-size: 14px;
  line-height: 26px;
  color: #75767a;
}

/*** 

====================================================================
		Call To Action
====================================================================

***/
.call-to-action {
  position: relative;
  padding: 120px 0;
  z-index: 2;
}
.call-to-action .bg-image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--theme-color2);
  opacity: 0.7;
  content: "";
}
.call-to-action .outer-box {
  position: relative;
  max-width: 920px;
  margin: 0 auto;
}
@media (max-width: 991.98px) {
  .call-to-action .outer-box {
    text-align: center;
  }
}
.call-to-action .outer-box .small-image {
  position: absolute;
  right: 0px;
  top: 100px;
  border-radius: 100px;
  overflow: hidden;
  margin-bottom: 0;
}
@media (max-width: 991.98px) {
  .call-to-action .outer-box .small-image {
    display: none;
  }
}
.call-to-action .outer-box .title {
  font-size: 60px;
  color: var(--theme-color-light);
  margin-bottom: 35px;
}
@media (max-width: 991.98px) {
  .call-to-action .outer-box .title br {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .call-to-action .outer-box .title {
    font-size: 42px;
    line-height: 1.2em;
  }
}
.call-to-action .outer-box .title .selected {
  position: relative;
}
.call-to-action .outer-box .title .selected:before {
  position: absolute;
  left: -20px;
  top: 12px;
  height: 71px;
  width: 275px;
  background-image: url(../images/icons/arrow-circle.png);
  z-index: -1;
  content: "";
}

/*** 

====================================================================
		Call To Action Two
====================================================================

***/
.call-to-action-two {
  position: relative;
  z-index: 2;
}
.call-to-action-two .outer-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-color: #0e0d16;
  padding: 70px 100px;
}
@media (max-width: 1199.98px) {
  .call-to-action-two .outer-box {
    padding: 50px 50px;
  }
}
@media (max-width: 991.98px) {
  .call-to-action-two .outer-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center;
  }
}
.call-to-action-two .title {
  font-size: 30px;
  line-height: 40px;
  color: #fff;
  letter-spacing: 0;
  margin-bottom: 0;
}
.call-to-action-two .info-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: right;
}
@media (max-width: 991.98px) {
  .call-to-action-two .info-box {
    text-align: center;
  }
}
.call-to-action-two .info-box a {
  display: inline-block;
  color: #fff;
}
.call-to-action-two .info-box .num {
  font-size: 30px;
  color: #fff;
  margin-bottom: 10px;
}
.call-to-action-two .info-box .num:hover {
  text-decoration: underline;
}
.call-to-action-two .info-box .mail {
  color: var(--theme-color1);
  font-size: 20px;
  font-weight: 700;
}
.call-to-action-two .info-box .mail:hover {
  text-decoration: underline;
}
.call-to-action-two .icon-box {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
@media (max-width: 991.98px) {
  .call-to-action-two .icon-box {
    position: relative;
    left: 0;
    top: 0;
    display: block;
    -webkit-transform: none;
            transform: none;
    margin: 50px 0;
  }
}
.call-to-action-two .icon-box::before {
  position: absolute;
  left: 100%;
  top: 50%;
  width: 60px;
  margin-top: -1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-left: 20px;
  content: "";
}
@media (max-width: 1199.98px) {
  .call-to-action-two .icon-box::before {
    display: none;
  }
}
.call-to-action-two .icon-box::after {
  position: absolute;
  right: 100%;
  top: 50%;
  width: 60px;
  margin-top: -1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-right: 20px;
  content: "";
}
@media (max-width: 1199.98px) {
  .call-to-action-two .icon-box::after {
    display: none;
  }
}
.call-to-action-two .icon-box .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100px;
  width: 100px;
  background-color: var(--bg-theme-color1);
  font-size: 42px;
  color: var(--theme-color2);
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.call-to-action-two .icon-box .icon:hover {
  background-color: var(--theme-color1);
}

/*** 

====================================================================
		FAQ's Sectiom
====================================================================

***/
.faqs-section {
  position: relative;
  padding: 120px 0 70px;
}
.faqs-section .faq-column {
  margin-bottom: 50px;
}
@media (max-width: 991.98px) {
  .faqs-section .content-column {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3;
  }
}
.faqs-section .content-column .image-box {
  position: relative;
  padding-right: 45px;
  margin-bottom: 50px;
}
@media (max-width: 1199.98px) {
  .faqs-section .content-column .image-box {
    padding-right: 0;
  }
}
.faqs-section .content-column .image-box .image {
  margin-bottom: 0;
}
.faqs-section .content-column .image-box .image img {
  width: 100%;
}
.faqs-section .content-column .exp-box {
  position: relative;
  text-align: center;
}
.faqs-section .content-column .exp-box .count {
  display: block;
  font-size: 150px;
  line-height: 1em;
  font-weight: 600;
  color: var(--theme-color2);
  margin-top: -95px;
  margin-bottom: 5px;
  text-shadow: 3px 3px 0 var(--theme-color1), -1px -1px 0 var(--theme-color1), 1px -1px 0 var(--theme-color1), -1px 1px 0 var(--theme-color1), 1px 1px 0 var(--theme-color1);
}
.faqs-section .content-column .exp-box .title {
  font-size: 30px;
  font-weight: 300;
}

.faq-block {
  position: relative;
  margin-bottom: 42px;
}
.faq-block .inner-box {
  position: relative;
  padding-right: 40px;
}
@media (max-width: 1199.98px) {
  .faq-block .inner-box {
    padding-right: 0;
  }
}
@media (max-width: 575.98px) {
  .faq-block .inner-box {
    text-align: center;
  }
}
.faq-block .inner-box:hover .icon {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}
.faq-block .title-box {
  position: relative;
  padding-left: 72px;
  margin-bottom: 10px;
  min-height: 52px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (max-width: 575.98px) {
  .faq-block .title-box {
    padding-left: 0;
    margin-bottom: 0;
    text-align: center;
  }
}
.faq-block .title-box .title {
  margin-bottom: 0;
  line-height: 1.2em;
}
.faq-block .icon {
  position: absolute;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 52px;
  width: 52px;
  color: var(--theme-color2);
  background-color: var(--bg-theme-color1);
  border-radius: 50%;
  font-size: 14px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .faq-block .icon {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin: 0 auto 15px;
  }
}
.faq-block .text {
  font-size: 16px;
  line-height: 30px;
  color: var(--theme-color4);
}

.accordion-box {
  position: relative;
}
.accordion-box .block {
  position: relative;
  background-color: #fff;
  border: 1px solid transparent;
  margin-bottom: 10px;
}
.accordion-box .block:last-child {
  margin-bottom: 0;
}
.accordion-box .block.active-block {
  border: 1px solid #e1e2e7;
}
.accordion-box .block .acc-btn {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  color: #000;
  font-weight: 600;
  cursor: pointer;
  font-family: var(--title-font);
  background-color: #f2f3f6;
  padding: 25px 40px;
  padding-right: 70px;
  letter-spacing: -0.04em;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
@media (max-width: 575.98px) {
  .accordion-box .block .acc-btn {
    padding: 25px 20px;
    padding-right: 50px;
  }
}
.accordion-box .block .acc-btn .icon {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 75px;
  width: 75px;
  font-size: 16px;
  line-height: 75px;
  color: var(--theme-color2);
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.accordion-box .block .acc-btn.active {
  color: var(--theme-color1);
  background-color: transparent;
}
.accordion-box .block .acc-btn.active .icon {
  color: var(--theme-color1);
}
.accordion-box .block .acc-btn.active .icon:before {
  content: "\f068";
}
.accordion-box .block .acc-content {
  position: relative;
  display: none;
}
.accordion-box .block .acc-content .content {
  position: relative;
  padding: 0px 40px 25px;
}
@media (max-width: 575.98px) {
  .accordion-box .block .acc-content .content {
    padding: 0 20px 25px;
  }
}
.accordion-box .block .acc-content .content .text {
  margin-bottom: 0;
}
.accordion-box .block .acc-content.current {
  display: block;
}

/***

====================================================================
    Marquee Section
====================================================================

***/
.marquee-section {
  position: relative;
  background-color: var(--bg-theme-color1);
  padding: 15px 0;
}

.marquee {
  position: relative;
  --duration: 30s;
  --gap: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  gap: var(--gap);
}
.marquee .marquee-group {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  gap: var(--gap);
  min-width: 100%;
  -webkit-animation: scroll var(--duration) linear infinite;
          animation: scroll var(--duration) linear infinite;
}
.marquee .text {
  font-size: 20px;
  line-height: 36px;
  color: var(--theme-color2);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin: 0 25px;
}
@media (prefers-reduced-motion: reduce) {
  .marquee .marquee-group {
    -webkit-animation-play-state: play;
            animation-play-state: play;
  }
}
@-webkit-keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-100% - var(--gap)));
            transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-100% - var(--gap)));
            transform: translateX(calc(-100% - var(--gap)));
  }
}

/*** 

====================================================================
    Fun Fact Section
====================================================================

***/
.fun-fact-section {
  position: relative;
  padding: 120px 0 70px;
}
.fun-fact-section.pull-up {
  padding-top: 90px;
}
.fun-fact-section.pull-up .bg {
  height: auto;
  bottom: 0;
  top: -150px;
}
.fun-fact-section .fact-counter {
  max-width: 1020px;
  margin: 0 auto;
}

.counter-block {
  position: relative;
  margin-bottom: 50px;
}
.counter-block:last-child .inner:before {
  display: none;
}
.counter-block .inner {
  position: relative;
  text-align: center;
}
.counter-block .inner:before {
  position: absolute;
  right: -12px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: #d4d0e4;
  opacity: 0.2;
  content: "";
}
.counter-block .inner:hover .icon-box i {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.counter-block .inner:hover .counter-title {
  opacity: 1;
}
.counter-block .icon-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 92px;
  width: 92px;
  font-size: 52px;
  color: var(--theme-color2);
  background-color: var(--bg-theme-color1);
  border-radius: 50%;
  margin: 0 auto 15px;
}
.counter-block .icon-box:Before {
  position: absolute;
  top: -5px;
  right: -5px;
  bottom: -5px;
  left: -5px;
  background-color: #efedf8;
  opacity: 0.1;
  content: "";
  border-radius: 50%;
}
.counter-block .icon-box i {
  position: relative;
  display: block;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block .count-box {
  position: relative;
  display: block;
  font-size: 60px;
  font-weight: 600;
  line-height: 1em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  color: #fff;
}
.counter-block .count-box .count-text {
  font-size: 60px;
  font-weight: 600;
  line-height: 1em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block .counter-title {
  display: block;
  font-size: 15px;
  line-height: 24px;
  color: var(--theme-color-light);
  font-weight: 500;
  margin-top: 15px;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/*** 

====================================================================
	Testimonial Section
====================================================================

***/
.testimonial-section {
  position: relative;
  padding: 120px 0 120px;
}
.testimonial-section .title-column {
  position: relative;
  margin-bottom: 50px;
}
.testimonial-section .title-column .sec-title {
  margin-bottom: 30px;
}
.testimonial-section .title-column .info-box {
  position: relative;
  padding-left: 110px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  min-height: 90px;
}
@media (max-width: 575.98px) {
  .testimonial-section .title-column .info-box {
    padding-left: 0;
  }
}
.testimonial-section .title-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 90px;
  width: 90px;
  font-size: 62px;
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (max-width: 575.98px) {
  .testimonial-section .title-column .info-box .icon {
    position: relative;
    margin-bottom: 20px;
  }
}
.testimonial-section .title-column .info-box .text {
  font-size: 20px;
  line-height: 30px;
}
.testimonial-section .testimonial-column .inner-column {
  margin: -15px -15px -15px;
  margin-right: -285px;
}
@media (max-width: 1199.98px) {
  .testimonial-section .testimonial-column .inner-column {
    margin-right: -15px;
  }
}
.testimonial-section .testimonial-column .owl-nav {
  position: absolute;
  left: -585px;
  bottom: 15px;
}
@media (max-width: 1199.98px) {
  .testimonial-section .testimonial-column .owl-nav {
    display: none;
  }
}

.testimonial-block {
  position: relative;
  overflow: hidden;
  padding: 15px 15px;
}
.testimonial-block .inner-box {
  position: relative;
}
.testimonial-block .inner-box:hover .content-box .icon {
  color: var(--theme-color2);
  background-color: var(--bg-theme-color2);
}
.testimonial-block .inner-box:hover .content-box .icon:after {
  border-bottom-color: #e7a72c;
}
.testimonial-block .inner-box:hover .content-box .info-box {
  background-color: var(--bg-theme-color1);
}
.testimonial-block .inner-box:hover .content-box .info-box .designation {
  color: #fff;
}
.testimonial-block .content-box {
  position: relative;
  background-color: #fff;
  padding: 35px 40px 40px;
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.04);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.04);
  background-image: url(../images/icons/shape3.png);
  background-position: right top;
  background-repeat: no-repeat;
}
@media (max-width: 575.98px) {
  .testimonial-block .content-box {
    padding: 40px 25px 45px;
    background-image: none;
  }
}
.testimonial-block .content-box:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(var(--theme-color2)), to(var(--theme-color1)));
  background: linear-gradient(to right, var(--theme-color2), var(--theme-color1));
  height: 6px;
  content: "";
}
.testimonial-block .content-box:after {
  position: absolute;
  left: 40px;
  top: 100%;
  -webkit-clip-path: polygon(31% 100%, 0 0, 100% 0);
          clip-path: polygon(31% 100%, 0 0, 100% 0);
  background-color: #28251f;
  height: 23px;
  width: 54px;
  content: "";
}
.testimonial-block .content-box .text {
  position: relative;
  font-size: 16px;
  line-height: 30px;
}
.testimonial-block .info-box {
  position: relative;
  margin-left: 10px;
  min-height: 96px;
  margin-top: 30px;
  padding-left: 115px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (max-width: 575.98px) {
  .testimonial-block .info-box {
    margin-left: 0px;
  }
}
.testimonial-block .info-box .thumb {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 96px;
  border: 5px solid #fff;
  overflow: hidden;
  border-radius: 50%;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}
.testimonial-block .info-box .thumb img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
.testimonial-block .info-box .rating {
  position: relative;
  font-size: 12px;
  z-index: 2;
  color: var(--theme-color1);
}
.testimonial-block .info-box .name {
  margin-bottom: 0;
}
.testimonial-block .info-box .designation {
  display: block;
  margin-bottom: 0;
}

/*** 

====================================================================
	Testimonial Section Two
====================================================================

***/
.testimonial-section-two {
  position: relative;
  padding: 120px 0 120px;
}
.testimonial-section-two .title-column {
  position: relative;
  margin-bottom: 50px;
}
.testimonial-section-two .title-column .sec-title {
  margin-bottom: 0px;
}
.testimonial-section-two .title-column .sec-title .sub-title,
.testimonial-section-two .title-column .sec-title .text {
  color: var(--theme-color2);
}
.testimonial-section-two .title-column .sec-title h2 {
  font-size: 40px;
}
.testimonial-section-two .title-column .sec-title h2:before {
  background-color: var(--theme-color-light);
}
.testimonial-section-two .owl-nav {
  position: absolute;
  left: -300px;
  bottom: 15px;
}
@media (max-width: 1199.98px) {
  .testimonial-section-two .owl-nav {
    display: none;
  }
}

.testimonial-block-two {
  position: relative;
  overflow: hidden;
}
.testimonial-block-two .inner-box {
  position: relative;
  padding-top: 35px;
}
.testimonial-block-two .inner-box:hover .content-box .thumb {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.testimonial-block-two .inner-box:hover .info-box .name {
  color: var(--theme-color1);
}
.testimonial-block-two .content-box {
  position: relative;
  padding: 55px 40px 35px;
  background-color: var(--theme-color2);
}
@media (max-width: 575.98px) {
  .testimonial-block-two .content-box {
    padding: 55px 25px 20px;
  }
}
.testimonial-block-two .content-box .thumb {
  position: absolute;
  left: 40px;
  top: -34px;
  width: 68px;
  height: 68px;
  border: 2px solid var(--theme-color1);
  padding: 5px;
  background-color: var(--theme-color2);
  overflow: hidden;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .testimonial-block-two .content-box .thumb {
    left: 25px;
  }
}
.testimonial-block-two .content-box .thumb img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
.testimonial-block-two .content-box .rating {
  position: relative;
  font-size: 12px;
  line-height: 15px;
  letter-spacing: -0.1em;
  z-index: 2;
  color: var(--theme-color1);
  margin-bottom: 10px;
}
.testimonial-block-two .content-box .text {
  position: relative;
  line-height: 28px;
  margin-bottom: 15px;
}
.testimonial-block-two .info-box {
  position: relative;
}
.testimonial-block-two .info-box .name {
  margin-bottom: 0;
  line-height: 1.2em;
  color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-block-two .info-box .designation {
  display: block;
  font-size: 11px;
  line-height: 1.8em;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.1em;
  margin-top: 2px;
}

/***

====================================================================
    team Section
====================================================================

***/
.team-section {
  position: relative;
  padding: 120px 0 90px;
  overflow: hidden;
}

.team-block-two {
  position: relative;
  margin-bottom: 30px;
}
.team-block-two .inner-box {
  position: relative;
  padding-bottom: 30px;
}
.team-block-two .inner-box:hover .image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.team-block-two .inner-box:hover .social-links {
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  opacity: 1;
  visibility: visible;
}
.team-block-two .inner-box:hover .info-box::after {
  height: 385px;
}
.team-block-two .inner-box:hover .info-box .name {
  color: var(--theme-color-light);
}
.team-block-two .info-box {
  position: relative;
  padding: 35px 20px 30px;
  text-align: center;
}
.team-block-two .info-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 385px;
  background-color: #f2f3f6;
  width: 100%;
  border-radius: 20px;
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-block-two .info-box:after {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  height: 0;
  content: "";
  border-radius: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  background-color: var(--theme-color2);
}
.team-block-two .info-box .name {
  position: relative;
  margin-bottom: 7px;
  z-index: 2;
}
.team-block-two .info-box .designation {
  position: relative;
  display: block;
  font-size: 14px;
  text-transform: capitalize;
  z-index: 2;
  line-height: 1em;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-two .image-box {
  position: relative;
  padding: 0 20px;
}
.team-block-two .image-box .image {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
  border-radius: 40px;
  z-index: 1;
}
.team-block-two .image-box .image img {
  width: 100%;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-two .share-icon {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: -30px;
  height: 60px;
  width: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 16px;
  color: var(--theme-color2);
  background-color: var(--bg-theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  z-index: 3;
  border-radius: 50%;
}
.team-block-two .social-links {
  position: absolute;
  right: 0;
  left: 0;
  bottom: 40px;
  padding: 15px 0;
  margin: 0 auto 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 54px;
  background: var(--theme-color2);
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
  z-index: 3;
  visibility: hidden;
  opacity: 0;
  border-radius: 10px;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-two .social-links a {
  position: relative;
  height: 35px;
  width: 50px;
  display: block;
  font-size: 14px;
  line-height: 35px;
  text-align: center;
  color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-block-two .social-links a:hover {
  color: var(--theme-color2);
}

/*** 

====================================================================
    Contact Section
====================================================================

***/
.contact-section {
  position: relative;
}
.contact-section .anim-icons {
  z-index: 3;
  pointer-events: none;
}
.contact-section .anim-icons .icon-line3 {
  left: 410px;
  top: 30px;
}
.contact-section .anim-icons .icon-arrow1 {
  left: -195px;
  top: 230px;
}
.contact-section .anim-icons .icon-arrow2 {
  top: 75px;
  left: 680px;
}
.contact-section .outer-box {
  position: relative;
  padding: 60px 60px;
}
@media (max-width: 767.98px) {
  .contact-section .outer-box {
    padding: 40px 20px 20px;
  }
}
.contact-section .outer-box .sec-title {
  margin-bottom: 40px;
}
.contact-section .outer-box .sec-title .sub-title {
  color: var(--theme-color2);
}
.contact-section .outer-box .sec-title h2 {
  font-size: 40px;
}
.contact-section .outer-box .sec-title h2:before {
  background-color: #fff;
  top: 20px;
  left: 0px;
}
.contact-section .outer-box .contact-form {
  padding: 60px 60px;
  background-color: #fff;
  max-width: 760px;
  z-index: 2;
}
@media (max-width: 1199.98px) {
  .contact-section .outer-box .contact-form {
    max-width: 100%;
  }
}
@media (max-width: 767.98px) {
  .contact-section .outer-box .contact-form {
    padding: 30px 20px;
  }
}
.contact-section .outer-box .image {
  position: absolute;
  right: 5px;
  bottom: 0;
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .contact-section .outer-box .image {
    display: none;
  }
}

.contact-form {
  position: relative;
}
.contact-form .row {
  margin: 0 -10px;
}
.contact-form .row > div {
  padding: 0 10px;
}
.contact-form .form-group {
  position: relative;
  margin-bottom: 20px;
}
.contact-form .form-group:last-child {
  margin-bottom: 0;
}
.contact-form .form-group label {
  font-size: 16px;
  line-height: 20px;
  color: #ffffff;
  font-weight: 500;
  display: block;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.contact-form .form-group label.error {
  display: block;
  font-weight: 500;
  font-size: 13px;
  text-transform: capitalize;
  line-height: 24px;
  color: #ff0000;
  margin-bottom: 0;
}
.contact-form .form-group .select2-container--default .select2-selection--single,
.contact-form .form-group input:not([type=submit]),
.contact-form .form-group textarea,
.contact-form .form-group select {
  position: relative;
  display: block;
  height: 60px;
  width: 100%;
  padding: 15px 30px;
  font-size: 14px;
  color: #717070;
  line-height: 28px;
  font-weight: 400;
  background-color: #f6f6f6;
  border: 1px solid transparent;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-form .form-group ::-webkit-input-placeholder {
  color: #717070;
}
.contact-form .form-group input:focus,
.contact-form .form-group select:focus,
.contact-form .form-group textarea:focus {
  border-color: var(--theme-color2);
}
.contact-form .form-group textarea {
  height: 100px;
  resize: none;
}
@media (max-width: 991.98px) {
  .contact-form .form-group input[type=submit],
  .contact-form .form-group button {
    width: 100%;
  }
}

/*** 

====================================================================
    Contact Section Two
====================================================================

***/
.contact-section-two {
  position: relative;
  padding: 120px 0 70px;
}
.contact-section-two .form-column {
  position: relative;
  margin-bottom: 50px;
}
.contact-section-two .form-column .inner-column {
  padding-right: 30px;
}
@media (max-width: 991.98px) {
  .contact-section-two .form-column .inner-column {
    padding-right: 0;
  }
}
.contact-section-two .image-box {
  position: absolute;
  right: 0;
  top: 0;
  width: 46%;
  height: 100%;
  padding-left: 130px;
}
@media (max-width: 1199.98px) {
  .contact-section-two .image-box {
    padding-left: 30PX;
  }
}
@media (max-width: 991.98px) {
  .contact-section-two .image-box {
    display: none;
  }
}
.contact-section-two .image-box .image {
  position: relative;
  margin-bottom: 0;
  height: 100%;
}
.contact-section-two .image-box .image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.contact-section-two .image-box .image-overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url(../images/icons/overlay2.png);
}
@media only screen and (max-width: 1699px) {
  .contact-section-two .image-box .image-overlay {
    left: 100px;
  }
}
@media (max-width: 1199.98px) {
  .contact-section-two .image-box .image-overlay {
    display: NONE;
  }
}

/***

====================================================================
    Why Choose Us
====================================================================

***/
.why-choose-us {
  position: relative;
  padding: 120px 0 70px;
}
.why-choose-us .icon-arrow1 {
  top: 450px;
  left: -300px;
  -webkit-transform: rotate(-25deg);
          transform: rotate(-25deg);
}
.why-choose-us .content-column {
  position: relative;
  margin-bottom: 50px;
  z-index: 3;
}
.why-choose-us .content-column .inner-column {
  position: relative;
  padding-right: 70px;
}
@media (max-width: 1199.98px) {
  .why-choose-us .content-column .inner-column {
    padding-right: 0;
  }
}
.why-choose-us .content-column .sec-title {
  margin-bottom: 40px;
}
.why-choose-us .content-column .sec-title .other-title {
  color: var(--theme-color1);
  margin-top: 30px;
  font-weight: 400;
}
.why-choose-us .content-column .sec-title .icon {
  position: absolute;
  right: -65px;
  bottom: -20px;
}
.why-choose-us .info-outer {
  position: relative;
  margin-right: -470px;
  border: 10px solid var(--theme-color2);
  background-color: #fff;
  padding: 40px 40px 10px;
}
@media (max-width: 991.98px) {
  .why-choose-us .info-outer {
    margin-right: 0;
  }
}
.why-choose-us .info-box {
  position: relative;
  margin-bottom: 30px;
}
.why-choose-us .info-box .inner {
  position: relative;
  padding-left: 90px;
  min-height: 70px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (max-width: 575.98px) {
  .why-choose-us .info-box .inner {
    padding-left: 0;
    text-align: center;
    padding-bottom: 10px;
  }
}
.why-choose-us .info-box .inner:hover .icon {
  background-color: var(--theme-color2);
  color: var(--theme-color1);
}
.why-choose-us .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 70px;
  width: 70px;
  font-size: 32px;
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .why-choose-us .info-box .icon {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin: 0 auto 10px;
  }
}
.why-choose-us .info-box .title {
  margin-bottom: 0;
}
.why-choose-us .image-column {
  position: relative;
  margin-bottom: 50px;
}
.why-choose-us .image-column .image-box {
  position: relative;
}
.why-choose-us .image-column .image-box .image {
  margin-bottom: 0;
}
.why-choose-us .image-column .image-box .image img {
  width: 100%;
}
.why-choose-us .image-column .rounded-text {
  position: absolute;
  left: -65px;
  top: -65px;
}
@media (max-width: 1199.98px) {
  .why-choose-us .image-column .rounded-text {
    display: none;
  }
}
.why-choose-us .image-column .rounded-text .letter {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  line-height: 1em;
  font-size: 72px;
  color: var(--theme-color2);
  font-weight: 700;
}
.why-choose-us .image-column .rounded-text img {
  -webkit-animation: fa-spin 30s infinite linear;
          animation: fa-spin 30s infinite linear;
}

/***

====================================================================
    Why Choose Us Two
====================================================================

***/
.why-choose-us-two {
  position: relative;
  background-color: #f2f3f6;
}
@media (max-width: 991.98px) {
  .why-choose-us-two {
    padding-bottom: 120px;
  }
}
.why-choose-us-two .icon-arrow1 {
  top: 470px;
  left: -260px;
}
.why-choose-us-two .content-column {
  position: relative;
  z-index: 3;
}
.why-choose-us-two .content-column .inner-column {
  position: relative;
  padding: 120px 0 85px;
}
.why-choose-us-two .content-column .sec-title {
  margin-bottom: 40px;
}
.why-choose-us-two .info-box {
  position: relative;
  margin-bottom: 35px;
}
.why-choose-us-two .info-box .inner {
  position: relative;
}
.why-choose-us-two .info-box .inner:hover .icon {
  background-color: var(--theme-color2);
  color: var(--theme-color1);
}
.why-choose-us-two .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 70px;
  width: 70px;
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
  font-size: 36px;
  border-radius: 50%;
  -webkit-box-shadow: 0 10px 50px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 50px rgba(0, 0, 0, 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .why-choose-us-two .info-box .icon {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin: 0 auto 20px;
  }
}
.why-choose-us-two .info-box .title-box {
  position: relative;
  padding-left: 90px;
  min-height: 70px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-bottom: 15px;
}
@media (max-width: 575.98px) {
  .why-choose-us-two .info-box .title-box {
    padding-left: 0;
    text-align: center;
    min-height: auto;
  }
}
.why-choose-us-two .info-box .title {
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .why-choose-us-two .info-box .title br {
    display: none;
  }
}
.why-choose-us-two .info-box .text {
  line-height: 26px;
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .why-choose-us-two .info-box .text {
    text-align: center;
  }
}
.why-choose-us-two .image-column .inner-column {
  position: relative;
  height: 100%;
  padding-left: 100px;
  margin-right: -370px;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two .image-column .inner-column {
    padding-left: 0;
  }
}
@media (max-width: 991.98px) {
  .why-choose-us-two .image-column .inner-column {
    margin-right: 0;
    height: auto;
  }
}
.why-choose-us-two .image-column .image-box {
  position: relative;
  height: 100%;
}
.why-choose-us-two .image-column .image-box .image {
  margin-bottom: 0;
  height: 100%;
}
.why-choose-us-two .image-column .image-box .image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.why-choose-us-two .image-column .content-box {
  position: absolute;
  left: 0px;
  top: 120px;
  background-color: rgba(24, 25, 28, 0.9);
  padding: 50px 60px 80px;
  max-width: 370px;
}
@media (max-width: 767.98px) {
  .why-choose-us-two .image-column .content-box {
    position: relative;
    top: 0;
    background-color: var(--theme-color2);
    max-width: 100%;
    padding: 50px 30px 80px;
  }
}
.why-choose-us-two .image-column .content-box .text {
  font-size: 36px;
  line-height: 50px;
  color: var(--theme-color-light);
  letter-spacing: -0.04em;
  margin-bottom: 110px;
}
@media (max-width: 991.98px) {
  .why-choose-us-two .image-column .content-box .text {
    margin-bottom: 50px;
  }
}
@media (max-width: 767.98px) {
  .why-choose-us-two .image-column .content-box .text {
    font-size: 24px;
    line-height: 1.4em;
  }
}
.why-choose-us-two .image-column .content-box .caption {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: var(--theme-color1);
  font-size: 18px;
  line-height: 30px;
  color: var(--theme-color2);
  font-weight: 700;
  padding: 25px 30px;
  text-align: center;
  letter-spacing: -0.04em;
}
@media (max-width: 767.98px) {
  .why-choose-us-two .image-column .content-box .caption {
    line-height: 1.2em;
  }
}

/*** 

====================================================================
    News Section
====================================================================

***/
.news-section {
  position: relative;
  padding: 120px 0 90px;
  z-index: 3;
}
.news-section .bg.pull-up {
  height: auto;
  top: -215px;
  bottom: 0;
}

.news-block {
  position: relative;
  margin-bottom: 30px;
}
.news-block .inner-box {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.news-block .inner-box:hover .image-box .image a:after {
  left: 0;
  right: 0;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.news-block .inner-box:hover .image-box .image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.news-block .image-box {
  position: relative;
}
.news-block .image-box .image {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}
.news-block .image-box .image img {
  display: block;
  width: 100%;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.news-block .image-box .image a:after {
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  right: 51%;
  top: 0;
  opacity: 1;
  pointer-events: none;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.news-block .content-box {
  position: relative;
  padding: 30px 30px 50px;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e2e7;
  background-color: #fff;
}
.news-block .content-box .date {
  position: absolute;
  top: -20px;
  left: 30px;
  background: var(--bg-theme-color1);
  color: var(--theme-color2);
  font-size: 13px;
  padding: 5px 18px;
  height: 30px;
  line-height: 20px;
  font-weight: 600;
}
.news-block .content-box .post-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.news-block .content-box .post-info li {
  font-size: 14px;
  line-height: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-weight: 400;
  color: #6f7174;
  margin-bottom: 10px;
  margin-right: 15px;
}
.news-block .content-box .post-info li i {
  font-size: 14px;
  margin-right: 10px;
  color: var(--theme-color1);
}
.news-block .content-box .post-info li a {
  color: #777;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.news-block .content-box .post-info li a:hover {
  color: var(--theme-color1);
}
.news-block .content-box .title {
  margin-bottom: 25px;
}
.news-block .content-box .title:hover {
  color: var(--theme-color1);
}
.news-block .content-box .read-more {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #f2f3f6;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 7px 30px;
  font-size: 13px;
  color: #6f7174;
  line-height: 30px;
}
.news-block .content-box .read-more i {
  color: var(--theme-color2);
  height: 30px;
  width: 30px;
  background-color: var(--bg-theme-color1);
  font-size: 14px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
}

/*** 

====================================================================
    Video Section
====================================================================

***/
.video-section {
  position: relative;
  padding: 120px 0 120px;
  text-align: center;
}
.video-section.pull-down {
  padding-bottom: 225px;
  margin-bottom: -120px;
}
.video-section .play-btn {
  position: relative;
  margin-bottom: 50px;
}
.video-section .sec-title {
  margin-bottom: 0;
}
.video-section .sec-title h2:before {
  display: none;
}
@media (max-width: 991.98px) {
  .video-section .sec-title h2 br {
    display: none;
  }
}
.video-section .sec-title h2 .selected {
  position: relative;
}
.video-section .sec-title h2 .selected:before {
  position: absolute;
  left: -20px;
  top: 5px;
  height: 71px;
  width: 275px;
  background-image: url(../images/icons/arrow-circle.png);
  z-index: -1;
  content: "";
}
@media (max-width: 767.98px) {
  .video-section .sec-title h2 .selected:before {
    display: none;
  }
}
.video-section .video-box {
  position: relative;
  padding: 170px 30px 180px;
  text-align: center;
  z-index: 3;
}
.video-section .video-box .bg-overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url(../images/icons/overlay.png);
}
.video-section .video-box .watch-video-text {
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  max-width: 100%;
}
.video-section .video-box .play-btn {
  position: absolute;
  right: 165px;
  bottom: 100px;
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .video-section .video-box .play-btn {
    position: relative;
    display: block;
    right: 0;
    bottom: 0;
    margin-top: 50px;
    text-align: center;
  }
}
.video-section .video-box .play-btn:hover .icon {
  background-color: var(--theme-color-light);
  color: var(--theme-color2);
}
.video-section .video-box .play-btn .icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 90px;
  width: 90px;
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
  font-size: 20px;
}

/*** 

====================================================================
    Home Layout 3 Styles
====================================================================

***/
/* Service Style Three */
.service-block-new-3 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
}
.service-block-new-3.at-home6 {
  margin-top: -100px;
}
@media only screen and (max-width: 1199px) {
  .service-block-new-3.at-home6 {
    margin-top: 0;
  }
}

.service-block-new-3 .inner-box {
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 25px 25px 25px;
  background-color: var(--bg-theme-color1);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  text-align: center;
  min-height: 280px;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  border-top: 6px solid var(--theme-color2);
}

.service-block-new-3 .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  content: "";
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}

.service-block-new-3 .inner-box:hover:before {
  height: 0;
}

.service-block-new-3 .inner-box:hover .title {
  color: #ffffff;
}

.service-block-new-3 .inner-box:hover .text {
  color: #454545;
}

.service-block-new-3 .icon {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100px;
  width: 100px;
  font-size: 64px;
  color: var(--theme-color1);
  font-weight: 900;
  line-height: 64px;
  background-color: #f4f5f8;
  border-radius: 50%;
  margin: 0 auto 25px;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.service-block-new-3 .title {
  margin-bottom: 10px;
  line-height: 1.2em;
}

.service-block-new-3 .title:hover {
  color: var(--theme-color1);
}

.service-block-new-3 .text {
  position: relative;
  font-size: 14px;
  line-height: 24px;
  color: #797582;
  font-weight: 500;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

/* Service image with iconbox */
.service-block-new-1 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
}
.service-block-new-1 .inner-box {
  position: relative;
  background-color: #ffffff;
  padding: 15px;
  border: 1px solid #e6e8ed;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-new-1 .inner-box:hover {
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
}
.service-block-new-1 .inner-box:hover .image-box img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.service-block-new-1 .inner-box:hover .image-box .image:after {
  left: 0;
  right: 0;
  opacity: 0;
}
.service-block-new-1 .image-box {
  position: relative;
}
.service-block-new-1 .image-box::before {
  background: linear-gradient(-135deg, rgba(23, 23, 23, 0) 50%, var(--theme-color1) 100%);
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  content: "";
  position: absolute;
  pointer-events: none;
  z-index: 2;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.service-block-new-1 .image-box .image {
  overflow: hidden;
  margin-bottom: 0;
}
.service-block-new-1 .image-box .image img {
  width: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-new-1 .image-box .image:after {
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  right: 51%;
  top: 0;
  opacity: 1;
  pointer-events: none;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.service-block-new-1 .image-box .icon-box {
  position: absolute;
  left: 25px;
  bottom: -10px;
  height: 90px;
  width: 90px;
  background: var(--bg-theme-color2);
  color: #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 58px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-new-1 .image-box .icon-box:before {
  position: absolute;
  bottom: 0;
  left: -10px;
  border-right: 10px solid var(--theme-color2);
  border-bottom: 10px solid transparent;
  content: "";
}
.service-block-new-1 .image-box .icon-box:after {
  position: absolute;
  bottom: 0;
  right: -10px;
  border-left: 10px solid var(--theme-color2);
  border-bottom: 10px solid transparent;
  content: "";
}
.service-block-new-1 .content-box {
  position: relative;
  padding: 35px 25px 15px;
}
.service-block-new-1 .content-box .title {
  margin-bottom: 14px;
}
.service-block-new-1 .content-box .title:hover {
  color: var(--theme-color2);
}
.service-block-new-1 .content-box .text {
  position: relative;
  margin-bottom: 0;
}
.service-block-new-1 .content-box .read-more {
  font-size: 12px;
  line-height: 30px;
  color: #808287;
  font-weight: 700;
  text-transform: uppercase;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  letter-spacing: 0.1em;
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear;
  margin-top: 15px;
}
.service-block-new-1 .content-box .read-more i {
  margin-left: 10px;
  color: var(--theme-color2);
  font-size: 16px;
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear;
}
.service-block-new-1 .content-box .read-more:hover {
  color: var(--theme-color2);
}
.service-block-new-1 .content-box .read-more:hover i {
  -webkit-transform: translateX(-15px);
          transform: translateX(-15px);
  opacity: 0;
}

/*  Video Section Style  */
.video-section-new-3 {
  position: relative;
  z-index: 9;
}
.video-section-new-3 .video-box-new-3 {
  position: relative;
  min-height: 560px;
  margin-top: 20px;
}
.video-section-new-3 .video-box-new-3 .bg {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.video-section-new-3 .video-box-new-3 .bg::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #141417;
  opacity: 0.2;
  content: "";
}
.video-section-new-3 .video-box-new-3 .content {
  padding: 240px 0 95px;
  text-align: center;
}
.video-section-new-3 .video-box-new-3 .content .title {
  position: relative;
  color: #fff;
  font-size: 60px;
  margin-bottom: 0;
  margin-top: 60px;
  font-weight: 700;
}
@media only screen and (max-width: 1199px) {
  .video-section-new-3 .video-box-new-3 .content .title {
    margin-bottom: 30px;
    font-size: 32px;
  }
}
.video-section-new-3 .video-box-new-3 .content .play-now {
  position: relative;
  display: inline-block;
  margin-bottom: 30px;
}
.video-section-new-3 .video-box-new-3 .content .play-now .icon {
  color: #fff;
  font-size: 30px;
  background-color: var(--bg-theme-color2);
}

.play-now .icon {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

/*  Funfact Style At Home Layout 3  */
.fun-fact-section-new-3 {
  position: relative;
  background: #1A4D6A;
  padding: 220px 0 85px;
  margin-top: -100px;
}
.fun-fact-section-new-3::before {
  position: absolute;
  content: "";
  background-image: url(../images/icons/pattern-1.jpg);
  width: 100%;
  height: 100%;
  top: 0;
  background-size: cover;
}
.fun-fact-section-new-3 .fact-counter {
  position: relative;
  z-index: 2;
}

.counter-block-new-3 {
  position: relative;
  margin-bottom: 50px;
}
.counter-block-new-3:last-child .inner:before {
  display: none;
}
.counter-block-new-3 .inner {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  text-align: center;
  padding: 35px 20px 30px;
  background-color: #313131;
}
.counter-block-new-3 .inner:hover::before {
  height: 0;
}
.counter-block-new-3 .inner:hover:after {
  height: 100%;
}
.counter-block-new-3 .content {
  position: relative;
  width: 100%;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  z-index: 9;
}
.counter-block-new-3 .content:hover .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.counter-block-new-3 .icon {
  position: relative;
  display: inline-block;
  color: var(--bg-theme-color1);
  border-radius: 50%;
  font-size: 45px;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  margin-bottom: 20px;
}
.counter-block-new-3 .count-box {
  position: relative;
  font-size: 50px;
  font-weight: 400;
  color: #ffffff;
  line-height: 1em;
  font-family: var(--title-font);
  display: block;
  margin-bottom: 15px;
}
.counter-block-new-3 .count-box .count-text {
  font-size: 30px;
  font-weight: 700;
  line-height: 1em;
}
.counter-block-new-3 .counter-title {
  display: block;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  font-weight: normal;
  margin-bottom: 0;
}

.projects-section-home3 {
  position: relative;
  z-index: 1;
}
.projects-section-home3 .upper-box {
  position: relative;
  padding: 120px 0 50px;
  background-color: #f6f6f6;
}
.projects-section-home3 .upper-box .sec-title {
  margin-bottom: 0;
}
.projects-section-home3 .owl-nav {
  position: absolute;
  top: -120px;
  right: 0;
  left: 0;
  margin: 0 auto;
  max-width: 1200px;
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .projects-section-home3 .owl-nav {
    display: none;
  }
}

.project-block-home3,
.project-block-home4 {
  position: relative;
  overflow: hidden;
}
.project-block-home3 .image-box,
.project-block-home4 .image-box {
  position: relative;
  overflow: hidden;
}
.project-block-home3 .image-box .image,
.project-block-home4 .image-box .image {
  position: relative;
  margin-bottom: 0;
}
.project-block-home3 .image-box .image:after, .project-block-home3 .image-box .image:before,
.project-block-home4 .image-box .image:after,
.project-block-home4 .image-box .image:before {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), to(var(--bg-theme-color2)));
  background-image: linear-gradient(transparent, var(--bg-theme-color2));
  z-index: 1;
  content: "";
  pointer-events: none;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.project-block-home3 .image-box .image:after,
.project-block-home4 .image-box .image:after {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), to(var(--bg-theme-color1)));
  background-image: linear-gradient(transparent, var(--bg-theme-color1));
  height: 0;
  z-index: 1;
}
.project-block-home3 .image-box .image img,
.project-block-home4 .image-box .image img {
  display: inline-block;
  max-width: 100%;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  min-height: 300px;
  -o-object-fit: cover;
     object-fit: cover;
}
.project-block-home3 .image-box .image a,
.project-block-home4 .image-box .image a {
  position: relative;
  display: block;
}
.project-block-home3:hover img,
.project-block-home4:hover img {
  opacity: 1;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.project-block-home3:hover .image::before,
.project-block-home4:hover .image::before {
  height: 0%;
}
.project-block-home3:hover .image::after,
.project-block-home4:hover .image::after {
  height: 100%;
}
.project-block-home3 .caption-box,
.project-block-home4 .caption-box {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 40px 40px;
  width: 100%;
  text-align: left;
  z-index: 2;
}
.project-block-home3 .caption-box .title,
.project-block-home4 .caption-box .title {
  margin-bottom: 0;
  color: #ffffff;
}

/*  Home 4 Services  */
.bg-f7 {
  background-color: #f7f7f7;
}

.border-top-1 {
  border-top: 1px solid #e4e4e4;
}

.service-block-home4 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
}
.service-block-home4 .inner-box {
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 45px 50px 45px;
  background-color: #ffffff;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  z-index: 2;
}
.service-block-home4 .inner-box:after {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--bg-theme-color2);
  -webkit-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: right top;
  transform-origin: right top;
  border-radius: 0 0 0 500px;
  content: "";
  z-index: -1;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}
.service-block-home4 .inner-box:hover:after {
  -webkit-transform: scale(1.5);
  transform: scale(1.5);
  opacity: 1;
}
.service-block-home4 .inner-box:hover .title,
.service-block-home4 .inner-box:hover .text,
.service-block-home4 .inner-box:hover .icon {
  color: #ffffff;
}
.service-block-home4 .count {
  position: absolute;
  right: -40px;
  top: -70px;
  height: 170px;
  width: 170px;
  background-color: var(--bg-theme-color2);
  color: #ffffff;
  border-radius: 50%;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}
.service-block-home4 em {
  font-style: normal;
  font-size: 30px;
  font-weight: 600;
  font-family: var(--title-font);
  position: absolute;
  left: 60px;
  bottom: 45px;
}
.service-block-home4 .icon {
  position: relative;
  display: block;
  font-size: 62px;
  color: var(--theme-color2);
  font-weight: 400;
  line-height: 1em;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  margin-bottom: 20px;
}
.service-block-home4 .title {
  margin-bottom: 20px;
}
.service-block-home4 .text {
  line-height: 30px;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}

.home4-image-column .info-box {
  position: absolute;
  left: 0;
  right: 45px;
  top: 0;
  padding: 40px 30px;
  padding-left: 140px;
  background-color: var(--bg-theme-color2);
  z-index: 2;
}
@media (max-width: 767.98px) {
  .home4-image-column .info-box {
    display: block;
    padding-left: 15px;
    position: relative;
  }
}
.home4-image-column .info-box .icon {
  position: absolute;
  left: 40px;
  top: 45px;
  font-size: 68px;
  line-height: 1em;
  color: #fff;
}
@media (max-width: 767.98px) {
  .home4-image-column .info-box .icon {
    margin-bottom: 10px;
    left: 0;
    position: relative;
    top: 0;
  }
}
.home4-image-column .info-box .title {
  font-size: 40px;
  color: #ffffff;
  margin-bottom: 0;
}
.home4-image-column .info-box .sub-title {
  font-size: 18px;
  color: #ffffff;
  letter-spacing: 0.01em;
  font-weight: 500;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.offer-section-home4 {
  position: relative;
  background-color: var(--bg-theme-color2);
}

.offer-section-home4:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url(../images/icons/home4-offer-bg.jpg);
  content: "";
}

.offer-section-home4 .content-column .inner-column {
  position: relative;
  padding: 120px 0 100px;
  padding-right: 80px;
}
@media (max-width: 575px) {
  .offer-section-home4 .content-column .inner-column {
    padding-right: 0;
  }
}

.offer-section-home4 .content-column .sec-title {
  margin-bottom: 25px;
}

.offer-section-home4 .content-column .info-box {
  position: relative;
  padding-left: 85px;
  min-height: 70px;
  margin-bottom: 30px;
}

.offer-section-home4 .content-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  font-size: 72px;
  color: var(--theme-color1);
  line-height: 1em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.offer-section-home4 .content-column .info-box .title {
  line-height: 34px;
  margin-bottom: 0;
  color: #fff;
}

.list-style-two {
  position: relative;
}

.offer-section-home4 .content-column .list-style-two li {
  color: #878a8f;
}

.list-style-two li {
  position: relative;
  font-size: 18px;
  line-height: 30px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 30px;
  margin-bottom: 5px;
}

.list-style-two li i {
  position: absolute;
  left: 0;
  top: 0;
  color: var(--theme-color1);
  font-size: 18px;
  line-height: 30px;
}

.offer-section-home4 .image-column .image-box {
  position: relative;
  margin-right: -375px;
}

.offer-section-home4 .image-column .image-box .image {
  position: relative;
  margin-bottom: 0;
  width: 100%;
}

.offer-section-home4 .image-column .image-box .image img {
  width: 100%;
  min-height: 570px;
  -o-object-fit: cover;
  object-fit: cover;
}

.offer-section-home4 .image-column .caption-box {
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  width: 100%;
  max-width: 200px;
  background-color: var(--bg-theme-color1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 50px 30px 45px 35px;
}

.offer-section-home4 .image-column .caption-box .icon-box {
  position: relative;
  margin-bottom: 20px;
}

.offer-section-home4 .image-column .caption-box .title {
  color: var(--theme-color1);
  margin-bottom: 0;
}

.play-now-home4 {
  height: 92px;
  width: 92px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 12px;
  border: 0.5px solid var(--theme-color2);
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  -webkit-animation: zoom-one 3s infinite linear;
  animation: zoom-one 3s infinite linear;
}
.play-now-home4:before {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  height: 62px;
  width: 62px;
  border-radius: 50%;
  background-color: var(--bg-theme-color2);
  content: "";
}
.play-now-home4 i {
  position: relative;
}

/*  Pricing Section  */
.pricing-section-home4 {
  position: relative;
  overflow: hidden;
  padding: 120px 0 70px;
}
.pricing-section-home4 .content-column {
  position: relative;
}
.pricing-section-home4 .content-column .inner-column {
  position: relative;
  padding-right: 70px;
}
.pricing-section-home4 .content-column .sec-title {
  margin-bottom: 30px;
}
.pricing-section-home4 .content-column .info-box {
  position: relative;
  z-index: 9;
  padding-left: 90px;
  margin-bottom: 40px;
}
@media (max-width: 767.98px) {
  .pricing-section-home4 .content-column .info-box {
    padding-left: 0;
  }
}
.pricing-section-home4 .content-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 58px;
  width: 58px;
  color: var(--theme-color2);
  background-color: var(--bg-theme-color1);
  border-radius: 50%;
}
@media (max-width: 767.98px) {
  .pricing-section-home4 .content-column .info-box .icon {
    position: relative;
    margin-bottom: 20px;
  }
}
.pricing-section-home4 .content-column .info-box .title {
  color: var(--theme-color2);
  margin-bottom: 20px;
}
.pricing-section-home4 .content-column .info-box .text {
  letter-spacing: 0.01em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-section-home4 .pricing-column {
  position: relative;
}
.pricing-section-home4 .pricing-column .inner-column {
  position: relative;
  margin-left: -30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

/*Pricing Table Style */
.tm-pricing-smart-switcher-button .switch-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
  border: 0;
}

.tm-pricing-smart-switcher-button .switch-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
  border: 0;
}

.tm-pricing-smart-switcher-button .switch-buttons li:first-child a {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}

.tm-pricing-smart-switcher-button .switch-buttons li:first-child a {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.tm-pricing-smart-switcher-button .switch-buttons li a:hover, .tm-pricing-smart-switcher-button .switch-buttons li a.active {
  color: var(--text-color-bg-theme-color1);
  border-color: var(--theme-color1);
  background-color: var(--theme-color1);
}

.tm-pricing-smart-switcher-button .switch-buttons li a {
  display: block;
  border: 1px solid #eee;
  outline: none;
  display: inline-block;
  padding: 0.9375rem 2.1875rem;
  cursor: pointer;
  border-radius: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background-color: rgb(254, 253, 254);
  -webkit-box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
  box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
}

.tm-pricing-smart-switcher-button .switch-buttons li a:hover, .tm-pricing-smart-switcher-button .switch-buttons li a.active {
  color: var(--text-color-bg-theme-color1);
  border-color: var(--theme-color1);
  background-color: var(--theme-color1);
}

.tm-pricing-smart-switcher-button .switch-buttons li a {
  display: block;
  border: 1px solid #eee;
  outline: none;
  display: inline-block;
  padding: 0.9375rem 2.1875rem;
  cursor: pointer;
  border-radius: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background-color: rgb(254, 253, 254);
  -webkit-box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
  box-shadow: 0px 15px 30px 0px rgba(119, 123, 146, 0.1);
}

.team-block-home4 {
  position: relative;
  margin-bottom: 40px;
}
.team-block-home4 .inner-box {
  position: relative;
  padding: 0 11px;
}
.team-block-home4 .inner-box:hover .image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.team-block-home4 .inner-box:hover .social-links {
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  opacity: 1;
  visibility: visible;
}
.team-block-home4 .inner-box:hover .image-box::before {
  opacity: 1;
  visibility: visible;
}
.team-block-home4 .image-box {
  position: relative;
}
.team-block-home4 .image-box::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, rgba(23, 23, 23, 0) 0%, rgba(23, 23, 23, 0.65) 65%, var(--theme-color1) 100%);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  z-index: 2;
}
.team-block-home4 .image-box .image {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
  z-index: 1;
}
.team-block-home4 .image-box .image img {
  width: 100%;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-home4 .info-box {
  position: relative;
  padding: 25px 37px;
  border: 2px solid #171717;
  border-top: 0;
}
.team-block-home4 .info-box .name {
  font-weight: 600;
  z-index: 2;
  margin-bottom: 7px;
}
.team-block-home4 .info-box .name:hover {
  color: var(--theme-color2);
}
.team-block-home4 .info-box .designation {
  position: relative;
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #717070;
  z-index: 2;
  line-height: 1em;
  letter-spacing: 0.1em;
  font-family: var(--title-font);
  text-transform: uppercase;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-home4 .share-icon {
  position: absolute;
  right: 2px;
  bottom: 0px;
  height: 60px;
  width: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 18px;
  color: var(--theme-color1);
  border-top: 1px solid #eee;
  background-color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  z-index: 3;
}
.team-block-home4 .social-links {
  position: absolute;
  right: 2px;
  bottom: 60px;
  padding: 15px 0;
  margin-top: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background: #ffffff;
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
  z-index: 3;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.team-block-home4 .social-links a {
  position: relative;
  height: 34px;
  width: 60px;
  display: block;
  font-size: 14px;
  line-height: 34px;
  text-align: center;
  color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-block-home4 .social-links a:hover {
  color: var(--theme-color2);
}

/*  Home 5 About Section  */
.tm-sc-funfact-home5 {
  background-color: #FFFFFF;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  bottom: 0;
  height: 105px;
  padding: 15px 40px;
  position: absolute;
  right: 0;
  top: 500px;
  width: 230px;
}

.dark-color {
  color: #18191c !important;
}

.home5-about-img .at-home8-2 {
  position: absolute;
  right: 0;
  top: 100px;
  z-index: 1;
}

.home5-about-img .img-2 {
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
}

.home5-about-img .project {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 1.7px;
  line-height: 21px;
  position: absolute;
  right: -70px;
  text-transform: uppercase;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  top: 250px;
}

/*  Home 5 Services  */
.service-item-home5 .inner-box {
  margin-bottom: 30px;
  position: relative;
  padding: 30px 25px;
  margin-top: 90px;
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  text-align: center;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}
.service-item-home5 .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #323232;
  content: "";
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}
.service-item-home5 .inner-box .service-icon-area {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: -90px;
}
.service-item-home5 .inner-box .service-icon-area .icon {
  height: 110px;
  width: 110px;
  font-size: 58px;
  background-color: var(--theme-color1);
  line-height: 120px;
  color: var(--text-color-bg-theme-color1);
  border-radius: 50%;
  margin: 0 auto 25px;
  position: relative;
  overflow: hidden;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  z-index: 1;
}
.service-item-home5 .inner-box .service-icon-area .icon:before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  content: "";
  border-radius: 50%;
  z-index: -1;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
  transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.service-item-home5 .inner-box .title {
  position: relative;
  margin-bottom: 15px;
  color: #fff;
}
.service-item-home5 .inner-box .text {
  position: relative;
  font-size: 14px;
  line-height: 24px;
  color: #797582;
  font-weight: 500;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}
.service-item-home5 .inner-box .btn-view-details {
  margin-top: 25px;
  position: relative;
}
.service-item-home5:hover .title {
  color: #333;
}
.service-item-home5:hover .inner-box {
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
}
.service-item-home5:hover .inner-box:before {
  height: 0;
}
.service-item-home5:hover .inner-box:before .service-icon-area {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  -webkit-transition-delay: 150ms;
  transition-delay: 150ms;
}
.service-item-home5:hover .inner-box .service-icon-area .icon {
  color: #fff;
}
.service-item-home5:hover .inner-box .service-icon-area .icon:before {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
}
.service-item-home5:hover .inner-box .text {
  color: #8c8f94;
}

/*  Home 5 Feature Block  */
.features-section-home5 {
  position: relative;
  padding: 150px 0 120px;
  z-index: 2;
}
.features-section-home5.pull-up {
  margin-top: -120px;
}
.features-section-home5:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--bg-theme-color1);
  background-image: url(../images/icons/bg-pattern.png);
  background-repeat: no-repeat;
  background-size: cover;
  content: "";
}
.features-section-home5 .bottom-box {
  position: relative;
  text-align: center;
  color: #fff;
  font-size: 18px;
  line-height: 30px;
}
.features-section-home5 .bottom-box .theme-btn {
  font-size: 12px;
  padding: 5px 20px;
  margin-left: 30px;
}
@media only screen and (max-width: 767px) {
  .features-section-home5 .bottom-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    line-height: 24px;
  }
  .features-section-home5 .bottom-box .theme-btn {
    margin-left: 0;
    margin-top: 20px;
  }
}

.feature-block-home5 {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
}
.feature-block-home5 .inner-box {
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 35px 40px 10px;
  background-color: #171717;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.feature-block-home5 .inner-box .content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.feature-block-home5 .inner-box:hover .title,
.feature-block-home5 .inner-box:hover .text {
  color: var(--theme-color1);
}
.feature-block-home5 .inner-box:hover .icon {
  color: #ffffff;
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
}
.feature-block-home5 .title {
  position: relative;
  padding-bottom: 30px;
  margin-bottom: 0px;
  color: #ffffff;
}
.feature-block-home5 .title:hover {
  color: var(--theme-color1);
}
.feature-block-home5 .icon {
  position: relative;
  display: block;
  font-size: 62px;
  color: var(--theme-color1);
  font-weight: 400;
  line-height: 1em;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  margin-bottom: 14px;
}
.feature-block-home5 .read-more {
  position: relative;
  display: block;
  color: #ffffff;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  font-weight: 600;
  border-top: 3px solid rgba(255, 255, 255, 0.1);
  padding: 10px 30px 2px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-family: var(--title-font);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}

.testimonial-section-home5 {
  position: relative;
  padding: 120px 0 170px;
}
@media only screen and (max-width: 1199px) {
  .testimonial-section-home5 {
    padding: 120px 0;
  }
}
.testimonial-section-home5 .bg::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--bg-theme-color2);
  opacity: 0.8;
  content: "";
}
.testimonial-section-home5 .testimonials {
  position: relative;
}
.testimonial-section-home5 .testimonials::before {
  position: absolute;
  left: -75px;
  top: -120px;
  height: 100px;
  width: 128px;
  background-image: url(../images/icons/shape-1.png);
  content: "";
}

.testimonial-block-home5 {
  position: relative;
}
.testimonial-block-home5 .inner-box {
  position: relative;
  padding-left: 230px;
}
@media only screen and (max-width: 1023px) {
  .testimonial-block-home5 .inner-box {
    padding-left: 0;
    text-align: center;
  }
}
.testimonial-block-home5 .text {
  position: relative;
  font-size: 40px;
  line-height: 50px;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 25px;
}
@media only screen and (max-width: 1199px) {
  .testimonial-block-home5 .text {
    font-size: 32px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-block-home5 .text {
    font-size: 24px;
    line-height: 1.4em;
  }
}
.testimonial-block-home5 .name {
  color: var(--theme-color1);
  margin-top: 70px;
  margin-bottom: 0;
}
@media only screen and (max-width: 1199px) {
  .testimonial-block-home5 .name {
    margin-top: 40px;
  }
}
@media only screen and (max-width: 1023px) {
  .testimonial-block-home5 .name {
    margin-top: 20px;
  }
}
.testimonial-block-home5 .image-box {
  position: absolute;
  left: 0;
  top: 10px;
  width: 200px;
  text-align: center;
  z-index: 2;
}
@media only screen and (max-width: 1023px) {
  .testimonial-block-home5 .image-box {
    position: relative;
    display: inline-block;
    top: 0;
  }
}
.testimonial-block-home5 .image {
  position: relative;
  display: inline-block;
  height: 138px;
  width: 138px;
  border-radius: 50%;
  padding: 5px;
  border: 2px solid var(--theme-color1);
  border-radius: 50%;
}

.testimonial-thumbs-home5 {
  position: absolute;
  left: 0px;
  top: 160px;
  width: 200px;
}
@media only screen and (max-width: 1023px) {
  .testimonial-thumbs-home5 {
    position: relative;
    top: 0;
    margin-top: 50px;
  }
}
.testimonial-thumbs-home5 .testimonial-thumb {
  position: relative;
  cursor: pointer;
  z-index: 9;
}
.testimonial-thumbs-home5 .testimonial-thumb .image {
  position: relative;
  height: 60px;
  width: 60px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-thumbs-home5 .testimonial-thumb .image img {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-thumbs-home5 .testimonial-thumb.swiper-slide-thumb-active .image {
  background-color: var(--bg-theme-color2);
}
.testimonial-thumbs-home5 .testimonial-thumb.swiper-slide-thumb-active .image img {
  opacity: 0.7;
}

.testimonial-content-home5 .swiper-slide:not(.swiper-slide-active) {
  opacity: 0 !important;
}

.testimonial-content-home5 .testimonial-block-home5.swiper-slide-active .image-column .image {
  -webkit-transform: translate(0);
          transform: translate(0);
  opacity: 1;
  -webkit-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.features-section-home5-v2 {
  position: relative;
  background-color: var(--bg-theme-color2);
}
.features-section-home5-v2 .features-column .inner-column {
  position: relative;
  padding: 100px 0;
  height: 100%;
}
.features-section-home5-v2 .features-column .bg-image {
  position: absolute;
  left: -360px;
  right: 0;
  top: 0;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.features-section-home5-v2 .features-column .bg-image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(#171717), to(#E8A133));
  background: linear-gradient(90deg, #171717 0%, #E8A133 100%);
  opacity: 0.9;
  content: "";
}
.features-section-home5-v2 .content-column {
  position: relative;
  padding: 100px 0;
}
.features-section-home5-v2 .content-column .inner-column {
  padding-left: 90px;
}
@media only screen and (max-width: 1023px) {
  .features-section-home5-v2 .content-column .inner-column {
    padding-left: 0;
  }
}
.features-section-home5-v2 .content-column .image-box {
  position: relative;
  margin-top: 60px;
}
.features-section-home5-v2 .content-column .image {
  margin-bottom: 0;
}
.features-section-home5-v2 .content-column img {
  height: 200px;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
}
.features-section-home5-v2 .content-column .icon {
  position: absolute;
  right: 0;
  top: -55px;
  height: 130px;
  width: 130px;
  color: #ffffff;
  font-size: 64px;
  background-color: var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.features-section-home5-v2 .content-column .icon:before {
  height: 100px;
  width: 100px;
  background-color: var(--bg-theme-color3);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.feature-block-home5-v2 {
  position: relative;
}
.feature-block-home5-v2:last-child .inner-box {
  padding-bottom: 0;
  border-bottom: 0;
  margin-bottom: 0;
  min-height: 110px;
}
.feature-block-home5-v2 .inner-box {
  position: relative;
  min-height: 160px;
  padding-top: 10px;
  padding-left: 150px;
  padding-bottom: 50px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 50px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media only screen and (max-width: 767px) {
  .feature-block-home5-v2 .inner-box {
    padding-left: 0;
  }
}
.feature-block-home5-v2 .inner-box:hover .icon {
  background-color: var(--bg-theme-color1);
  color: #fff;
  -webkit-transform: rotate(180deg) scale(-1);
  transform: rotate(180deg) scale(-1);
}
.feature-block-home5-v2 .inner-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 110px;
  width: 110px;
  background-color: #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 64px;
  color: var(--theme-color2);
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media only screen and (max-width: 767px) {
  .feature-block-home5-v2 .inner-box .icon {
    margin-bottom: 25px;
    position: relative;
  }
}
.feature-block-home5-v2 .title {
  color: #ffffff;
  font-weight: 400;
  margin-bottom: 10px;
}
.feature-block-home5-v2 .text {
  max-width: 200px;
  color: #ffdbda;
}

/*  Home 5 Testimonials  */
.testimonial-section-home5-v2 {
  position: relative;
  padding: 120px 0 130px;
}
.testimonial-section-home5-v2 .sec-title {
  padding-right: 10px;
}

.testimonial-block-home5-v2 {
  position: relative;
}
.testimonial-block-home5-v2 .inner-box {
  position: relative;
  margin-top: 35px;
}
.testimonial-block-home5-v2 .inner-box:hover .content-box {
  background-color: var(--bg-theme-color2);
}
.testimonial-block-home5-v2 .inner-box:hover .content-box:before {
  border-top: 20px solid var(--theme-color1);
}
.testimonial-block-home5-v2 .inner-box:hover .content-box .text {
  color: #ffffff;
}
.testimonial-block-home5-v2 .content-box {
  position: relative;
  display: block;
  background-color: #ffffff;
  padding: 0px 40px 60px;
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-block-home5-v2 .content-box:before {
  position: absolute;
  left: 0;
  top: 100%;
  border-top: 20px solid #ffffff;
  border-left: 55px solid transparent;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  content: "";
}
.testimonial-block-home5-v2 .thumb {
  position: relative;
  display: inline-block;
  height: 69px;
  width: 69px;
  margin-top: -35px;
  border-radius: 50%;
  padding: 5px;
  background-color: #ffffff;
  border: 2px solid var(--theme-color1);
  margin-bottom: 10px;
}
.testimonial-block-home5-v2 .thumb img {
  width: 100%;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-block-home5-v2 .rating {
  font-size: 14px;
  color: var(--theme-color1);
  letter-spacing: 1px;
  margin-bottom: 3px;
}
.testimonial-block-home5-v2 .text {
  position: relative;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-block-home5-v2 .info-box {
  position: relative;
  padding: 20px 10px 0;
  margin-left: 55px;
}
.testimonial-block-home5-v2 .info-box .name {
  margin-bottom: 0;
}
.testimonial-block-home5-v2 .info-box .designation {
  position: relative;
  text-transform: uppercase;
  display: block;
  font-size: 11px;
  color: #808287;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.1em;
}

/*  Contact Section Home 5  */
.contact-section-home5 {
  position: relative;
  overflow: hidden;
  padding: 120px 0 70px;
}
.contact-section-home5 .bg-image {
  position: absolute;
  right: 60px;
  bottom: 0;
}
@media only screen and (max-width: 1699px) {
  .contact-section-home5 .bg-image {
    right: -60px;
  }
}
@media only screen and (max-width: 1400px) {
  .contact-section-home5 .bg-image {
    display: none;
  }
}
.contact-section-home5 .sec-title {
  margin-bottom: 40px;
}
.contact-section-home5 .title-column {
  position: relative;
  margin-bottom: 50px;
}
.contact-section-home5 .form-column {
  margin-bottom: 50px;
}
.contact-section-home5 .form-column .inner-column {
  padding-right: 130px;
  margin-left: -30px;
}
@media only screen and (max-width: 1199px) {
  .contact-section-home5 .form-column .inner-column {
    padding-right: 0;
    margin-left: 0;
  }
}
.contact-section-home5:before {
  background-color: #fff;
  bottom: 0;
  content: "";
  position: absolute;
  right: 0;
  width: 58%;
  top: -50px;
}

.contact-info-block {
  position: relative;
  margin-bottom: 20px;
}
.contact-info-block .inner {
  position: relative;
  padding-left: 110px;
  min-height: 80px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media only screen and (max-width: 576px) {
  .contact-info-block .inner {
    padding-left: 70px;
    min-height: auto;
  }
}
.contact-info-block .inner:hover .icon {
  background-color: var(--bg-theme-color1);
  border-radius: 50%;
}
.contact-info-block .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 80px;
  width: 80px;
  background-color: var(--bg-theme-color2);
  color: #fff;
  font-size: 24px;
  line-height: 1em;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media only screen and (max-width: 576px) {
  .contact-info-block .icon {
    height: 50px;
    width: 50px;
    font-size: 20px;
  }
}
.contact-info-block .title {
  font-size: 16px;
  text-transform: uppercase;
  color: #717070;
  font-weight: 500;
  margin-bottom: 0;
}
.contact-info-block .text {
  font-size: 20px;
  line-height: 30px;
  color: #171717;
  font-weight: 500;
}
.contact-info-block .text a {
  color: inherit;
  font-weight: inherit;
}
.contact-info-block .text a:hover {
  color: var(--theme-color2);
}

.contact-form-home5 {
  border-top: 5px solid var(--theme-color2);
  margin-right: 50px;
  margin-top: -50px;
}

.contact-form-home5 {
  position: relative;
  background-color: #ffffff;
  padding: 50px 60px 50px;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
}
@media only screen and (max-width: 1023px) {
  .contact-form-home5 {
    padding: 50px 40px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-form-home5 {
    padding: 40px 20px;
  }
}
.contact-form-home5::before {
  position: absolute;
  right: 30px;
  bottom: 30px;
  height: 414px;
  width: 340px;
  border-bottom: 5px solid;
  -o-border-image: linear-gradient(to left, var(--theme-color1), transparent) 1;
     border-image: -webkit-gradient(linear, right top, left top, from(var(--theme-color1)), to(transparent)) 1;
     border-image: linear-gradient(to left, var(--theme-color1), transparent) 1;
  z-index: 2;
  pointer-events: none;
  content: "";
}
@media only screen and (max-width: 1023px) {
  .contact-form-home5::before {
    display: none;
  }
}
.contact-form-home5:after {
  position: absolute;
  right: 30px;
  bottom: 30px;
  height: 414px;
  width: 340px;
  pointer-events: none;
  border-right: 5px solid;
  -o-border-image: linear-gradient(transparent, var(--border-theme-color2)) 1;
     border-image: -webkit-gradient(linear, left top, left bottom, from(transparent), to(var(--border-theme-color2))) 1;
     border-image: linear-gradient(transparent, var(--border-theme-color2)) 1;
  z-index: 2;
  content: "";
}
@media only screen and (max-width: 1023px) {
  .contact-form-home5:after {
    display: none;
  }
}
.contact-form-home5 .title {
  font-size: 40px;
  text-align: center;
  margin-bottom: 20px;
}
.contact-form-home5 .form-group {
  position: relative;
  margin-bottom: 10px;
}
.contact-form-home5 .form-group:last-child {
  margin-bottom: 0;
}
.contact-form-home5 .form-group label {
  font-size: 16px;
  line-height: 20px;
  color: #ffffff;
  font-weight: 500;
  display: block;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.contact-form-home5 .form-group label.error {
  display: block;
  font-weight: 500;
  font-size: 12px;
  text-transform: capitalize;
  line-height: 24px;
  color: #ff0000;
  margin-bottom: 0;
}
.contact-form-home5 .form-group .select2-container--default .select2-selection--single,
.contact-form-home5 .form-group input:not([type=submit]),
.contact-form-home5 .form-group textarea,
.contact-form-home5 .form-group select {
  position: relative;
  display: block;
  width: 100%;
  height: 60px;
  padding: 15px 30px;
  line-height: 30px;
  font-size: 14px;
  color: #717070;
  font-weight: 500;
  background-color: #f6f6f6;
  border: 1px solid transparent;
  border-radius: 5px;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-form-home5 .form-group ::-webkit-input-placeholder {
  color: #6a6a6a;
}
.contact-form-home5 .form-group ::-moz-input-placeholder {
  color: #8c8f94;
}
.contact-form-home5 .form-group ::-ms-input-placeholder {
  color: #8c8f94;
}
.contact-form-home5 .form-group input:focus,
.contact-form-home5 .form-group select:focus,
.contact-form-home5 .form-group textarea:focus {
  border-color: var(--border-theme-color2);
}
.contact-form-home5 .form-group textarea {
  height: 170px;
  resize: none;
}
.contact-form-home5 .form-group input[type=submit],
.contact-form-home5 .form-group button {
  margin-top: 0px;
  line-height: 30px;
  padding: 15px 50px;
}

.home5-contact-img {
  background-image: url(../images/background/bg4.jpg);
  background-position: center center;
  background-size: cover;
  height: 594px;
  max-width: 194px;
  width: 100%;
}

.work-block-home5 .icon {
  -webkit-box-align: center;
  -ms-flex-align: center;
  background-color: var(--bg-theme-color2);
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color1);
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 60px;
  left: 0;
  position: absolute;
  top: 0;
  width: 60px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.work-block-home5 .inner-box {
  border-bottom: 1px solid #e2e0e5;
  margin-bottom: 35px;
  padding-left: 80px;
  padding-bottom: 35px;
  position: relative;
}
@media only screen and (max-width: 1023px) {
  .work-block-home5 .inner-box {
    padding-left: 0;
    text-align: left;
  }
}
.work-block-home5 .inner-box:hover .icon {
  background-color: var(--bg-theme-color1);
  color: var(--theme-color2);
  -webkit-transform: scale(-1) rotate(-180deg);
  transform: scale(-1) rotate(-180deg);
}

.why-choose-us-home5 {
  position: relative;
}
.why-choose-us-home5:before {
  background-image: url(../images/icons/icon-dots-3.png);
  background-position: center right;
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 50%;
}
.why-choose-us-home5 .content-column {
  position: relative;
  z-index: 1;
  margin-bottom: 50px;
}
.why-choose-us-home5 .content-column .inner-column {
  position: relative;
}
.why-choose-us-home5 .content-column .sec-title {
  margin-bottom: 40px;
}
.why-choose-us-home5 .content-column .feature-box {
  position: relative;
  padding-left: 100px;
  padding-top: 5px;
  min-height: 70px;
  margin-bottom: 30px;
}
.why-choose-us-home5 .content-column .feature-box:hover .icon {
  color: var(--theme-color1);
}
.why-choose-us-home5 .content-column .feature-box:hover .icon:after {
  background-color: #ebebeb;
}
.why-choose-us-home5 .content-column .feature-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 64px;
  line-height: 1em;
  color: var(--theme-color2);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.why-choose-us-home5 .content-column .feature-box .icon:after {
  position: absolute;
  left: 25px;
  top: 15px;
  height: 54px;
  width: 54px;
  background-color: var(--bg-theme-color1);
  border-radius: 50%;
  z-index: -2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  content: "";
}
.why-choose-us-home5 .content-column .theme-btn {
  margin-top: 20px;
}
.why-choose-us-home5 .image-column {
  position: relative;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1199px) {
  .why-choose-us-home5 .image-column {
    display: none;
  }
}
.why-choose-us-home5 .image-column .image-box {
  position: relative;
  margin-left: -24px;
}
.why-choose-us-home5 .image-column .image {
  position: relative;
  display: block;
  background-color: var(--bg-theme-color1);
  margin-bottom: 0;
}
.why-choose-us-home5 .image-column .image img {
  width: 100%;
}
.why-choose-us-home5 .image-column .image:before {
  background-color: rgba(8, 10, 11, 0.6);
  background-image: url(../images/icons/shape-2.png);
  background-position: center;
  background-size: cover;
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.why-choose-us-home5 .image-column .inner-column {
  position: relative;
  margin-left: -375px;
  padding-right: 100px;
}
@media only screen and (max-width: 1199px) {
  .why-choose-us-home5 .info-column {
    width: 100%;
  }
}
.why-choose-us-home5 .info-column .inner-column {
  position: relative;
  padding-left: 36px;
}
@media only screen and (max-width: 1199px) {
  .why-choose-us-home5 .info-column .inner-column {
    padding-left: 0;
  }
}
.why-choose-us-home5 .info-column .info {
  position: relative;
  padding-bottom: 30px;
  border-bottom: 5px solid var(--bg-theme-color2);
  margin-bottom: 40px;
}
.why-choose-us-home5 .info-column .info:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: 0;
}
.why-choose-us-home5 .info-column .info .title {
  position: relative;
  top: -5px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 1023px) {
  .why-choose-us-home5 .info-column .info .title br {
    display: none;
  }
}
.why-choose-us-home5 .info-column .info-box {
  position: relative;
  padding: 30px 50px;
  background-color: var(--bg-theme-color2);
  margin-left: -60px;
  margin-right: 70px;
  margin-top: 30px;
}
@media only screen and (max-width: 1199px) {
  .why-choose-us-home5 .info-column .info-box {
    margin-left: 0;
    margin-right: 0;
  }
}
@media only screen and (max-width: 1023px) {
  .why-choose-us-home5 .info-column .info-box {
    padding: 20px 30px;
  }
}
.why-choose-us-home5 .info-column .info-box:before {
  position: absolute;
  left: -35px;
  bottom: 0;
  border-right: 35px solid var(--border-theme-color2);
  border-top: 45px solid transparent;
  content: "";
}
@media only screen and (max-width: 1199px) {
  .why-choose-us-home5 .info-column .info-box:before {
    display: none;
  }
}
.why-choose-us-home5 .info-column .info-box .icon {
  display: block;
  font-size: 28px;
  color: #fff;
  margin-bottom: 8px;
}
.why-choose-us-home5 .info-column .info-box .title {
  margin-bottom: 0;
  color: #fff;
}
@media only screen and (max-width: 1023px) {
  .why-choose-us-home5 .info-column .info-box .title br {
    display: none;
  }
}

/*  News Block Home 6  */
.news-block-home6 {
  position: relative;
  margin-bottom: 50px;
}
.news-block-home6 .inner-box {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.news-block-home6 .inner-box:hover .read-more {
  background-color: var(--bg-theme-color2);
  color: #ffffff;
}
.news-block-home6 .inner-box:hover .image-box .image a:after {
  left: 0;
  right: 0;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.news-block-home6 .inner-box:hover .image-box .image img {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.news-block-home6 .image-box {
  position: relative;
}
.news-block-home6 .image-box .image {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}
.news-block-home6 .image-box .image img {
  display: block;
  width: 100%;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.news-block-home6 .image-box .image a:after {
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  right: 51%;
  top: 0;
  opacity: 1;
  pointer-events: none;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.news-block-home6 .image-box .date {
  position: absolute;
  left: 20px;
  top: 0px;
  background: var(--theme-color1);
  color: #ffffff;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 60px;
  width: 60px;
  text-align: center;
  font-size: 16px;
  line-height: 1.2em;
  font-weight: 500;
  text-transform: uppercase;
  font-family: var(--title-font);
}
.news-block-home6 .lower-content {
  position: relative;
  margin-left: 20px;
  margin-right: 20px;
  padding: 0px 10px 10px;
  margin-top: -32px;
  z-index: 2;
  background-color: #ffffff;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.05);
  border-top: 4px solid var(--theme-color1);
  text-align: center;
}
.news-block-home6 .author-thumb {
  position: relative;
  height: 60px;
  width: 60px;
  border-radius: 50%;
  margin-top: -30px;
  overflow: hidden;
  border: 2px solid var(--theme-color1);
  display: inline-block;
  margin-bottom: 5px;
}
.news-block-home6 .post-info {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 5px;
}
.news-block-home6 .post-info li {
  position: relative;
  font-size: 14px;
  line-height: 25px;
  color: var(--theme-color2);
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-family: var(--title-font);
  margin: 0 5px;
}
.news-block-home6 .post-info li i {
  margin-right: 5px;
  color: var(--theme-color1);
  font-size: 14px;
}
.news-block-home6 .title {
  padding: 0 15px;
  line-height: 1.4em;
  font-weight: 600;
}
.news-block-home6 .title:hover {
  color: var(--theme-color1);
}
.news-block-home6 .read-more {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font-size: 14px;
  line-height: 25px;
  padding: 10px 25px;
  text-transform: uppercase;
  font-weight: 600;
  color: #717070;
  font-family: var(--title-font);
  background-color: #f6f6f6;
  letter-spacing: 0.1em;
  border-radius: 3px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/* Call To Action At Home 6  */
.call-to-action-home6 {
  position: relative;
  padding: 120px 0;
  z-index: 2;
}
.call-to-action-home6 .outer-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #0F0F0F;
  overflow: hidden;
}
.call-to-action-home6 .outer-box:before {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 50%;
  background-image: url(../images/icons/bg-pattern1.png);
  background-position: center right;
  background-repeat: no-repeat;
  content: "";
}
.call-to-action-home6 .content-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  padding: 80px;
}
@media only screen and (max-width: 1023px) {
  .call-to-action-home6 .content-box {
    display: block;
    padding: 40px 30px;
  }
}

/*** 

====================================================================
    Map Section
====================================================================

***/
.map-section {
  position: relative;
  min-height: 480px;
}
.map-section .map {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  display: block;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

/*** 

====================================================================
    Clients Section
====================================================================

***/
.clients-section {
  position: relative;
  z-index: 1;
}
.clients-section .sponsors-outer {
  padding: 70px 0px;
}

.client-block {
  position: relative;
  overflow: hidden;
  text-align: center;
}
.client-block a {
  position: relative;
  padding: 20px 30px;
  display: inline-block;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  text-align: center;
}
.client-block a:hover {
  background-color: #f6f6f6;
}
.client-block a:hover img {
  opacity: 1;
}
.client-block img {
  display: inline-block !important;
  width: auto !important;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/***

==================================================================
    Pricing Section
==================================================================

***/
.pricing-section {
  position: relative;
  overflow: hidden;
  padding: 120px 0 70px;
}
.pricing-section .content-column {
  position: relative;
}
.pricing-section .content-column .inner-column {
  position: relative;
  padding-right: 70px;
}
.pricing-section .content-column .sec-title {
  margin-bottom: 30px;
}
.pricing-section .content-column .info-box {
  position: relative;
  z-index: 9;
  padding-left: 90px;
  margin-bottom: 40px;
}
@media (max-width: 767.98px) {
  .pricing-section .content-column .info-box {
    padding-left: 0;
  }
}
.pricing-section .content-column .info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 58px;
  width: 58px;
  color: var(--theme-color1);
  background-color: var(--bg-theme-color2);
  border-radius: 50%;
}
@media (max-width: 767.98px) {
  .pricing-section .content-column .info-box .icon {
    position: relative;
    margin-bottom: 20px;
  }
}
.pricing-section .content-column .info-box .title {
  color: var(--theme-color1);
  margin-bottom: 20px;
}
.pricing-section .content-column .info-box .text {
  letter-spacing: 0.01em;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-section .pricing-column {
  position: relative;
}
.pricing-section .pricing-column .inner-column {
  position: relative;
  margin-left: -30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.pricing-block {
  position: relative;
  margin-bottom: 40px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-block.pull-left {
  margin-left: -70px;
  z-index: 1;
}
@media (max-width: 767.98px) {
  .pricing-block.pull-left {
    margin-left: 0;
  }
}
.pricing-block .inner-box {
  position: relative;
  padding: 58px 60px 60px;
  background-color: var(--bg-theme-color2);
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  max-width: 370px;
  width: 100%;
}
.pricing-block .inner-box:before {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 286px;
  height: 271px;
  opacity: 0.5;
  content: "";
}
.pricing-block .inner-box:hover .image img {
  -webkit-transform: rotate(180deg) scale(-1);
          transform: rotate(180deg) scale(-1);
}
.pricing-block .image {
  position: relative;
  margin-bottom: 15px;
}
.pricing-block .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-block .price-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 25px;
}
.pricing-block .price-box .price {
  display: block;
  font-size: 46px;
  line-height: 1em;
  font-weight: 700;
  color: #fff;
}
.pricing-block .price-box .price sup {
  font-size: 60%;
}
.pricing-block .price-box .validaty {
  position: relative;
  font-size: 12px;
  text-transform: uppercase;
  color: #fff;
  letter-spacing: 0.1em;
  margin-left: 2px;
  top: 3px;
}
.pricing-block .title {
  position: relative;
  display: block;
  line-height: 1.2em;
  color: #ffffff;
  font-weight: 700;
  margin-bottom: 30px;
}
.pricing-block .features {
  position: relative;
  margin-bottom: 35px;
}
.pricing-block .features li {
  position: relative;
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: #8c8f94;
  font-weight: 400;
  margin-bottom: 10px;
}
.pricing-block .btn-box {
  position: relative;
}
.pricing-block.style-two .inner-box {
  background-color: #fff;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  padding: 38px 60px 40px;
}
.pricing-block.style-two .inner-box:before {
  opacity: 0.4;
}
.pricing-block.style-two .price-box {
  border-bottom: 1px solid #e6e8ed;
}
.pricing-block.style-two .price-box .validaty,
.pricing-block.style-two .price-box .price {
  color: var(--theme-color1);
}
.pricing-block.style-two:hover {
  margin-left: 0;
}

/***

====================================================================
    Products details
====================================================================

***/
.product-details .bxslider .image-box {
  position: relative;
  display: block;
  margin-right: 30px;
  margin-bottom: 10px;
}
.product-details .bxslider .image-box img {
  width: 100%;
}
.product-details .bxslider .thumb-box li {
  position: relative;
  display: inline-block;
  float: left;
  margin-right: 10px;
  margin-bottom: 15px;
  width: 100px;
  height: 100px;
}
.product-details .bxslider .thumb-box li:last-child {
  margin: 0px !important;
}
.product-details .bxslider .thumb-box li a {
  position: relative;
  display: inline-block;
}
.product-details .bxslider .thumb-box li a:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  opacity: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.product-details .bxslider .thumb-box li a.active:before {
  opacity: 1;
}
.product-details .bx-wrapper {
  margin-bottom: 30px;
}
.product-details .product-info .product-details__top {
  position: relative;
  display: block;
  margin-top: -8px;
}
.product-details .product-info .product-details__title {
  font-size: 40px;
  line-height: 44px;
  font-weight: 700;
  margin: 0;
}
.product-details .product-info .product-details__title span {
  position: relative;
  display: inline-block;
  color: var(--theme-color1);
  font-size: 20px;
  line-height: 26px;
  font-weight: 700;
  margin-left: 20px;
  letter-spacing: 0;
}
.product-details .product-info .product-details__reveiw {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 22px;
  padding-bottom: 20px;
  margin-bottom: 31px;
  border-bottom: 1px solid #e0e4e8;
}
.product-details .product-info .product-details__reveiw i {
  font-size: 16px;
  color: var(--theme-color2);
}
.product-details .product-info .product-details__reveiw i + i {
  margin-left: 4px;
}
.product-details .product-info .product-details__reveiw span {
  position: relative;
  top: 1px;
  line-height: 1;
  font-size: 16px;
  color: var(--theme-color1);
  margin-left: 18px;
}
.product-details .product-info .product-details__quantity-title {
  margin: 0;
  color: #222;
  font-size: 20px;
  line-height: 30px;
  font-weight: 700;
  margin-right: 20px;
}
.product-details .product-info .product-details__buttons {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 30px;
  margin-top: 40px;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons {
    display: block;
  }
}
.product-details .product-info .product-details__buttons-1 {
  position: relative;
  display: block;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons-1 {
    margin-bottom: 10px;
    margin-right: 10px;
  }
}
.product-details .product-info .product-details__buttons-2 {
  position: relative;
  display: block;
  margin-left: 10px;
}
.product-details .product-info .product-details__buttons-2 .thm-btn {
  background-color: var(--theme-color2);
}
.product-details .product-info .product-details__buttons-2 .thm-btn:before {
  background-color: var(--theme-color1);
}
.product-details .product-info .product-details__buttons-2 .thm-btn:after {
  background-color: var(--theme-color1);
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons-2 {
    margin-left: 0;
    margin-top: 0;
  }
}
.product-details .product-info .product-details__social {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.product-details .product-info .product-details__social .title {
  position: relative;
  display: block;
}
.product-details .product-info .product-details__social .title h3 {
  color: #222;
  font-size: 20px;
  line-height: 20px;
  font-weight: 700;
}
.product-details .product-info .product-details__social .social-icon-one {
  margin-left: 30px;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__social {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: start;
  }
  .product-details .product-info .product-details__social .social-icon-one {
    margin-left: 0;
  }
}

.product-details__quantity {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 20px;
}
.product-details__quantity .quantity-box {
  position: relative;
  width: 98px;
  border-radius: 10px;
  height: 60px;
}
.product-details__quantity .quantity-box input {
  width: 98px;
  border-radius: 10px;
  height: 60px;
  border: 1px solid #e0e4e8;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  padding-left: 30px;
  outline: none;
  font-size: 18px;
  font-weight: 500;
  color: var(--theme-color1);
}
.product-details__quantity .quantity-box button {
  width: 29px;
  height: 29px;
  background-color: transparent;
  color: var(--text-color-bg-theme-color1);
  font-size: 8px;
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: var(--theme-color1);
  border: none;
  border-left: 1px solid #e0e4e8;
  border-top-right-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  outline: none;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-details__quantity .quantity-box button:hover {
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
}
.product-details__quantity .quantity-box button.sub {
  bottom: 1px;
  top: auto;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 10px;
}

.product-discription {
  position: relative;
  display: block;
}
.product-discription .product-description__title {
  font-size: 30px;
  margin-bottom: 27px;
}
.product-discription .product-description__text1 {
  font-size: 16px;
  line-height: 30px;
  margin: 0;
}
.product-discription .product-description__list {
  position: relative;
  display: block;
  margin-top: 30px;
  margin-bottom: 30px;
}
.product-discription .product-description__list ul {
  position: relative;
  display: block;
}
.product-discription .product-description__list ul li {
  position: relative;
  display: block;
  margin-bottom: 2px;
}
.product-discription .product-description__list ul li:last-child {
  margin-bottom: 0px;
}
.product-discription .product-description__list ul li p {
  margin: 0;
  font-weight: 600;
  color: var(--headings-color);
}
.product-discription .product-description__list ul li p span:before {
  position: relative;
  display: inline-block;
  color: var(--theme-color1);
  font-size: 17px;
  line-height: 17px;
  margin-right: 11px;
  top: 2px;
}
.product-discription .tabs-content .text p {
  margin-bottom: 17px;
}
.product-discription .tabs-content .text p:last-child {
  margin-bottom: 0px;
}
.product-discription .tab-btn-box {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 60px;
}
.product-discription .tab-btn-box:before {
  position: absolute;
  content: "";
  background-color: #e1e8e4;
  width: 100%;
  height: 1px;
  left: 0px;
  top: 28px;
}
.product-discription .tab-btn-box .tab-btns li {
  position: relative;
  display: inline-block;
  font-size: 14px;
  text-transform: uppercase;
  color: #1e2434;
  text-align: center;
  padding: 14px 30px;
  background-color: #fff;
  border: 1px solid #e1e8e4;
  cursor: pointer;
  margin: 0px 8.5px;
  margin-bottom: 15px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.product-discription .tab-btn-box .tab-btns li.active-btn {
  color: var(--text-color-bg-theme-color1);
  background-color: var(--theme-color1);
}
.product-discription .tab-btn-box .tab-btns li:last-child {
  margin-bottom: 0;
}
.product-discription .single-comment-box .inner-box {
  position: relative;
  display: block;
  background-color: #f4f5f4;
  padding: 34px 30px 34px 125px;
}
.product-discription .single-comment-box .inner-box .comment-thumb {
  position: absolute;
  left: 30px;
  top: 40px;
  border-radius: 50%;
  width: 80px;
}
.product-discription .single-comment-box .inner-box .comment-thumb img {
  width: 100%;
  border-radius: 50%;
}
.product-discription .single-comment-box .inner-box .rating {
  position: relative;
  display: block;
  margin-bottom: 2px;
}
.product-discription .single-comment-box .inner-box .rating li {
  position: relative;
  display: inline-block;
  font-size: 12px;
  float: left;
  margin-right: 4px;
  color: #fdc009;
}
.product-discription .single-comment-box .inner-box .rating li:last-child {
  margin: 0px !important;
}
.product-discription .single-comment-box .inner-box h5 {
  display: block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 16px;
}
.product-discription .single-comment-box .inner-box h5 span {
  font-weight: 400;
  text-transform: capitalize;
}
.product-discription .customer-comment {
  position: relative;
  display: block;
  margin-bottom: 60px;
}
.product-discription .comment-box {
  position: relative;
  display: block;
  background-color: #fff;
  padding: 51px 60px 60px 60px;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
.product-discription .comment-box h3 {
  display: block;
  font-size: 24px;
  line-height: 34px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 23px;
}
.product-discription .comment-box .form-group {
  position: relative;
  margin-bottom: 15px;
}
.product-discription .comment-box .form-group label {
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 28px;
  color: #707582;
  margin-bottom: 8px;
}
.product-discription .comment-box .column:last-child .form-group {
  margin-bottom: 0px;
}
.product-discription .comment-box .review-box {
  position: relative;
  display: block;
  margin-top: 8px;
}
.product-discription .comment-box .review-box p {
  position: relative;
  float: left;
  margin-right: 10px;
}
.product-discription .comment-box .review-box .rating {
  position: relative;
  float: left;
}
.product-discription .comment-box .review-box .rating li {
  position: relative;
  display: inline-block;
  font-size: 14px;
  line-height: 28px;
  float: left;
  margin-right: 4px;
  color: #fdc009;
}
.product-discription .comment-box .review-box .rating li:last-child {
  margin: 0px !important;
}
.product-discription .comment-box .custom-controls-stacked {
  position: relative;
  float: left;
}

.related-product h3 {
  margin-bottom: 30px;
}

@media only screen and (max-width: 767px) {
  .product-details__img {
    margin-bottom: 50px;
  }
  .product-details__title span {
    margin-left: 0;
    display: block;
  }
  .product-details__buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
  .product-details__buttons-2 {
    margin-left: 0;
    margin-top: 10px;
  }
  .product-details__social-link {
    margin-left: 0;
    margin-top: 20px;
  }
}
/***

====================================================================
 Categories Section
====================================================================

***/
.categories-section {
  position: relative;
  padding: 100px 0 70px;
}
.categories-section .bg-pattern {
  position: absolute;
  left: 0;
  top: -220px;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url(../images/icons/pattern-7.png);
  background-repeat: no-repeat;
  background-position: left top;
  z-index: -1;
}
.categories-section:before {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 300px;
  width: 100%;
  background: var(--theme-color3);
  content: "";
  z-index: 1;
}
.categories-section:after {
  position: absolute;
  left: 0;
  bottom: -50px;
  height: 70px;
  width: 100%;
  z-index: 2;
  content: "";
  background-image: url(../images/icons/pattern-3.png);
  background-repeat: no-repeat;
  background-position: center bottom;
}

.category-block {
  position: relative;
  margin-bottom: 30px;
  z-index: 9;
}
.category-block .inner-box {
  position: relative;
  text-align: center;
  background: #ffffff;
  padding: 40px 30px 30px;
  border-radius: 10px;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block .inner-box:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 190px;
  background-repeat: no-repeat;
  background-position: center bottom;
  content: "";
}
.category-block .inner-box:hover {
  -webkit-transform: translateY(-20px);
  transform: translateY(-20px);
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.category-block .inner-box:hover .image img {
  -webkit-transform: rotate(10deg) scale(1.2);
  transform: rotate(10deg) scale(1.2);
}
.category-block .inner-box.cat-bg-1:before {
  background-image: url(../images/icons/cat-1-bg.png);
}
.category-block .inner-box.cat-bg-2:before {
  background-image: url(../images/icons/cat-2-bg.png);
}
.category-block .inner-box.cat-bg-3:before {
  background-image: url(../images/icons/cat-3-bg.png);
}
.category-block .inner-box.cat-bg-4:before {
  background-image: url(../images/icons/cat-4-bg.png);
}
.category-block .image {
  position: relative;
  display: inline-block;
  height: 180px;
  width: 180px;
  overflow: hidden;
  border-radius: 50%;
  margin-bottom: 15px;
}
.category-block .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block h4 {
  font-size: 20px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 10px;
}
.category-block h4 a {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block h4 a:hover {
  color: var(--theme-color1);
}
.category-block p {
  font-size: 14px;
  color: #797f7d;
  line-height: 26px;
  margin-bottom: 0px;
}

/***

====================================================================
    Categories Section Two
====================================================================

***/
.categories-section-two {
  position: relative;
  padding: 120px 0 90px;
}

.category-block-two {
  position: relative;
  padding-top: 70px;
  margin-bottom: 30px;
  z-index: 9;
}
.category-block-two .inner-box {
  position: relative;
  text-align: center;
  background: #ffffff;
  border-radius: 10px;
  margin: 0 auto;
  padding: 18px;
}
.category-block-two .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 60px;
  width: 100%;
  border-radius: 10px;
  background: #e3eee5;
  content: "";
}
.category-block-two .inner-box:hover .image:before {
  left: 100%;
  -webkit-transition: all 1000ms ease;
  transition: all 1000ms ease;
}
.category-block-two .inner-box:hover .image img {
  -webkit-transform: scale(0.9);
  transform: scale(0.9);
}
.category-block-two .inner-box:hover h4 a {
  color: var(--theme-color1);
}
.category-block-two .content {
  position: relative;
  background: #ffffff;
  border-radius: 10px;
  padding: 0 40px 40px;
  z-index: 1;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
}
.category-block-two .content:before {
  position: absolute;
  top: -88px;
  left: 0;
  width: 180px;
  height: 180px;
  right: 0;
  margin: 0 auto;
  border-radius: 50%;
  background: #e3eee5;
  content: "";
}
.category-block-two .content:after {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  font-size: 30px;
  line-height: 1em;
  color: #e8f3ea;
  height: 15px;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  content: attr(data-text);
}
.category-block-two .image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  margin-bottom: 25px;
  margin-top: -70px;
}
.category-block-two .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two .image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 120%;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgb(255, 255, 255)));
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#00ffffff",GradientType=1 );
  content: "";
  opacity: 0.3;
  left: -100%;
  pointer-events: none;
  z-index: 1;
}
.category-block-two h4 {
  font-size: 22px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 15px;
}
.category-block-two h4 a {
  color: var(--theme-color3);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two p {
  font-size: 16px;
  color: #797f7d;
  line-height: 26px;
  margin-bottom: 0px;
}
.category-block-two .link {
  position: relative;
  display: inline-block;
  height: 50px;
  width: 50px;
  background: #e8f3ea;
  border-radius: 50%;
  line-height: 50px;
  margin-top: 25px;
  color: #608174;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two .link:hover {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
.category-block-two.child-two .link {
  background: #f0e2e3;
}
.category-block-two.child-two .inner-box:before {
  background: #f0e2e3;
}
.category-block-two.child-two .inner-box:hover h4 a {
  color: #e69da2;
}
.category-block-two.child-two .content:before {
  background: #f0e2e3;
}
.category-block-two.child-two .content:after {
  color: #f0e2e3;
}
.category-block-two.child-three .link {
  background: #f1ede1;
}
.category-block-two.child-three .inner-box:before {
  background: #f1ede1;
}
.category-block-two.child-three .inner-box:hover h4 a {
  color: #c9b579;
}
.category-block-two.child-three .content:before {
  background: #f1ede1;
}
.category-block-two.child-three .content:after {
  color: #f1ede1;
}

/***

====================================================================
    Products Section
====================================================================

***/
.products-section {
  position: relative;
  padding: 120px 0;
}

.products-section .bg-image {
  position: absolute;
  left: 0;
  top: 0;
  height: 670px;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-attachment: fixed;
}

.products-section .bg-image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #113629;
  opacity: 0.7;
  content: "";
}

.products-section .bg-image:after {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 50px;
  width: 100%;
  background-image: url(../images/icons/pattern-8.png);
  background-position: center bottom;
  content: "";
}

.products-section .sec-title h2 {
  font-size: 60px;
}

.products-section .sec-title .theme-btn {
  margin-top: 30px;
}

.products-box {
  max-width: 1530px;
  position: relative;
  padding: 120px 60px 90px;
  margin: 120px auto 0;
  background-color: #f7f5ee;
  overflow: hidden;
  border-radius: 10px;
}

.products-box:before {
  position: absolute;
  left: -90px;
  bottom: 0;
  height: 70%;
  width: 100%;
  background: url(../images/icons/shape-7.png) top left no-repeat;
  content: "";
}

.products-box .sec-title {
  margin-bottom: 30px;
}

.products-box .outer-box {
  position: relative;
  padding-right: 400px;
}

.products-box .outer-box .banner-box-two {
  position: absolute;
  right: 0;
  top: 0;
}

.banner-box-two {
  position: relative;
}

.banner-box-two .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: -webkit-gradient(linear, left top, left bottom, from(rgb(22, 67, 51)), to(rgba(229, 229, 229, 0)));
  background: linear-gradient(to bottom, rgb(22, 67, 51) 0%, rgba(229, 229, 229, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="var(--theme-color3)", endColorstr="#00e5e5e5",GradientType=0 );
  content: "";
}

.banner-box-two .inner-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 370px;
  background: var(--theme-color3);
  border-radius: 10px;
  min-height: 440px;
  text-align: center;
  overflow: hidden;
  padding: 20px 20px;
}

.banner-box-two .title {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  font-size: 20px;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 20px;
}

.banner-box-two .title strong {
  font-size: 60px;
  line-height: 1em;
  color: var(--theme-color2);
  font-weight: 400;
}

.banner-box-two h4 {
  font-size: 30px;
  line-height: 1.2em;
  color: #ffffff;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 30px;
}

.product-block-two {
  position: relative;
  margin-bottom: 30px;
}

.product-block-two .inner-box {
  position: relative;
  border: 2px solid transparent;
  border-radius: 10px;
  background: #ffffff;
  padding: 20px 20px;
  padding-left: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  min-height: 150px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .inner-box:hover {
  border: 2px solid var(--theme-color1);
}

.product-block-two .image {
  position: absolute;
  left: 20px;
  top: 20px;
  border-radius: 50%;
  overflow: hidden;
  height: 110px;
  width: 110px;
  border: 1px solid #e4e1d6;
  margin-bottom: 0px;
}

.product-block-two .image img {
  width: auto;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .inner-box:hover .image img {
  -webkit-transform: scale(-1) rotate(180deg);
  transform: scale(-1) rotate(180deg);
}

.product-block-two h4 {
  display: block;
  font-size: 22px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 5px;
}

.product-block-two h4 a {
  color: var(--theme-color3);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .price {
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: var(--theme-color1);
  font-weight: 600;
}

.product-block-two .price del {
  display: inline-block;
  margin-left: 15px;
  font-size: 16px;
  color: #ff0000;
  line-height: 27px;
  opacity: 0.3;
}

.product-block-two .rating {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 14px;
  color: #ffc737;
}

.products-carousel .owl-nav {
  display: none;
}

.products-carousel .owl-dots {
  position: absolute;
  right: 0;
  top: -80px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 8;
}

.products-carousel .owl-dot {
  position: relative;
  height: 7px;
  width: 7px;
  display: block;
  background: #879d91;
  margin-right: 5px;
  border-radius: 5px;
  display: block;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.products-carousel .owl-dot.active {
  background: var(--theme-color3);
}

/***

====================================================================
    Featured Products
====================================================================

***/
.featured-products {
  position: relative;
  padding: 120px 0 90px;
}

.featured-products .bg-shape {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: url(../images/icons/pattern-7.png) left bottom no-repeat;
  background-size: 700px;
}

/*=== Mixitup Gallery ===*/
.featured-products .filters {
  margin-bottom: 40px;
  text-align: center;
}

.featured-products .filters .filter-tabs {
  position: relative;
  display: inline-block;
}

.featured-products .filters li {
  position: relative;
  display: inline-block;
  line-height: 24px;
  padding: 0px 2px 10px;
  cursor: pointer;
  color: #797f7d;
  font-weight: 500;
  font-size: 18px;
  margin: 0 12px 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.featured-products .filters li:last-child {
  margin-right: 0;
}

.featured-products .filters .filter.active,
.featured-products .filters .filter:hover {
  color: var(--theme-color1);
}

.featured-products .filters li:before {
  position: absolute;
  left: 0;
  bottom: 8px;
  height: 2px;
  width: 100%;
  content: "";
  background-color: var(--theme-color2);
  -webkit-transform: scale(0, 1);
  transform: scale(0, 1);
  -webkit-transform-origin: top right;
  transform-origin: top right;
  -webkit-transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1), -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1); /* easeInOutQuint */
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1); /* easeInOutQuint */
}

.featured-products .filters li.active:before,
.featured-products .filters li:hover:before {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
  -webkit-transform-origin: bottom left;
  transform-origin: bottom left;
}

.product-block {
  position: relative;
  margin-bottom: 30px;
}

.product-block.mix {
  display: none;
}

.product-block .inner-box {
  position: relative;
  border: 2px solid #e4e1d5;
  border-radius: 10px;
  text-align: center;
  background: #ffffff;
  overflow: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover {
  border: 2px solid var(--theme-color1);
  -webkit-box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
}

.product-block .image {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  margin-bottom: 0px;
}

.product-block .image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgb(255, 255, 255)));
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#00ffffff",GradientType=1 );
  content: "";
  opacity: 0.3;
  left: -100%;
  pointer-events: none;
  z-index: 1;
}

.product-block .inner-box:hover .image:before {
  left: 100%;
  -webkit-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.product-block .image img {
  width: auto;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover .image img {
  -webkit-transform: scale(0.9);
  transform: scale(0.9);
}

.product-block .content {
  position: relative;
  padding: 30px 30px 30px;
}

.product-block h4 {
  display: block;
  font-size: 22px;
  color: var(--theme-color2);
  font-weight: 700;
  margin-bottom: 5px;
}

.product-block h4 a {
  color: var(--theme-color2);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block h4 a:hover {
  color: var(--theme-color1);
}

.product-block .price {
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: var(--text-gray-silver);
  font-weight: 600;
}

.product-block .price del {
  display: inline-block;
  margin-left: 15px;
  font-size: 16px;
  color: #ff0000;
  line-height: 27px;
  opacity: 0.3;
}

.product-block .rating {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 14px;
  color: #ffc737;
}

.product-block .tag {
  position: absolute;
  top: 30px;
  left: 20px;
  font-size: 14px;
  line-height: 23px;
  color: #ffffff;
  background: #FD5F5C;
  font-weight: 400;
  padding: 0 12px;
  border-radius: 3px;
  z-index: 9;
  font-style: italic;
  text-transform: uppercase;
}

.product-block .icon-box {
  position: absolute;
  right: 20px;
  top: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover .icon-box {
  top: 20px;
  opacity: 1;
  visibility: visible;
  -webkit-transition-delay: 300ms;
  transition-delay: 300ms;
}

.product-block .ui-btn {
  position: relative;
  display: block;
  height: 40px;
  width: 40px;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  color: #ffffff;
  z-index: 9;
  background-color: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
  cursor: pointer;
  border-radius: 50px;
  margin-bottom: 10px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .ui-btn:hover {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}

.product-block .cat {
  display: block;
  font-size: 18px;
  color: #707070;
  font-style: italic;
  font-family: "Jost", sans-serif;
  margin-bottom: 5px;
}

/*
 * shop-catalog-layouts.scss
 * -----------------------------------------------
*/
table.tbl-shopping-cart .product-thumbnail, table.cart-total .product-thumbnail {
  min-width: 64px;
}
table.tbl-shopping-cart img, table.cart-total img {
  width: 64px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
table.tbl-shopping-cart th,
table.tbl-shopping-cart td, table.cart-total th,
table.cart-total td {
  vertical-align: middle;
  border-left: 1px solid #e3e3e3;
  padding: 20px 30px;
}
table.tbl-shopping-cart .product-name a, table.cart-total .product-name a {
  color: var(--headings-color);
}
table.tbl-shopping-cart .product-name .variation, table.cart-total .product-name .variation {
  font-size: 0.9rem;
  list-style: none;
}
table.tbl-shopping-cart .product-remove a, table.cart-total .product-remove a {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 24px;
  -webkit-transition: all 300ms ease-out;
  transition: all 300ms ease-out;
  background-color: #757575;
  color: #ffffff;
  text-align: center;
}
table.tbl-shopping-cart .coupon-form .apply-button, table.cart-total .coupon-form .apply-button {
  position: relative;
  display: inline-block;
  color: #1e2434;
  background: #f4f5f4;
  padding: 15px 29px;
  cursor: pointer;
  text-transform: uppercase;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
table.tbl-shopping-cart .coupon-form .apply-button:hover, table.cart-total .coupon-form .apply-button:hover {
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
}

table.tbl-shopping-cart > thead > tr > th,
table.tbl-shopping-cart > tbody > tr > th,
table.tbl-shopping-cart > tfoot > tr > th {
  color: #444;
}

.payment-method .accordion-box .block {
  background: #f4f5f4;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin-bottom: 20px;
}
.payment-method .accordion-box .block .acc-content .payment-info {
  position: relative;
  display: block;
  background-color: #fff;
  padding: 30px 30px 10px 30px;
}
.payment-method .accordion-box .block:last-child {
  margin-bottom: 0px;
}
.payment-method .accordion-box .block .acc-btn {
  padding: 19px 30px 22px 30px;
}
.payment-method .accordion-box .block .acc-btn .icon-outer {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  top: 50%;
  right: 30px;
  height: auto;
  /* line-height: 65px; */
  font-size: 28px;
  color: #1e2434;
}
.payment-method .accordion-box .block .acc-btn.active .icon-outer {
  color: var(--text-color-bg-theme-color2);
}
.payment-method .payment-method h3 {
  margin-bottom: 32px;
}

.shop-sidebar {
  position: relative;
  display: block;
}
.shop-sidebar .sidebar-search {
  margin-bottom: 30px;
}
.shop-sidebar .sidebar-search .search-form .form-group {
  position: relative;
  margin: 0px;
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search] {
  position: relative;
  width: 100%;
  height: 52px;
  background-color: var(--theme-light-background);
  border: 1px solid var(--theme-light-background);
  border-radius: 5px;
  color: #646578;
  padding: 10px 60px 10px 20px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .sidebar-search .search-form .form-group button {
  position: absolute;
  display: inline-block;
  top: 5px;
  right: 5px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  font-size: 18px;
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
  cursor: pointer;
  border-radius: 3px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .sidebar-search .search-form .form-group button:hover {
  color: var(--text-color-bg-theme-color1);
  background-color: var(--theme-color1);
}
.shop-sidebar .sidebar-widget {
  position: relative;
  display: block;
  background-color: var(--theme-light-background);
  padding: 35px 30px 37px 30px;
  border-radius: 5px;
  margin-bottom: 30px;
}
.shop-sidebar .sidebar-widget:last-child {
  margin-bottom: 0px;
}
.shop-sidebar .sidebar-widget .widget-title {
  position: relative;
  display: block;
  margin-bottom: 16px;
}
.shop-sidebar .category-widget .category-list li {
  position: relative;
  display: block;
  margin-bottom: 14px;
}
.shop-sidebar .category-widget .category-list li:last-child {
  margin-bottom: 0px;
}
.shop-sidebar .category-widget .category-list li a {
  position: relative;
  display: inline-block;
  color: #646578;
  font-weight: 400;
  padding-left: 20px;
}
.shop-sidebar .category-widget .category-list li a:before {
  position: absolute;
  content: "\f0da";
  font-family: "Font Awesome 6 Pro";
  left: 0px;
  top: 0px;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color2);
}
.shop-sidebar .category-widget .category-list li a:hover {
  color: var(--theme-color1);
}
.shop-sidebar .price-filters .widget-title {
  margin-bottom: 28px;
}
.shop-sidebar .post-widget {
  padding-bottom: 9px;
}
.shop-sidebar .post-widget .post {
  position: relative;
  padding-left: 90px;
  padding-bottom: 24px;
  margin-bottom: 23px;
  min-height: 108px;
  border-bottom: 1px solid #e1e1e1;
}
.shop-sidebar .post-widget .post:last-child {
  margin-bottom: 0px;
  border-bottom: none;
}
.shop-sidebar .post-widget .post .post-thumb {
  position: absolute;
  left: 0px;
  top: 7px;
  width: 70px;
  height: 70px;
  border: 1px solid #d0d4dd;
  border-radius: 5px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .post-widget .post .post-thumb img {
  width: 100%;
  border-radius: 5px;
}
.shop-sidebar .post-widget .post a {
  position: relative;
  display: inline-block;
  font-size: 16px;
  line-height: 26px;
  color: #646578;
  margin-bottom: 7px;
}
.shop-sidebar .post-widget .post .price {
  position: relative;
  display: block;
  font-size: 14px;
  line-height: 24px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  color: #0a267a;
}
.shop-sidebar .post-widget .post:hover .post-thumb {
  border-color: var(--theme-color1);
}
.shop-sidebar .post-widget .post:hover a {
  color: var(--theme-color1);
}

.range-slider {
  position: relative;
}
.range-slider .title {
  line-height: 26px;
  position: relative;
  display: inline-block;
  margin-right: 4px;
}
.range-slider .title:before {
  position: absolute;
  content: "$";
  left: -5px;
  top: -19px;
  color: #646578;
  font-size: 18px;
}
.range-slider p {
  position: relative;
  display: inline-block;
  color: #646578;
  margin-right: 10px !important;
}
.range-slider .input {
  color: #646578;
  max-width: 75px;
  font-size: 18px;
  margin-top: 5px;
  position: relative;
  display: inline-block;
}
.range-slider .input input {
  background: none;
  color: #646578;
  font-size: 15px;
  text-align: left;
}
.range-slider .ui-widget.ui-widget-content {
  height: 4px;
  border: none;
  margin-bottom: 14px;
  background-color: #d0d4dd;
  border-radius: 2px;
}
.range-slider .ui-slider .ui-slider-range {
  top: 0px;
  height: 4px;
  background-color: var(--theme-color1);
}
.range-slider .ui-state-default {
  top: -5px;
  width: 14px;
  height: 14px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 0px;
  background-color: var(--theme-color1);
}
.range-slider .ui-state-default:before {
  position: absolute;
  content: "";
  background-color: #ffffff;
  width: 6px;
  height: 6px;
  left: 4px;
  top: 4px;
  border-radius: 50%;
}
.range-slider .ui-widget-content .ui-state-default {
  top: -5px;
  width: 14px;
  height: 14px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 0px;
  background-color: var(--theme-color1);
}
.range-slider .ui-widget-content .ui-state-default:before {
  position: absolute;
  content: "";
  background-color: #ffffff;
  width: 6px;
  height: 6px;
  left: 4px;
  top: 4px;
  border-radius: 50%;
}
.range-slider input[type=submit] {
  position: relative;
  display: block;
  background: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
  float: right;
  text-align: center;
  border: none;
  font-size: 14px;
  font-weight: 500;
  margin-top: 0;
  text-transform: capitalize;
  cursor: pointer;
  padding: 7px 20px;
  border-radius: 10px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.range-slider input[type=submit]:hover {
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
}

/*** 

====================================================================
Page Title
====================================================================

***/
@-webkit-keyframes "ripple" {
  70% {
    -webkit-box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
@keyframes "ripple" {
  70% {
    -webkit-box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
.page-title {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 260px 0 120px;
  min-height: 250px;
  text-align: center;
}
@media only screen and (max-width: 768px) {
  .page-title {
    padding: 200px 0 110px;
  }
}
.page-title:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: #322c02;
  opacity: 0.75;
  content: "";
}
.page-title .title {
  font-size: 64px;
  color: #ffffff;
  margin-bottom: 17px;
}
.page-title .text {
  position: relative;
  color: #ffffff;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0.05em;
  max-width: 520px;
}

.page-breadcrumb {
  position: relative;
  margin-top: 5px;
}
.page-breadcrumb li {
  position: relative;
  display: inline-block;
  margin-right: 12px;
  padding-right: 13px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}
.page-breadcrumb li:after {
  position: absolute;
  content: "\f105";
  right: -6px;
  top: 1px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 900;
  font-family: "Font Awesome 6 Pro";
  color: #ffffff;
}
.page-breadcrumb li:last-child {
  padding-right: 0px;
  margin-right: 0px;
}
.page-breadcrumb li:last-child::after {
  display: none;
}
.page-breadcrumb li a {
  color: var(--theme-color1);
  font-weight: 500;
  text-transform: capitalize;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}
.page-breadcrumb li a:hover {
  color: #ffffff;
}

.page-title.style-two {
  background-position: center center;
}
.page-title.style-two .page-breadcrumb-outer {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 10px 0;
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  z-index: 8;
}
.page-title.style-two .page-breadcrumb li {
  color: rgb(7, 7, 16);
  font-weight: 600;
}
.page-title.style-two .page-breadcrumb li:after {
  color: rgb(7, 7, 16);
}
.page-title.style-two .page-breadcrumb li a {
  color: rgba(7, 7, 16, 0.6);
}
.page-title.style-two .page-breadcrumb li a:hover {
  color: rgb(7, 7, 16);
}

.play-now {
  position: relative;
  display: block;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-now .icon {
  position: relative;
  display: inline-block;
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: #ffffff;
  color: #ff6d2e;
  z-index: 1;
  padding-left: 0px;
  font-size: 14px;
  display: block;
  border-radius: 50%;
  -webkit-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -ms-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -o-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -webkit-transform-origin: center;
  transform-origin: center;
}
.play-now .ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
}
.play-now .ripple:before {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}
.play-now .ripple:after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

.background-image {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: repeat;
  background-position: center;
  background-size: cover;
}

.error-page__inner {
  position: relative;
  display: block;
  text-align: center;
}
.error-page__inner .error-page__title-box {
  position: relative;
  display: block;
}
.error-page__inner .error-page__title {
  position: relative;
  display: inline-block;
  font-size: 280px;
  line-height: 280px;
  margin-bottom: 0;
  color: var(--theme-color1);
}
.error-page__inner .error-page__sub-title {
  font-size: 40px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  line-height: 50px;
  margin-top: -16px;
}
.error-page__inner .error-page__text {
  font-size: 20px;
  line-height: 30px;
  text-align: center;
}
.error-page__inner .error-page__form {
  position: relative;
  display: block;
  margin: 42px auto 20px;
}
.error-page__inner .error-page__form input[type=search] {
  height: 60px;
  width: 100%;
  border: none;
  outline: none;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #333;
  padding-left: 50px;
  padding-right: 75px;
  border-radius: 7px;
}
.error-page__inner .error-page__form button[type=submit] {
  background-color: transparent;
  font-size: 22px;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  width: 72px;
  outline: none;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
}
.error-page__inner .error-page__form-input {
  position: relative;
  display: block;
  max-width: 570px;
  width: 100%;
  margin: 0 auto;
}

/***
=============================================
    Services Details
=============================================
***/
.service-sidebar {
  position: relative;
  display: block;
  max-width: 365px;
  width: 100%;
}
.service-sidebar .service-sidebar-single {
  position: relative;
  display: block;
  margin-bottom: 30px;
}
.service-sidebar .service-sidebar-single-services {
  position: relative;
  display: block;
  background: #f6f4ec;
  border-radius: 10px;
  padding: 35px 30px 25px;
}
.service-sidebar .service-sidebar-single-services .title {
  position: relative;
  display: block;
  margin-bottom: 12px;
  padding-left: 20px;
}
.service-sidebar .service-sidebar-single-services .title h3 {
  color: var(--headings-color);
  font-size: 20px;
  line-height: 30px;
  letter-spacing: -0.02em;
}
.service-sidebar .service-sidebar-single-services ul {
  position: relative;
  display: block;
  margin-top: 10px;
}
.service-sidebar .service-sidebar-single-services ul li {
  position: relative;
  display: block;
  margin-bottom: 5px;
  margin-top: -10px;
}
.service-sidebar .service-sidebar-single-services ul li:last-child {
  margin-bottom: 0;
}
.service-sidebar .service-sidebar-single-services ul li a {
  position: relative;
  display: block;
  color: var(--agriox-color-1, #687469);
  font-size: 18px;
  padding: 22px 20px 22px;
  border-radius: 10px;
  background: transparent;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.service-sidebar .service-sidebar-single-services ul li a:hover {
  color: var(--headings-color);
}
.service-sidebar .service-sidebar-single-services ul li a:hover::before {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(0deg);
          transform: perspective(400px) rotateX(0deg);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}
.service-sidebar .service-sidebar-single-services ul li a:hover i {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-services ul li a::before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  content: "";
  z-index: -1;
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(90deg);
          transform: perspective(400px) rotateX(90deg);
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  background: #ffffff;
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
.service-sidebar .service-sidebar-single-services ul li a i {
  font-size: 16px;
}
.service-sidebar .service-sidebar-single-services ul li.current a::before {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(0deg);
          transform: perspective(400px) rotateX(0deg);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}
.service-sidebar .service-sidebar-single-services ul li.current a i {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-services ul li.current:first-child {
  margin-top: 20px;
}
.service-sidebar .service-sidebar-single-services ul li.current:last-child {
  margin-bottom: 35px;
}
.service-sidebar .service-sidebar-single-contact-box {
  position: relative;
  display: block;
  background-attachment: scroll;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 10px;
  padding: 50px 0px 44px;
  z-index: 1;
}
.service-sidebar .service-sidebar-single-contact-box::before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(109, 140, 84, 0.93);
  border-radius: 10px;
  content: "";
  z-index: -1;
}
.service-sidebar .service-sidebar-single-contact-box .icon {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 50%;
  background: var(--theme-color2);
}
.service-sidebar .service-sidebar-single-contact-box .icon:hover {
  background-color: var(--headings-color);
}
.service-sidebar .service-sidebar-single-contact-box .icon:hover span::before {
  color: #fff;
}
.service-sidebar .service-sidebar-single-contact-box .icon span::before {
  position: relative;
  display: inline-block;
  color: var(--headings-color);
  font-size: 30px;
  line-height: 60px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-sidebar .service-sidebar-single-contact-box .title {
  position: relative;
  display: block;
  margin-top: 20px;
  margin-bottom: 42px;
}
.service-sidebar .service-sidebar-single-contact-box .title h2 {
  color: #ffffff;
  font-size: 36px;
}
.service-sidebar .service-sidebar-single-contact-box .phone {
  font-size: 24px;
  line-height: 34px;
}
.service-sidebar .service-sidebar-single-contact-box .phone a {
  color: #ffffff;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-sidebar .service-sidebar-single-contact-box .phone a:hover {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-contact-box p {
  color: #ffffff;
  font-size: 14px;
  line-height: 22px;
}
.service-sidebar .service-sidebar-single-btn {
  position: relative;
  display: block;
}
.service-sidebar .service-sidebar-single-btn .thm-btn {
  font-size: 16px;
  padding: 13px 50px 28px;
}
.service-sidebar .service-sidebar-single-btn .thm-btn span::before {
  position: relative;
  display: inline-block;
  top: 13px;
  color: #334b35;
  font-size: 40px;
  padding-right: 25px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
  font-weight: 500;
}
.service-sidebar .service-sidebar-single-btn .thm-btn:hover span::before {
  color: #ffffff;
}
.service-sidebar .banner-widget {
  position: relative;
  display: block;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.08);
}
.service-sidebar .banner-widget .widget-content {
  position: relative;
  display: block;
  width: 100%;
  padding: 45px 30px 40px 30px;
  background-size: cover;
  background-repeat: no-repeat;
}
.service-sidebar .banner-widget .widget-content .shape {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 278px;
  background-repeat: no-repeat;
  background-size: cover;
}
.service-sidebar .banner-widget .content-box {
  position: relative;
  max-width: 200px;
  width: 100%;
}
.service-sidebar .banner-widget .content-box .icon-box {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  font-size: 40px;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  text-align: center;
  border-radius: 5px;
  margin-bottom: 23px;
}
.service-sidebar .banner-widget .content-box .icon-box .icon-shape {
  position: absolute;
  top: -15px;
  right: -38px;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
}
.service-sidebar .banner-widget .content-box h3 {
  display: block;
  font-size: 24px;
  line-height: 32px;
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 22px;
}
.service-sidebar .banner-widget .content-box .theme-btn-two:hover {
  background: #0a267a;
}
.service-sidebar .service-sidebar-single-btn .theme-btn {
  padding: 20px 50px;
}
.service-sidebar .service-sidebar-single-btn .theme-btn .btn-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.service-sidebar .service-sidebar-single-btn .theme-btn span::before {
  position: relative;
  display: inline-block;
  font-size: 36px;
  padding-right: 25px;
  margin-top: 7px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
  font-weight: 500;
}

.services-details__content .feature-list .single-item {
  position: relative;
  display: block;
  border: 1px solid #e1e8e4;
  padding: 16px 30px 16px 53px;
  margin-bottom: 20px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
}
.services-details__content .feature-list .single-item .icon-box {
  color: var(--theme-color1);
  position: absolute;
  left: 20px;
  top: 16px;
  font-size: 18px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
}
.services-details__content .feature-list .single-item .title {
  display: block;
  margin: 0;
  font-size: 16px;
  line-height: 32px;
  font-weight: 600;
  text-transform: uppercase;
}
.services-details__content .feature-list .single-item:hover {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.services-details__content .feature-list .single-item:hover .icon-box {
  color: var(--text-color-bg-theme-color2);
}

.service-list li {
  position: relative;
  display: block;
  margin-bottom: 10px;
}
.service-list li:last-child {
  margin-bottom: 0px;
}
.service-list li a {
  position: relative;
  display: block;
  font-size: 18px;
  color: var(--headings-color);
  font-weight: 600;
  background-color: #fff;
  padding: 17px 20px 17px 50px;
  -webkit-box-shadow: 20px 5px 20px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 20px 5px 20px 0px rgba(0, 0, 0, 0.05);
}
.service-list li a:hover, .service-list li a.current {
  color: var(--text-color-bg-theme-color1);
  background-color: var(--theme-color1);
  padding-left: 80px;
}
.service-list li a:hover i, .service-list li a.current i {
  width: 60px;
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
}
.service-list li i {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  left: 0px;
  top: 0px;
  width: 30px;
  height: 100%;
  background-color: #f6f4ec;
  text-align: center;
  font-size: 16px;
  color: #707582;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.sidebar-service-list {
  margin: 0;
}
.sidebar-service-list li + li {
  margin-top: 10px;
}
.sidebar-service-list li a {
  font-size: 18px;
  font-weight: 700;
  position: relative;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  background-color: #f5faff;
  border-radius: 15px;
  padding: 19px 40px;
}
.sidebar-service-list li a:hover {
  color: var(--theme-color2);
}
.sidebar-service-list li a:hover i {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #fff;
  background-color: var(--theme-color2);
}
.sidebar-service-list li a i {
  height: 32px;
  width: 45px;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  color: #191825;
  background-color: #fff;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 16px;
  border-radius: 15px;
}
.sidebar-service-list li.current a {
  color: var(--theme-color2);
}
.sidebar-service-list li.current a i {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #fff;
  background-color: var(--theme-color2);
}

.service-details-help {
  position: relative;
  display: block;
  padding: 57px 55px 50px;
  margin-top: 30px;
  text-align: center;
  z-index: 1;
  background-color: var(--theme-color2);
  border-radius: 15px;
  overflow: hidden;
}

.help-shape-1 {
  position: absolute;
  bottom: -215px;
  left: -95px;
  width: 220px;
  height: 500px;
  background-color: #303030;
  mix-blend-mode: soft-light;
  border-radius: 150px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  z-index: -1;
}

.help-shape-2 {
  position: absolute;
  top: -118px;
  right: -130px;
  width: 180px;
  height: 350px;
  background-color: #fff;
  mix-blend-mode: soft-light;
  border-radius: 186px;
  -webkit-transform: rotate(48deg);
          transform: rotate(48deg);
}

.help-icon {
  height: 73px;
  width: 73px;
  background-color: #fff;
  color: #191825;
  font-size: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  margin: 0 auto 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.help-icon:hover {
  background-color: #191825;
  color: #fff;
}

.help-title {
  font-size: 38px;
  color: #fff;
  line-height: 40px;
  margin-bottom: 21px;
  font-weight: 700;
}

.help-contact {
  position: relative;
  display: block;
  margin-top: 21px;
}
.help-contact p {
  font-size: 14px;
  color: #fff;
  margin: 0;
  line-height: 32px;
  font-weight: 600;
  opacity: 0.7;
}
.help-contact a {
  font-size: 30px;
  color: #fff;
  font-weight: 600;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.help-contact a:hover {
  color: var(--theme-color1);
}

/*--------------------------------------------------------------
# Project Details
--------------------------------------------------------------*/
.project-details__img {
  position: relative;
  display: block;
}
.project-details__img img {
  width: 100%;
  border-radius: 10px;
}

.project-details__content {
  position: relative;
  display: block;
}

.project-details__content-left {
  position: relative;
  display: block;
  margin-top: 31px;
}

.project-details__content-right {
  position: relative;
  display: block;
  margin-top: 40px;
}

.project-details__details-box {
  position: relative;
  background-color: var(--theme-color1);
  padding: 40px 50px 30px;
  z-index: 1;
  margin-top: -140px;
}

.project-details__details-list {
  position: relative;
  display: block;
}
.project-details__details-list li {
  position: relative;
  display: block;
}
.project-details__details-list li + li {
  margin-top: 24px;
}

.project-details__client {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #757575!important;
}

.project-details__name {
  font-size: 18px;
  line-height: 24px;
}

.project-details__social {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.project-details__social a {
  position: relative;
  height: 40px;
  width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  font-size: 15px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1;
}
.project-details__social a:hover {
  background-color: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
}
.project-details__social a + a {
  margin-left: 10px;
}

.project-details__pagination-box {
  position: relative;
  display: block;
  text-align: center;
  border-top: 1px solid #ece9e0;
  border-bottom: 1px solid #ece9e0;
  padding: 30px 0;
  margin-top: 30px;
}

.project-details__pagination {
  position: relative;
  display: block;
}
.project-details__pagination li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.project-details__pagination li a {
  font-size: 14px;
  color: #757873;
  font-weight: 400;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.project-details__pagination li a:hover {
  color: var(--theme-color1);
}
.project-details__pagination li a:hover i {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  border: 2px solid var(--theme-color2);
}
.project-details__pagination li .content {
  position: relative;
  display: block;
}
.project-details__pagination li.next {
  float: left;
  position: relative;
}
.project-details__pagination li.next i {
  position: relative;
  height: 52px;
  width: 52px;
  border: 2px solid var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--theme-color2);
  font-size: 16px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  margin-right: 20px;
  z-index: 1;
}
.project-details__pagination li.next .content {
  text-align: left;
}
.project-details__pagination li.previous {
  position: relative;
  float: right;
}
.project-details__pagination li.previous i {
  position: relative;
  height: 52px;
  width: 52px;
  border: 2px solid var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--theme-color2);
  font-size: 16px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  margin-left: 20px;
  z-index: 1;
}
.project-details__pagination li.previous .content {
  text-align: right;
}

/***

====================================================================
    Blog Details
====================================================================

***/
.blog-details {
  position: relative;
  display: block;
}

.blog-details__left {
  position: relative;
  display: block;
}

.blog-details__img {
  position: relative;
  display: block;
  border-radius: 10px;
}
.blog-details__img img {
  width: 100%;
  border-radius: 10px;
}

.blog-details__date {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--theme-color1);
  text-align: center;
  padding: 21px 24px 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.blog-details__date .day {
  font-size: 16px;
  color: #fff;
  font-weight: var(--body-font-weight-bold);
  line-height: 16px;
}
.blog-details__date .month {
  position: relative;
  display: block;
  font-size: 10px;
  font-weight: var(--body-font-weight-bold);
  color: #fff;
  line-height: 12px;
  text-transform: uppercase;
}

.blog-details__content {
  position: relative;
  display: block;
  margin-top: 22px;
}

.blog-details__meta {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.blog-details__meta li + li {
  margin-left: 18px;
}
.blog-details__meta li a {
  font-size: 15px;
  color: #777;
  font-weight: 500;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.blog-details__meta li a:hover {
  color: var(--theme-color1);
}
.blog-details__meta li a i {
  color: var(--theme-color1);
  margin-right: 6px;
}

.blog-details__title {
  font-size: 30px;
  line-height: 40px;
  margin-top: 12px;
  margin-bottom: 21px;
  font-weight: var(--body-font-weight-bold);
}

.blog-details__bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 30px 0 30px;
  margin-top: 49px;
  border-top: 1px solid #ece9e0;
}
@media only screen and (max-width: 767px) {
  .blog-details__bottom {
    gap: 30px;
  }
}
.blog-details__bottom p {
  margin: 0;
}

.blog-details__tags span {
  color: #0e2207;
  font-size: 20px;
  margin-right: 14px;
  font-weight: var(--body-font-weight-bold);
}
.blog-details__tags a {
  position: relative;
  font-size: 12px;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  display: inline-block;
  padding: 5px 30px 5px;
  font-weight: var(--body-font-weight-bold);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 35px;
}
.blog-details__tags a:hover {
  background-color: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
  text-decoration: none;
}
.blog-details__tags a + a {
  margin-left: 6px;
}

.blog-details__social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.blog-details__social-list a {
  position: relative;
  height: 43px;
  width: 43px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  font-size: 15px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1;
}
.blog-details__social-list a:hover {
  color: var(--text-color-bg-theme-color2);
}
.blog-details__social-list a:hover:after {
  opacity: 1;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}
.blog-details__social-list a:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-color: var(--theme-color2);
  -webkit-transition-delay: 0.1s;
  transition-delay: 0.1s;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
  -webkit-transition-property: all;
  transition-property: all;
  opacity: 1;
  -webkit-transform-origin: top;
  transform-origin: top;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  z-index: -1;
}
.blog-details__social-list a + a {
  margin-left: 10px;
}

.blog-details__pagenation-box {
  position: relative;
  display: block;
  overflow: hidden;
  margin-bottom: 53px;
}

.blog-details__pagenation {
  position: relative;
  display: block;
}
.blog-details__pagenation li {
  position: relative;
  float: left;
  font-size: 20px;
  color: #0e2207;
  font-weight: var(--body-font-weight-bold);
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  line-height: 30px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  max-width: 370px;
  width: 100%;
  padding-left: 60px;
  padding-right: 60px;
  padding-top: 52px;
  padding-bottom: 52px;
  border-radius: 10px;
}
.blog-details__pagenation li:hover {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.blog-details__pagenation li + li {
  margin-left: 30px;
}

/* Nav Links */
.nav-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 53px;
}
@media only screen and (max-width: 767px) {
  .nav-links {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
  }
}
.nav-links .prev {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  width: calc(50% - 15px);
  margin-right: 30px;
}
@media only screen and (max-width: 767px) {
  .nav-links .prev {
    width: 100%;
  }
}
.nav-links .prev .thumb {
  margin-right: 20px;
}
.nav-links .next {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  width: calc(50% - 15px);
}
@media only screen and (max-width: 767px) {
  .nav-links .next {
    width: 100%;
  }
}
.nav-links .next .thumb {
  margin-left: 20px;
}
.nav-links > div {
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.nav-links > div .thumb {
  display: inline-block;
  min-width: 60px;
  width: 60px;
  height: 60px;
  overflow: hidden;
}
.nav-links > div .thumb a {
  display: inline-block;
}
.nav-links > div > a {
  display: inline-block;
  word-wrap: break-word;
  white-space: -moz-pre-wrap;
  white-space: pre-wrap;
  font-size: 20px;
  line-height: 1.637;
  font-weight: var(--body-font-weight-bold);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  padding: 52px 50px;
  border-radius: 10px;
  width: 100%;
}
@media only screen and (max-width: 767px) {
  .nav-links > div > a {
    padding: 30px;
  }
}
.nav-links > div > a:hover {
  color: var(--text-color-bg-theme-color1);
  background-color: var(--theme-color1);
}

/*** 

====================================================================
Sidebar
====================================================================

***/
@media (max-width: 991px) {
  .sidebar {
    margin-top: 50px;
  }
}
.sidebar__single + .sidebar__single {
  margin-top: 30px;
}

.sidebar__title {
  margin: 0;
  font-size: 20px;
  margin-bottom: 5px;
  font-weight: var(--h4-font-weight);
}

.sidebar__search {
  position: relative;
  display: block;
}

.sidebar__search-form {
  position: relative;
}
.sidebar__search-form input[type=search] {
  display: block;
  border: none;
  outline: none;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  font-size: 16px;
  font-weight: 500;
  padding-left: 50px;
  height: 74px;
  width: 100%;
  padding-right: 80px;
  border-radius: 10px;
}
.sidebar__search-form input[type=search]::-webkit-input-placeholder {
  color: var(--text-color-bg-theme-color2);
  opacity: 1;
}
.sidebar__search-form input[type=search]::-webkit-input-placeholder, .sidebar__search-form input[type=search]:-ms-input-placeholder, .sidebar__search-form input[type=search]::-ms-input-placeholder, .sidebar__search-form input[type=search]::placeholder {
  color: var(--text-color-bg-theme-color2);
  opacity: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__search-form input[type=search] {
    padding-left: 30px;
  }
}
.sidebar__search-form button[type=submit] {
  background-color: transparent;
  color: var(--text-color-bg-theme-color2);
  font-size: 22px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 72px;
  outline: none;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
}
@media only screen and (max-width: 767px) {
  .sidebar__search-form button[type=submit] {
    width: 42px;
  }
}

.sidebar__post {
  position: relative;
  display: block;
  padding: 46px 30px 30px;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__post {
    padding: 30px;
  }
}
.sidebar__post .sidebar__title {
  margin-left: 20px;
}
@media only screen and (max-width: 767px) {
  .sidebar__post .sidebar__title {
    margin-left: 0;
  }
}
.sidebar__post .sidebar__post-list {
  margin: 0;
}
.sidebar__post .sidebar__post-list li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 16px 20px 17px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
@media only screen and (max-width: 767px) {
  .sidebar__post .sidebar__post-list li {
    padding: 16px 0;
  }
}
.sidebar__post .sidebar__post-list li:hover {
  background-color: #ffffff;
  border-radius: 10px;
}
.sidebar__post .sidebar__post-list li + li {
  margin-top: 11px;
}
.sidebar__post .sidebar__post-list .sidebar__post-image {
  margin-right: 20px;
  -webkit-box-flex: 70px;
      -ms-flex: 70px 0 0px;
          flex: 70px 0 0;
}
.sidebar__post .sidebar__post-list .sidebar__post-image > img {
  width: 80px;
  border-radius: 10px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content {
  position: relative;
  top: -3px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content h3 {
  font-size: 18px;
  margin: 0;
  line-height: 26px;
  letter-spacing: 0;
}
.sidebar__post .sidebar__post-list .sidebar__post-content h3 a {
  color: #0e2207;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content-meta {
  font-size: 14px;
  font-weight: 500;
  color: #757873 !important;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.sidebar__post .sidebar__post-list .sidebar__post-content-meta i {
  color: var(--theme-color2);
  font-size: 14px;
  padding-right: 3px;
}

.sidebar__category {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  border-radius: 10px;
  padding: 45px 30px 38px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__category {
    padding: 30px 15px 30px;
  }
}
.sidebar__category .sidebar__title {
  padding-left: 20px;
  margin-bottom: 9px;
}
.sidebar__category-list {
  margin: 0;
}
.sidebar__category-list li + li {
  margin-top: 4px;
}
.sidebar__category-list li a {
  color: #757873;
  font-size: 16px;
  position: relative;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  background: none;
  padding: 12px 20px;
  font-weight: 500;
  border-radius: 10px;
}
.sidebar__category-list li a:hover {
  background-color: rgb(255, 255, 255);
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  color: #0e2207;
  text-shadow: 1px 0 0 rgba(14, 34, 7, 0.5);
}
.sidebar__category-list li a:hover span {
  color: #ffcd1e;
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
}
.sidebar__category-list li a span {
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%) scale(0);
  transform: translateY(-50%) scale(0);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  color: var(--theme-color2);
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 16px;
}
.sidebar__category-list li.active a {
  background-color: rgb(255, 255, 255);
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  color: #0e2207;
  border-radius: 10px;
  text-shadow: 1px 0 0 rgba(14, 34, 7, 0.5);
}
.sidebar__category-list li.active a span {
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
  color: #ffcd1e;
}

.sidebar__tags {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  padding: 46px 45px 50px;
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__tags {
    padding: 30px;
  }
}
.sidebar__tags .sidebar__title {
  margin-left: 5px;
  margin-bottom: 25px;
}

.sidebar__tags-list {
  margin-top: -10px;
}
.sidebar__tags-list a {
  font-size: 14px;
  color: #0e2207;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background: #ffffff;
  display: inline-block;
  padding: 5px 28px 5px;
  margin-left: 5px;
  border-radius: 30px;
}
.sidebar__tags-list a:hover {
  color: var(--text-color-bg-theme-color1);
  background: var(--theme-color1);
}
.sidebar__tags-list a + a {
  margin-left: 5px;
  margin-top: 10px;
}

.sidebar__comments {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  padding: 46px 50px 43px;
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__comments {
    padding: 30px;
  }
}
.sidebar__comments .sidebar__title {
  margin-bottom: 25px;
}

.sidebar__comments-list {
  position: relative;
  display: block;
}
.sidebar__comments-list li {
  position: relative;
  display: block;
  padding-left: 65px;
}
.sidebar__comments-list li:hover .sidebar__comments-icon {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.sidebar__comments-list li + li {
  margin-top: 23px;
}

.sidebar__comments-icon {
  height: 45px;
  width: 45px;
  background-color: var(--theme-color1);
  border-radius: 50%;
  font-size: 15px;
  color: var(--text-color-bg-theme-color1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.sidebar__comments-text-box p {
  font-size: 15px;
  margin: 0;
  line-height: 26px;
  font-weight: 500;
}
.sidebar__comments-text-box p span {
  color: #0e2207;
}
.sidebar__comments-text-box h5 {
  font-size: 15px;
  margin: 0;
  line-height: 26px;
  color: #757873;
  font-weight: 500;
  letter-spacing: 0;
}

/*** 

====================================================================
    Comments
====================================================================

***/
.comment-one .comment-one__title {
  margin-bottom: 30px;
}
.comment-one .comment-one__single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #ece9e0;
  padding-bottom: 60px;
  margin-bottom: 60px;
  -webkit-box-align: top;
      -ms-flex-align: top;
          align-items: top;
}
@media only screen and (max-width: 767px) {
  .comment-one .comment-one__single {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.comment-one .comment-one__content {
  position: relative;
  margin-left: 45px;
}
@media only screen and (max-width: 767px) {
  .comment-one .comment-one__content {
    margin-top: 20px;
    margin-left: 0;
  }
}
.comment-one .comment-one__content h3 {
  margin: 0;
  font-size: 20px;
  color: var(--theme-black);
  margin-bottom: 24px;
}
.comment-one .comment-one__content p {
  font-size: 16px;
  font-weight: 500;
}
.comment-one .comment-one__btn {
  padding: 5px 30px;
  position: absolute;
  top: 0;
  right: 0;
  font-size: 14px;
}
.comment-one .comment-one__image {
  position: relative;
  display: block;
  border-radius: 50%;
  -webkit-box-flex: 100px;
      -ms-flex: 100px 0 0px;
          flex: 100px 0 0;
}
.comment-one .comment-one__image img {
  border-radius: 50%;
}

.comment-form .comment-form__title {
  margin-top: -7px;
}

/*--------------------------------------------------------------
# Team Details
--------------------------------------------------------------*/
.team-details {
  position: relative;
  display: block;
}

.team-details__top {
  padding: 0 0 120px;
}

.team-details-shape-1 {
  position: absolute;
  bottom: -270px;
  right: 0;
  opacity: 0.5;
  z-index: 2;
}
.team-details-shape-1 img {
  width: auto;
}

.team-details__top-left {
  position: relative;
  display: block;
  margin-right: 20px;
}

.team-details__top-img {
  position: relative;
  display: block;
  border-radius: 30px;
}
.team-details__top-img img {
  width: 100%;
  border-radius: 30px;
}

.team-details__big-text {
  font-size: 80px;
  line-height: 80px;
  text-transform: uppercase;
  color: #eef0f6;
  letter-spacing: 0.35em;
  font-weight: 400;
  position: absolute;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  top: 209px;
  left: -325px;
}

.team-details__top-right {
  position: relative;
  display: block;
  margin-left: 50px;
}
@media only screen and (max-width: 991px) {
  .team-details__top-right {
    margin-top: 70px;
    margin-left: 0;
  }
}

.team-details__top-content {
  position: relative;
  display: block;
  margin-top: -11px;
}

.team-details__top-name {
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  margin-bottom: 3px;
}

.team-details__top-title {
  font-size: 16px;
  color: var(--theme-color2);
}

.team-details__social {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 28px;
}
.team-details__social a {
  position: relative;
  height: 40px;
  width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  font-size: 15px;
  border-radius: 50%;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.team-details__social a:hover {
  background-color: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
}
.team-details__social a + a {
  margin-left: 10px;
}

.team-details__top-text-1 {
  font-size: 30px;
  color: var(--theme-color1);
  line-height: 45px;
  font-weight: 400;
  margin-bottom: 30px;
}

.team-details__top-text-2 {
  padding-top: 23px;
  padding-bottom: 35px;
}

.team-details__bottom {
  position: relative;
  display: block;
  border-top: 1px solid #e4e5ea;
  padding-top: 110px;
}

.team-details__bottom-left {
  position: relative;
  display: block;
  margin-right: 70px;
}

.team-details__bottom-left-title {
  font-size: 36px;
  font-weight: 700;
  line-height: 46px;
}

.team-details__bottom-left-text {
  padding-top: 30px;
}

.team-details__bottom-right {
  position: relative;
  display: block;
  margin-left: 70px;
  margin-top: 1px;
}
@media only screen and (max-width: 991px) {
  .team-details__bottom-right {
    margin-left: 0;
  }
}

.team-details__progress {
  position: relative;
  display: block;
  width: 100%;
}
.team-details__progress .bar {
  position: relative;
  width: 100%;
  height: 13px;
  background-color: #eef0f6;
  border-radius: 7px;
  margin-bottom: 22px;
}
.team-details__progress .bar-inner {
  position: relative;
  display: block;
  width: 0px;
  height: 13px;
  border-radius: 7px;
  background-color: var(--theme-color1);
  -webkit-transition: all 1500ms ease;
  transition: all 1500ms ease;
}
.team-details__progress .count-text {
  position: absolute;
  right: 0px;
  bottom: 21px;
  line-height: 24px;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  opacity: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.team-details__progress .bar-inner.counted .count-text {
  opacity: 1;
}
.team-details__progress .bar.marb-0 {
  margin-bottom: 0;
}

.team-details__progress-single {
  position: relative;
  display: block;
}

.team-details__progress-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 28px;
  margin-bottom: 6px;
}

.team-contact-form {
  background-color: #eef0f6;
}
.team-contact-form input[type=text],
.team-contact-form input[type=email] {
  background-color: #fff;
}
.team-contact-form textarea {
  background-color: #fff;
  height: 180px;
}

/***

====================================================================
        Contact
====================================================================

***/
.contact-details__info {
  position: relative;
  display: block;
  margin-top: 41px;
}
.contact-details__info li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.contact-details__info li .icon {
  height: 80px;
  width: 80px;
  background-color: var(--theme-color2);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li .icon span {
  color: var(--text-color-bg-theme-color2);
  font-size: 25px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li:hover .icon {
  background-color: var(--theme-color1);
}
.contact-details__info li:hover .icon span {
  color: var(--text-color-bg-theme-color1);
}
.contact-details__info li .text {
  margin-left: 30px;
}
.contact-details__info li .text p {
  font-size: 14px;
  line-height: 24px;
}
.contact-details__info li .text a {
  font-size: 18px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li .text span {
  font-size: 20px;
}
.contact-details__info li + li {
  margin-top: 30px;
}

/***

==================================================================
	Main Footer
==================================================================

***/
.main-footer {
  position: relative;
  background-color: var(--theme-color2);
  /* Widget Section */
}
.main-footer .footer-upper {
  position: relative;
  padding: 30px 0 7px;
  background-color: #0d2f42;
}
.main-footer .widgets-section {
  position: relative;
  padding: 70px 0 50px;
}
.main-footer .footer-column {
  position: relative;
  margin-bottom: 50px;
}
.main-footer .footer-widget {
  position: relative;
}
.main-footer .widget-title {
  position: relative;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2em;
  margin-bottom: 20px;
}

.contact-info-block {
  position: relative;
  margin-bottom: 30px;
}
.contact-info-block:last-child .inner:before {
  display: none;
}
.contact-info-block .inner {
  position: relative;
  padding-left: 40px;
}
.contact-info-block .inner:before {
  position: absolute;
  right: 20px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: #fff;
  opacity: 0.1;
  content: "";
}
@media (max-width: 575.98px) {
  .contact-info-block .inner {
    padding-left: 0;
    text-align: center;
  }
}
.contact-info-block .inner .icon {
  position: absolute;
  left: 0;
  top: 12px;
  font-size: 20px;
  color: var(--theme-color1);
}
@media (max-width: 575.98px) {
  .contact-info-block .inner .icon {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
  }
}
.contact-info-block .inner .sub-title {
  font-size: 12px;
  color: #7f7f7f;
  font-weight: 400;
  line-height: 20px;
  display: block;
}
.contact-info-block .inner .text {
  font-size: 18px;
  color: #fff;
  line-height: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
@media (max-width: 575.98px) {
  .contact-info-block .inner .text {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}
.contact-info-block .inner .text a {
  color: #fff;
}
.contact-info-block .inner .text a:hover {
  color: var(--theme-color1);
}

.main-footer .about-widget {
  position: relative;
}
.main-footer .about-widget .logo {
  margin-bottom: 15px;
}
.main-footer .about-widget .text {
  margin-bottom: 30px;
}

/*=== User LInks ===*/
.user-links {
  position: relative;
}
.user-links li {
  position: relative;
  font-size: 15px;
  line-height: 26px;
  color: #75767a;
  font-weight: 400;
  margin-bottom: 10px;
}
.user-links li:last-child {
  margin-bottom: 0;
}
.user-links li a {
  position: relative;
  display: inline-block;
  color: inherit;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.user-links li a:hover {
  color: #FFFFFF;
}
.user-links li a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background-color: var(--bg-theme-color1);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.user-links li a:hover:before {
  width: 100%;
}
.user-links.two-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.user-links.two-column li {
  width: 50%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
}

.newsletter-widget {
  position: relative;
}
.newsletter-widget .text {
  margin-bottom: 10px;
}

/* Subscribe Form */
.subscribe-form {
  position: relative;
}
.subscribe-form .form-group {
  position: relative;
}
.subscribe-form .form-group input[type=text],
.subscribe-form .form-group input[type=email] {
  position: relative;
  display: block;
  height: 60px;
  width: 100%;
  font-size: 14px;
  line-height: 30px;
  color: #717070;
  padding: 20px 30px;
  background: #fff;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.subscribe-form .form-group input[type=text]::-webkit-input-placeholder, .subscribe-form .form-group input[type=email]::-webkit-input-placeholder {
  color: #717070;
}
.subscribe-form .form-group input[type=text]::-moz-placeholder, .subscribe-form .form-group input[type=email]::-moz-placeholder {
  color: #717070;
}
.subscribe-form .form-group input[type=text]:-ms-input-placeholder, .subscribe-form .form-group input[type=email]:-ms-input-placeholder {
  color: #717070;
}
.subscribe-form .form-group input[type=text]::-ms-input-placeholder, .subscribe-form .form-group input[type=email]::-ms-input-placeholder {
  color: #717070;
}
.subscribe-form .form-group input[type=text]::placeholder,
.subscribe-form .form-group input[type=email]::placeholder {
  color: #717070;
}
.subscribe-form .form-group .theme-btn {
  position: relative;
  margin-top: 10px;
  width: 100%;
}

/*=== Footer Bottom ===*/
.footer-bottom {
  position: relative;
  z-index: 3;
  text-align: center;
}
.footer-bottom .copyright-text {
  position: relative;
  font-size: 15px;
  line-height: 24px;
  padding: 33px 15px;
  font-weight: 400;
  color: #75767a;
  margin-bottom: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.footer-bottom .copyright-text a {
  color: inherit;
}
.footer-bottom .copyright-text a:hover {
  color: #ffffff;
}




/* My Custom css */
.service-block-new-1 .image-box .image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}
.service-block .inner-box .image-box .image img {
    width: 100%;
    height: 350px;
    object-fit: cover;
}
.service-block-two .inner-box .image-box .image img {
    width: 100%;
    height: 310px;
    object-fit: cover;
}
.project-block-home3 .image-box .image img {
    height: 480px;
}
.project-block-home4 .image-box .image img {
    height: 480px;
}
.project-block .image-box .image img {
    height: 380px;
}

.team-block-two .image-box .image img {
    width: 100%;
    height: 360px;
    object-fit: cover;
}

@media (max-width: 991px) {
    .service-block-new-1 .image-box .image img {height: 350px;}
    .service-block .inner-box .image-box .image img {height: 440px;}
    .service-block-two .inner-box .image-box .image img {height: 310px;}
    .project-block-home3 .image-box .image img {height: 480px;}
    .project-block-home4 .image-box .image img {height: 480px;}
    .project-block .image-box .image img {height: 450px;}
    .team-block-two .image-box .image img {height: 430px;}
}
@media (max-width: 767px) {
    .service-block-new-1 .image-box .image img {height: auto;}
    .service-block .inner-box .image-box .image img {height: auto;}
    .service-block-two .inner-box .image-box .image img {height: auto;}
    .project-block-home3 .image-box .image img {height: 500px;}
    .project-block-home4 .image-box .image img {height: 500px;}
    .project-block .image-box .image img {height: auto;}
    .team-block-two .image-box .image img {height: auto;}
}

.sidebar__category-list li {
    margin-bottom: 10px;
}
.sidebar__category-list li a {
    background: #fff;
    margin-left: 20px;
    margin-right: 20px;
}
.sidebar__category-list li a:hover {
    background: var(--theme-color1);
}

ul.pagination {
    justify-content: center;
}

ul.pagination .page-link {
    color: var(--theme-color1);
}

ul.pagination .page-item {
    margin-left: 5px;
    margin-right: 5px;
}

ul.pagination .page-item.active .page-link {
    background: var(--theme-color1);
    border-color: var(--theme-color1);
}

.video-section .video-box {
    padding-top: 240px!important;
    padding-bottom: 240px!important;
}

.contact-info-block .inner {
    padding-left: 110px!important;
}
.contact-info-block .inner .icon {
    top: 0!important;
}
.contact-info-block .inner:hover .icon {
    background-color: var(--bg-theme-color1)!important;
    border-radius: 50%;
    color: var(--bg-theme-color2)!important;
}
.map-section {
    margin-bottom: -20px;
}
.map-section iframe {
    width: 100%;
    height: 540px;
}
.page-title::before {
    background: #3e3e3e;
}

.sec-title .sub-title {
    text-transform: none!important;
}


.header-style-two .main-box .main-menu .navigation > li {
    margin-right: 40px!important;
}
.header-style-two .main-box .main-menu .navigation li.lang {
    margin-right: 0!important;
}
.header-style-two .main-box .main-menu .navigation > li.lang::before {
    display: none;
}
.header-style-two .main-box .main-menu .navigation > li.lang select {
    background: none;
    border: none;
    padding: 4px 10px;
    height: auto;
    font-size: 18px;
    color: #fff;
    margin-top: -3px;
    font-weight: 400!important;
}
.header-style-two .main-box .main-menu .navigation > li.lang select:focus {
    box-shadow: none;
}
.main-menu .navigation li.lang {
    display: flex;
    align-items: center;
}
.main-menu .navigation li.lang img {
    width: 25px;
    height: 25px;
}
.main-menu .navigation li.lang img.globe {
    display: block;
}
.main-menu .navigation li.lang img.globe-black {
    display: none;
}


.sticky-header .main-menu .navigation > li.lang select {
    background: none;
    border: none;
    padding: 4px 10px;
    height: auto;
    font-size: 18px;
    color: #000;
    margin-top: -3px;
    font-weight: 400!important;
}
.sticky-header .main-menu .navigation > li.lang::before {
    display: none;
}
.sticky-header .main-menu .navigation li.lang {
    display: flex;
    align-items: center;
}
.sticky-header .main-menu .navigation li.lang img {
    width: 25px;
    height: 25px;
}
.sticky-header .main-menu .navigation > li.lang select:focus {
    box-shadow: none;
}
.sticky-header .main-menu .navigation li.lang img.globe {
    display: none;
}
.sticky-header .main-menu .navigation li.lang img.globe-black {
    display: block;
}

@media (max-width: 991px) {
    .mobile-menu .navigation li.lang img {
        width: 25px;
        height: 25px;
    }
    .mobile-menu .navigation li.lang {
        margin-right: 0!important;
        padding: 5px 20px;
    }
    .mobile-menu .navigation li.lang::before {
        display: none;
    }
    .mobile-menu .navigation li.lang select {
        background: none;
        border: none;
        padding: 4px 10px;
        height: auto;
        font-size: 18px;
        color: #fff;
        margin-top: -3px;
        font-weight: 400!important;
    }
    .mobile-menu .navigation li.lang select:focus {
        box-shadow: none;
    }
    .mobile-menu .navigation li.lang {
        display: flex;
        align-items: center;
    }
    .mobile-menu .navigation li.lang img {
        width: 25px;
        height: 25px;
    }
    .mobile-menu .navigation li.lang img.globe {
        display: block;
    }
    .mobile-menu .navigation li.lang img.globe-black {
        display: none;
    }
}
    