<p align="center"><img src="/art/logo.svg" alt="Logo Laravel Sanctum"></p>

<p align="center">
<a href="https://github.com/laravel/sanctum/actions"><img src="https://github.com/laravel/sanctum/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/sanctum"><img src="https://img.shields.io/packagist/dt/laravel/sanctum" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/sanctum"><img src="https://img.shields.io/packagist/v/laravel/sanctum" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/sanctum"><img src="https://img.shields.io/packagist/l/laravel/sanctum" alt="License"></a>
</p>

## Introduction

Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.

## Official Documentation

Documentation for Sanctum can be found on the [Laravel website](https://laravel.com/docs/sanctum).

## Contributing

Thank you for considering contributing to Sanctum! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/sanctum/security/policy) on how to report security vulnerabilities.

## License

Laravel Sanctum is open-sourced software licensed under the [MIT license](LICENSE.md).
