<?php

namespace Php<PERSON>ars<PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use PhpParser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 396;
    protected $actionTableSize = 1241;
    protected $gotoTableSize = 629;

    protected $invalidSymbol = 168;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE = 434;
    protected $numNonLeafStates = 736;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          163,  164,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  161,   35,  162,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected $action = array(
          133,  134,  135,  579,  136,  137,    0,  748,  749,  750,
          138,   38,  327,-32766,-32766,-32766,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,  102,  103,  104,  105,  106, 1109, 1110,
         1111, 1108, 1107, 1106, 1112,  742,  741,-32766, 1232,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767,    2,  107,  108,  109,  751,  274,  381,  380,-32766,
        -32766,-32766,-32766,  104,  105,  106, 1024,  422,  110,  265,
          139,  403,  755,  756,  757,  758,  466,  467,  428,  938,
          291,-32766,  287,-32766,-32766,  759,  760,  761,  762,  763,
          764,  765,  766,  767,  768,  769,  789,  580,  790,  791,
          792,  793,  781,  782,  344,  345,  784,  785,  770,  771,
          772,  774,  775,  776,  355,  816,  817,  818,  819,  820,
          581,  777,  778,  582,  583,  810,  801,  799,  800,  813,
          796,  797,  687, -545,  584,  585,  795,  586,  587,  588,
          589,  590,  591, -328, -593, -367, 1234, -367,  798,  592,
          593, -593,  140,-32766,-32766,-32766,  133,  134,  135,  579,
          136,  137, 1057,  748,  749,  750,  138,   38,  688, 1020,
         1019, 1018, 1021,  390,-32766,    7,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,  379,  380, 1033,  689,
          690,  742,  741,-32766,-32766,-32766,  422, -545, -545, -590,
        -32766,-32766,-32766, 1032,-32766,  127, -590, 1236, 1235, 1237,
         1318,  751, -545,  290,-32766,  283,-32766,-32766,-32766,-32766,
        -32766, 1236, 1235, 1237, -545,  265,  139,  403,  755,  756,
          757,  758,   16,  481,  428,  458,  459,  460,  298,  722,
           35,  759,  760,  761,  762,  763,  764,  765,  766,  767,
          768,  769,  789,  580,  790,  791,  792,  793,  781,  782,
          344,  345,  784,  785,  770,  771,  772,  774,  775,  776,
          355,  816,  817,  818,  819,  820,  581,  777,  778,  582,
          583,  810,  801,  799,  800,  813,  796,  797,  129,  824,
          584,  585,  795,  586,  587,  588,  589,  590,  591, -328,
           83,   84,   85, -593,  798,  592,  593, -593,  149,  773,
          743,  744,  745,  746,  747,  824,  748,  749,  750,  786,
          787,   37,  145,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  291,  274,  835,
          254, 1109, 1110, 1111, 1108, 1107, 1106, 1112, -590,  860,
          110,  861, -590,  482,  751,-32766,-32766,-32766,-32766,-32766,
          142,  603, 1085,  742,  741, 1262,  326,  987,  752,  753,
          754,  755,  756,  757,  758,  309,-32766,  822,-32766,-32766,
        -32766,-32766,  242,  553,  759,  760,  761,  762,  763,  764,
          765,  766,  767,  768,  769,  789,  812,  790,  791,  792,
          793,  781,  782,  783,  811,  784,  785,  770,  771,  772,
          774,  775,  776,  815,  816,  817,  818,  819,  820,  821,
          777,  778,  779,  780,  810,  801,  799,  800,  813,  796,
          797,  311,  940,  788,  794,  795,  802,  803,  805,  804,
          806,  807,  323,  609, 1274, 1033,  833,  798,  809,  808,
           50,   51,   52,  512,   53,   54,  860,  241,  861,  918,
           55,   56, -111,   57,-32766,-32766,-32766, -111,  826, -111,
          290, 1302, 1347,  356,  305, 1348,  339, -111, -111, -111,
         -111, -111, -111, -111, -111,-32766, -194,-32766,-32766,-32766,
         -193,  956,  957,  829,  -86,  988,  958,  834,   58,   59,
          340,  428,  952, -544,   60,  832,   61,  247,  248,   62,
           63,   64,   65,   66,   67,   68,   69, 1241,   28,  267,
           70,  444,  513, -342,-32766,  141, 1268, 1269,  514,  918,
          833,  326, -272,  918, 1266,   42,   25,  515,  940,  516,
           14,  517,  908,  518,  828,  369,  519,  520,  373,  709,
         1033,   44,   45,  445,  376,  375,  388,   46,  521,  712,
          -86,  440, 1101,  367,  338, -543,  441, -544, -544,  830,
         1227,  442,  523,  524,  525,  290, 1236, 1235, 1237,  361,
         1030,  443, -544, 1087,  526,  527,  839, 1255, 1256, 1257,
         1258, 1252, 1253,  297, -544,  151, -550, -584,  833, 1259,
         1254, -584, 1033, 1236, 1235, 1237,  298, -154, -154, -154,
          152,   71,  908,  321,  322,  326,  908,  920, 1030,  707,
          833,  154, -154, 1337, -154,  155, -154,  283, -154, -543,
         -543,   82, 1232, 1086, 1322,  734,  156,  326,  374,  158,
         1033, 1321, -194,  -79, -543,  -88, -193,  742,  741,  956,
          957,  653,   26,-32766,  522,  -51, -543,   33, -549,  894,
          952, -111, -111, -111,   32,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  -59,   75,
           28,  672,  673,  326,  -58,   36,  250,  920,  124,  707,
          125,  920,  833,  707, -154,  130, 1266,  131,-32766, -547,
          144, -542,  150,  406, 1234,  377,  378, 1146, 1148,  382,
          383,-32766,-32766,-32766,  -85,-32766, 1056,-32766, -542,-32766,
          644,  645,-32766,  159,  160,  161, 1232,-32766,-32766,-32766,
          162,  -79, 1227,-32766,-32766,  742,  741,  163, -302,-32766,
          419,  -75,   -4,  918,  -73,  287,  526,  527,-32766, 1255,
         1256, 1257, 1258, 1252, 1253,  -72,  -71,  -70,  -69,  -68,
          -67, 1259, 1254, -547, -547, -542, -542,  742,  741,  -66,
          -47,  -18,-32766,   73,  148,  918,  322,  326, 1234,  273,
         -542,  284, -542, -542,  723,-32766,-32766,-32766,  726,-32766,
         -547,-32766, -542,-32766,  917,  147,-32766, -542,  288,  289,
         -298,-32766,-32766,-32766,-32766,  713,  279,-32766,-32766, -542,
         1234,  280,  285,-32766,  419,   48,  286,-32766,-32766,-32766,
          332,-32766,-32766,-32766,  292,-32766,  908,  293,-32766,  934,
          274, 1030,  918,-32766,-32766,-32766,  110,  682,  132,-32766,
        -32766,  833,  146,-32766,  559,-32766,  419,  659,  374,  824,
          435, 1349,   74, 1033,-32766,  296,  654, 1116,  908,  956,
          957,  306,  714,  698,  522,  555,  303,   13,  310,  852,
          952, -111, -111, -111,  700,  463,  492,  953,  283,  299,
          300,-32766,   49,  675,  918,  304,  660, 1234,  676,  936,
         1273,-32766,   10, 1263,-32766,-32766,-32766,  642,-32766,  918,
        -32766,  920,-32766,  707,   -4,-32766,  126,   34,  918,  565,
        -32766,-32766,-32766,-32766,    0,  908,-32766,-32766,    0, 1234,
          918,    0,-32766,  419,    0,    0,-32766,-32766,-32766,  717,
        -32766,-32766,-32766,  920,-32766,  707, 1033,-32766,  724, 1275,
            0,  487,-32766,-32766,-32766,-32766,  301,  302,-32766,-32766,
         -507, 1234,  571, -497,-32766,  419,  607,    8,-32766,-32766,
        -32766,  372,-32766,-32766,-32766,   17,-32766,  908,  371,-32766,
          832,  298,  320,  128,-32766,-32766,-32766,   40,  370,   41,
        -32766,-32766,  908, -250, -250, -250,-32766,  419,  731,  374,
          973,  908,  707,  732,  899,-32766,  997,  974,  728,  981,
          956,  957,  971,  908,  982,  522,  897,  969, 1090, 1093,
          894,  952, -111, -111, -111,   28, 1094, 1091, 1092, -249,
         -249, -249, 1241, 1098,  708,  374,  844,  833, 1288, 1306,
         1340, 1266,  647, 1267,  711,  715,  956,  957,  716, 1241,
          718,  522,  920,  719,  707, -250,  894,  952, -111, -111,
         -111,  720,  -16,  721,  725,  710, -511,  920,  895,  707,
         -578, 1232, 1344, 1346,  855,  854,  920, 1227,  707, -577,
          863,  946,  989,  862, 1345,  945,  943,  944,  920,  947,
          707, -249,  527, 1218, 1255, 1256, 1257, 1258, 1252, 1253,
          927,  937,  925,  979,  980,  631, 1259, 1254, 1343, 1300,
        -32766, 1289, 1307,  833, 1316, -275, 1234, -576,   73, -550,
         -549,  322,  326,-32766,-32766,-32766, -548,-32766, -491,-32766,
          833,-32766,    1,   29,-32766,   30,   39,   43,   47,-32766,
        -32766,-32766,   72,   76,   77,-32766,-32766, 1232, -111, -111,
           78,-32766,  419, -111,   79,   80,   81,  143,  153, -111,
        -32766,  157,  246,  328, 1232, -111, -111,  356,-32766,  357,
         -111,  358,  359,  360,  361,  362, -111,  363,  364,  365,
          366,  368,  436,    0, -273,-32766, -272,   19,   20,  298,
           21,   22,   24,  405,   75, 1203,  483,  484,  326,  491,
            0,  494,  495,  496,  497,  501,  298,  502,  503,  510,
          693,   75,    0, 1245, 1186,  326, 1264, 1059, 1058, 1039,
         1222, 1035, -277, -103,   18,   23,   27,  295,  404,  600,
          604,  633,  699, 1190, 1240, 1187, 1319,    0,    0,    0,
          326
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,   70,    9,   10,   11,    9,   10,   11,   44,
           45,   46,   47,   48,   49,   50,   51,   52,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,  116,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,    8,   53,   54,   55,   57,   57,  106,  107,  137,
            9,   10,   11,   50,   51,   52,    1,  116,   69,   71,
           72,   73,   74,   75,   76,   77,  134,  135,   80,    1,
           30,   30,   30,   32,   33,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,   80,   70,  136,  137,  138,  139,  140,  141,
          142,  143,  144,    8,    1,  106,   80,  108,  150,  151,
          152,    8,  154,    9,   10,   11,    2,    3,    4,    5,
            6,    7,  164,    9,   10,   11,   12,   13,  116,  119,
          120,  121,  122,  106,   30,  108,   32,   33,   34,   35,
           36,   37,   38,    9,   10,   11,  106,  107,  138,  137,
          138,   37,   38,    9,   10,   11,  116,  134,  135,    1,
            9,   10,   11,  137,   30,   14,    8,  155,  156,  157,
            1,   57,  149,  163,   30,  163,   32,   33,   34,   35,
           36,  155,  156,  157,  161,   71,   72,   73,   74,   75,
           76,   77,    8,   31,   80,  129,  130,  131,  158,  161,
            8,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  109,  110,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  124,  125,
          126,  127,  128,  129,  130,  131,  132,  133,    8,   80,
          136,  137,  138,  139,  140,  141,  142,  143,  144,  164,
            9,   10,   11,  160,  150,  151,  152,  164,  154,    2,
            3,    4,    5,    6,    7,   80,    9,   10,   11,   12,
           13,   30,    8,   32,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
           49,   50,   51,   52,   53,   54,   55,   30,   57,    1,
            8,  116,  117,  118,  119,  120,  121,  122,  160,  106,
           69,  108,  164,  161,   57,    9,   10,   11,    9,   10,
          161,    1,    1,   37,   38,    1,  167,   31,   71,   72,
           73,   74,   75,   76,   77,    8,   30,   80,   32,   33,
           34,   35,   14,   85,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,    8,  122,  136,  137,  138,  139,  140,  141,  142,
          143,  144,    8,   51,  146,  138,   82,  150,  151,  152,
            2,    3,    4,    5,    6,    7,  106,   97,  108,    1,
           12,   13,  101,   15,    9,   10,   11,  106,   80,  108,
          163,    1,   80,  163,  113,   83,    8,  116,  117,  118,
          119,  120,  121,  122,  123,   30,    8,   32,   33,   34,
            8,  117,  118,   80,   31,  159,  122,  159,   50,   51,
            8,   80,  128,   70,   56,  155,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,    1,   70,   71,
           72,   73,   74,  162,    9,  161,   78,   79,   80,    1,
           82,  167,  164,    1,   86,   87,   88,   89,  122,   91,
          101,   93,   84,   95,  156,    8,   98,   99,    8,  161,
          138,  103,  104,  105,  106,  107,    8,  109,  110,   31,
           97,    8,  123,  115,  116,   70,    8,  134,  135,  156,
          122,    8,  124,  125,  126,  163,  155,  156,  157,  163,
          116,    8,  149,  162,  136,  137,    8,  139,  140,  141,
          142,  143,  144,  145,  161,   14,  163,  160,   82,  151,
          152,  164,  138,  155,  156,  157,  158,   75,   76,   77,
           14,  163,   84,  165,  166,  167,   84,  159,  116,  161,
           82,   14,   90,   85,   92,   14,   94,  163,   96,  134,
          135,  161,  116,  159,    1,  161,   14,  167,  106,   14,
          138,    8,  164,   16,  149,   31,  164,   37,   38,  117,
          118,   75,   76,  137,  122,   31,  161,   14,  163,  127,
          128,  129,  130,  131,   16,   17,   18,   19,   20,   21,
           22,   23,   24,   25,   26,   27,   28,   29,   16,  163,
           70,   75,   76,  167,   16,  147,  148,  159,   16,  161,
           16,  159,   82,  161,  162,   16,   86,   16,   74,   70,
           16,   70,  101,  102,   80,  106,  107,   59,   60,  106,
          107,   87,   88,   89,   31,   91,    1,   93,   70,   95,
          111,  112,   98,   16,   16,   16,  116,  103,  104,  105,
           16,   31,  122,  109,  110,   37,   38,   16,   35,  115,
          116,   31,    0,    1,   31,   30,  136,  137,  124,  139,
          140,  141,  142,  143,  144,   31,   31,   31,   31,   31,
           31,  151,  152,  134,  135,  134,  135,   37,   38,   31,
           31,   31,   74,  163,   31,    1,  166,  167,   80,   31,
          149,   31,  134,  135,   31,   87,   88,   89,   31,   91,
          161,   93,  161,   95,   31,   31,   98,  149,   37,   37,
           35,  103,  104,  105,   74,   31,   35,  109,  110,  161,
           80,   35,   35,  115,  116,   70,   35,   87,   88,   89,
           35,   91,  124,   93,   37,   95,   84,   37,   98,   38,
           57,  116,    1,  103,  104,  105,   69,   77,   31,  109,
          110,   82,   70,   85,   89,  115,  116,   96,  106,   80,
          108,   83,  154,  138,  124,  113,   90,   82,   84,  117,
          118,  114,   31,   80,  122,   85,  132,   97,  132,  127,
          128,  129,  130,  131,   92,   97,   97,  128,  163,  134,
          135,   74,   70,   94,    1,  133,  100,   80,  100,  154,
          146,  137,  150,  160,   87,   88,   89,  113,   91,    1,
           93,  159,   95,  161,  162,   98,  161,  161,    1,  153,
          103,  104,  105,   74,   -1,   84,  109,  110,   -1,   80,
            1,   -1,  115,  116,   -1,   -1,   87,   88,   89,   31,
           91,  124,   93,  159,   95,  161,  138,   98,   31,  146,
           -1,  102,  103,  104,  105,   74,  134,  135,  109,  110,
          149,   80,   81,  149,  115,  116,  153,  149,   87,   88,
           89,  149,   91,  124,   93,  149,   95,   84,  149,   98,
          155,  158,  161,  161,  103,  104,  105,  159,  161,  159,
          109,  110,   84,  100,  101,  102,  115,  116,  159,  106,
          159,   84,  161,  159,  159,  124,  159,  159,  162,  159,
          117,  118,  159,   84,  159,  122,  159,  159,  159,  159,
          127,  128,  129,  130,  131,   70,  159,  159,  159,  100,
          101,  102,    1,  159,  161,  106,  160,   82,  160,  160,
          160,   86,  160,  166,  161,  161,  117,  118,  161,    1,
          161,  122,  159,  161,  161,  162,  127,  128,  129,  130,
          131,  161,   31,  161,  161,  161,  165,  159,  162,  161,
          163,  116,  162,  162,  162,  162,  159,  122,  161,  163,
          162,  162,  162,  162,  162,  162,  162,  162,  159,  162,
          161,  162,  137,  162,  139,  140,  141,  142,  143,  144,
          162,  162,  162,  162,  162,  162,  151,  152,  162,  162,
           74,  162,  162,   82,  162,  164,   80,  163,  163,  163,
          163,  166,  167,   87,   88,   89,  163,   91,  163,   93,
           82,   95,  163,  163,   98,  163,  163,  163,  163,  103,
          104,  105,  163,  163,  163,  109,  110,  116,  117,  118,
          163,  115,  116,  122,  163,  163,  163,  163,  163,  128,
          124,  163,  163,  163,  116,  117,  118,  163,  137,  163,
          122,  163,  163,  163,  163,  163,  128,  163,  163,  163,
          163,  163,  163,   -1,  164,  137,  164,  164,  164,  158,
          164,  164,  164,  164,  163,  165,  164,  164,  167,  164,
           -1,  164,  164,  164,  164,  164,  158,  164,  164,  164,
          164,  163,   -1,  164,  164,  167,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,   -1,   -1,   -1,
          167
    );

    protected $actionBase = array(
            0,   -2,  154,  542,  752,  893,  929,   52,  374,  431,
          398,  869,  793,  235,  307,  307,  793,  307,  784,  908,
          908,  917,  908,  538,  841,  468,  468,  468,  708,  708,
          708,  708,  740,  740,  849,  849,  881,  817,  634, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036,  348,  346,  370,  653, 1063, 1069,
         1065, 1070, 1061, 1060, 1064, 1066, 1071,  946,  947,  774,
          949,  950,  943,  952, 1067,  882, 1062, 1068,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  525,  191,  359,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,  174,  174,
          174,  620,  620,   51,  465,  356,  955,  955,  955,  955,
          955,  955,  955,  955,  955,  955,  658,  184,  144,  144,
            7,    7,    7,    7,    7, 1031,  371, 1048,  -25,  -25,
          -25,  -25,   50,  725,  526,  449,   39,  317,   80,  474,
          474,   13,   13,  512,  512,  422,  422,  512,  512,  512,
          808,  808,  808,  808,  443,  505,  360,  308,  -78,  209,
          209,  209,  209,  -78,  -78,  -78,  -78,  803,  877,  -78,
          -78,  -78,   63,  641,  641,  822,   -1,   -1,   -1,  641,
          253,  790,  548,  253,  384,  548,  480,  402,  764,  759,
          -49,  447,  764,  639,  755,  198,  143,  825,  609,  825,
         1059,  320,  768,  426,  749,  720,  874,  904, 1072,  796,
          941,  798,  942,  106,  -58,  710, 1058, 1058, 1058, 1058,
         1058, 1058, 1058, 1058, 1058, 1058, 1058, 1073,  336, 1059,
          423, 1073, 1073, 1073,  336,  336,  336,  336,  336,  336,
          336,  336,  336,  336,  619,  423,  586,  616,  423,  795,
          336,  348,  814,  348,  348,  348,  348,  348,  348,  348,
          348,  348,  348,  750,  202,  348,  346,   78,   78,  484,
           65,   78,   78,   78,   78,  348,  348,  348,  348,  609,
          783,  766,  613,  813,  492,  783,  783,  783,  473,  135,
          378,  488,  713,  775,   67,  779,  779,  785,  969,  969,
          779,  769,  779,  785,  975,  779,  779,  969,  969,  823,
          280,  563,  478,  550,  568,  969,  377,  779,  779,  779,
          779,  746,  573,  779,  342,  314,  779,  779,  746,  744,
          760,   43,  762,  969,  969,  969,  746,  547,  762,  762,
          762,  839,  844,  794,  758,  444,  433,  588,  232,  801,
          758,  758,  779,  558,  794,  758,  794,  758,  745,  758,
          758,  758,  794,  758,  769,  502,  758,  717,  583,  224,
          758,    6,  979,  980,  624,  981,  973,  987, 1019,  991,
          992,  873,  965,  999,  974,  993,  972,  970,  773,  682,
          684,  818,  811,  963,  777,  777,  777,  956,  777,  777,
          777,  777,  777,  777,  777,  777,  682,  743,  829,  765,
         1006,  689,  691,  754,  906,  901, 1030, 1004, 1049,  994,
          828,  694, 1028, 1008,  846,  821, 1009, 1010, 1029, 1050,
         1052,  910,  782,  911,  912,  876, 1012,  883,  777,  979,
          992,  693,  974,  993,  972,  970,  748,  739,  737,  738,
          736,  735,  723,  734,  753, 1053,  954,  907,  878, 1011,
          957,  682,  879, 1023,  756, 1032, 1033,  827,  788,  778,
          880,  913, 1014, 1015, 1016,  884, 1054,  887,  830, 1024,
          951, 1035,  789,  918, 1037, 1038, 1039, 1040,  889,  919,
          892,  916,  900,  845,  776, 1020,  761,  920,  591,  787,
          791,  800, 1018,  606, 1000,  902,  921,  922, 1041, 1043,
         1044,  923,  924,  995,  847, 1026,  799, 1027, 1022,  848,
          850,  617,  797, 1055,  781,  786,  772,  621,  632,  925,
          927,  931,  998,  763,  770,  853,  855, 1056,  771, 1057,
          938,  635,  857,  718,  939, 1046,  719,  724,  637,  678,
          672,  731,  792,  903,  826,  757,  780, 1017,  724,  767,
          858,  940,  859,  860,  867, 1045,  868,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  458,  458,  458,
          458,  458,  458,  307,  307,  307,  307,  307,  307,  307,
            0,    0,  307,    0,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,   66,   66,  291,
          291,  291,   66,   66,   66,   66,   66,   66,   66,   66,
           66,   66,    0,  291,  291,  291,  291,  291,  291,  291,
          291,   66,  823,   66,   -1,   -1,   -1,   -1,   66,   66,
           66,  -88,  -88,   66,  384,   66,   66,   -1,   -1,   66,
           66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
            0,    0,  423,  548,   66,  769,  769,  769,  769,   66,
           66,   66,   66,  548,  548,   66,   66,   66,    0,    0,
            0,    0,    0,    0,    0,    0,  423,  548,    0,  423,
            0,    0,  769,  769,   66,  384,  823,  643,   66,    0,
            0,    0,    0,  423,  769,  423,  336,  779,  548,  779,
          336,  336,   78,  348,  643,  611,  611,  611,  611,    0,
            0,  609,  823,  823,  823,  823,  823,  823,  823,  823,
          823,  823,  823,  769,    0,  823,    0,  769,  769,  769,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  769,    0,    0,  969,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,  975,
            0,    0,    0,    0,    0,    0,  769,    0,    0,    0,
            0,    0,    0,    0,    0,    0,  777,  788,    0,  788,
            0,  777,  777,  777,    0,    0,    0,    0,  797,  771
    );

    protected $actionDefault = array(
            3,32767,  103,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  101,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  596,  596,
          596,  596,32767,32767,  254,  103,32767,32767,  469,  387,
          387,  387,32767,32767,  540,  540,  540,  540,  540,  540,
        32767,32767,32767,32767,32767,32767,  469,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  101,
        32767,32767,32767,   37,    7,    8,   10,   11,   50,   17,
          324,32767,32767,32767,32767,  103,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  589,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  473,  452,
          453,  455,  456,  386,  541,  595,  327,  592,  385,  146,
          339,  329,  242,  330,  258,  474,  259,  475,  478,  479,
          215,  287,  382,  150,  151,  416,  470,  418,  468,  472,
          417,  392,  397,  398,  399,  400,  401,  402,  403,  404,
          405,  406,  407,  408,  409,  390,  391,  471,  449,  448,
          447,32767,32767,  414,  415,  419,32767,32767,32767,32767,
        32767,32767,32767,32767,  103,32767,  389,  422,  420,  421,
          438,  439,  436,  437,  440,32767,32767,32767,  441,  442,
          443,  444,  316,32767,32767,  366,  364,  316,  112,32767,
        32767,  429,  430,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  534,  446,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  103,
        32767,  101,  536,  411,  413,  503,  424,  425,  423,  393,
        32767,  510,32767,  103,32767,  512,32767,32767,32767,32767,
        32767,32767,32767,  535,32767,  542,  542,32767,  496,  101,
          195,32767,32767,32767,  195,  195,32767,32767,32767,32767,
        32767,32767,32767,32767,  603,  496,  111,  111,  111,  111,
          111,  111,  111,  111,  111,  111,  111,32767,  195,  111,
        32767,32767,32767,  101,  195,  195,  195,  195,  195,  195,
          195,  195,  195,  195,  190,32767,  268,  270,  103,  557,
          195,32767,  515,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  508,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  496,
          434,  139,32767,  139,  542,  426,  427,  428,  498,  542,
          542,  542,  312,  289,32767,32767,32767,32767,  513,  513,
          101,  101,  101,  101,  508,32767,32767,32767,32767,  112,
          100,  100,  100,  100,  100,  104,  102,32767,32767,32767,
        32767,  223,  100,32767,  102,  102,32767,32767,  223,  225,
          212,  102,  227,32767,  561,  562,  223,  102,  227,  227,
          227,  247,  247,  485,  318,  102,  100,  102,  102,  197,
          318,  318,32767,  102,  485,  318,  485,  318,  199,  318,
          318,  318,  485,  318,32767,  102,  318,  214,  100,  100,
          318,32767,32767,32767,  498,32767,32767,32767,32767,32767,
        32767,32767,  222,32767,32767,32767,32767,32767,32767,32767,
          529,32767,  546,  559,  432,  433,  435,  544,  457,  458,
          459,  460,  461,  462,  463,  465,  591,32767,  502,32767,
        32767,32767,  338,  601,32767,  601,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  602,32767,  542,32767,32767,32767,32767,  431,    9,
           76,  491,   43,   44,   52,   58,  519,  520,  521,  522,
          516,  517,  523,  518,32767,32767,  524,  567,32767,32767,
          543,  594,32767,32767,32767,32767,32767,32767,  139,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          529,32767,  137,32767,32767,32767,32767,32767,32767,32767,
        32767,  525,32767,32767,32767,  542,32767,32767,32767,32767,
          314,  311,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  542,32767,
        32767,32767,32767,32767,  291,32767,  308,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  286,32767,32767,  381,  498,  294,  296,
          297,32767,32767,32767,32767,  360,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  153,  153,    3,
            3,  341,  153,  153,  153,  341,  341,  153,  341,  341,
          341,  153,  153,  153,  153,  153,  153,  280,  185,  262,
          265,  247,  247,  153,  352,  153
    );

    protected $goto = array(
          196,  196, 1031,  703,  694,  430,  658, 1062, 1334, 1334,
          424,  313,  314,  335,  573,  319,  429,  336,  431,  635,
          651,  652,  850,  669,  670,  671, 1334,  167,  167,  167,
          167,  221,  197,  193,  193,  177,  179,  216,  193,  193,
          193,  193,  193,  194,  194,  194,  194,  194,  194,  188,
          189,  190,  191,  192,  218,  216,  219,  534,  535,  420,
          536,  538,  539,  540,  541,  542,  543,  544,  545, 1132,
          168,  169,  170,  195,  171,  172,  173,  166,  174,  175,
          176,  178,  215,  217,  220,  238,  243,  244,  245,  257,
          258,  259,  260,  261,  262,  263,  264,  268,  269,  270,
          271,  281,  282,  316,  317,  318,  425,  426,  427,  578,
          222,  223,  224,  225,  226,  227,  228,  229,  230,  231,
          232,  233,  234,  235,  236,  180,  237,  181,  198,  199,
          200,  239,  188,  189,  190,  191,  192,  218, 1132,  201,
          182,  183,  184,  202,  198,  185,  240,  203,  201,  165,
          204,  205,  186,  206,  207,  208,  187,  209,  210,  211,
          212,  213,  214,  853,  851,  278,  278,  278,  278,  418,
          620,  620,  350,  570,  597, 1265, 1265, 1265, 1265, 1265,
         1265, 1265, 1265, 1265, 1265, 1283, 1283,  831,  618,  655,
         1283, 1283, 1283, 1283, 1283, 1283, 1283, 1283, 1283, 1283,
          353,  353,  353,  353,  866,  557,  550,  858,  825,  907,
          902,  903,  916,  859,  904,  856,  905,  906,  857,  878,
          457,  910,  865,  884,  546,  546,  546,  546,  831,  601,
          831, 1084, 1079, 1080, 1081,  341,  550,  557,  566,  567,
          343,  576,  599,  613,  614,  407,  408,  972,  465,  465,
          667,   15,  668, 1323,  411,  412,  413,  465,  681,  348,
         1233,  414, 1233,  478,  569,  346,  439, 1031, 1031, 1233,
          993,  480, 1031,  393, 1031, 1031, 1104, 1105, 1031, 1031,
         1031, 1031, 1031, 1031, 1031, 1031, 1031, 1031, 1031, 1315,
         1315, 1315, 1315, 1233,  657, 1333, 1333, 1055, 1233, 1233,
         1233, 1233, 1037, 1036, 1233, 1233, 1233, 1034, 1034, 1181,
          354,  678,  949, 1333,  437, 1026, 1042, 1043,  337,  691,
          354,  354,  827,  923,  691, 1040, 1041,  924,  691,  663,
         1336,  939,  871,  939,  354,  354, 1281, 1281,  354,  679,
         1350, 1281, 1281, 1281, 1281, 1281, 1281, 1281, 1281, 1281,
         1281,  552,  537,  537,  911,  354,  912,  537,  537,  537,
          537,  537,  537,  537,  537,  537,  537,  548,  564,  548,
          574,  611,  730,  634,  636,  849,  548,  656,  475, 1308,
         1309,  680,  684, 1007,  692,  701, 1003,  252,  252,  996,
          970,  970,  968,  970,  729,  843,  549, 1005, 1000,  423,
          455,  608, 1294,  846,  955,  966,  966,  966,  966,  325,
          308,  455,  960,  967,  249,  249,  249,  249,  251,  253,
          402,  351,  352,  683,  868,  551,  561,  449,  449,  449,
          551, 1305,  561, 1305,  612,  396,  461, 1010, 1010, 1224,
         1305,  395,  398,  558,  598,  602, 1015,  468,  577,  469,
          470, 1310, 1311,  876,  552,  846, 1341, 1342,  964,  409,
          702,  733,  324,  275,  324, 1317, 1317, 1317, 1317,  606,
          621,  624,  625,  626,  627,  648,  649,  650,  705, 1068,
          596, 1097,  874,  706,  476, 1228,  507,  697,  880, 1095,
         1115,  432, 1301,  628,  630,  632,  432,  879,  867, 1067,
         1071,    5, 1072,    6, 1038, 1038,  977,    0,  975,  662,
         1049, 1045, 1046,    0,    0,    0,    0, 1226,  449,  449,
          449,  449,  449,  449,  449,  449,  449,  449,  449,  928,
         1120,  449,  965, 1070,    0,    0,  616, 1303, 1303, 1070,
         1229, 1230, 1012,  499,    0,  500,    0,    0,  841,    0,
          870,  506,  661,  991, 1113,  883, 1212,  941,  864,    0,
         1213, 1216,  942, 1217,    0,    0, 1231, 1291, 1292,    0,
         1223,    0,    0,    0,  846,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  255,  255
    );

    protected $gotoCheck = array(
           42,   42,   72,    9,   72,   65,   65,  126,  181,  181,
           65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
           85,   85,   26,   85,   85,   85,  181,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   15,   27,   23,   23,   23,   23,   43,
          107,  107,   96,  170,  129,  107,  107,  107,  107,  107,
          107,  107,  107,  107,  107,  168,  168,   12,   55,   55,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
           24,   24,   24,   24,   35,   75,   75,   15,    6,   15,
           15,   15,   15,   15,   15,   15,   15,   15,   15,   35,
           82,   15,   35,   45,  106,  106,  106,  106,   12,  106,
           12,   15,   15,   15,   15,   75,   75,   75,   75,   75,
           75,   75,   75,   75,   75,   81,   81,   49,  148,  148,
           81,   75,   81,  179,   81,   81,   81,  148,   81,  177,
           72,   81,   72,   83,  103,   81,   82,   72,   72,   72,
          102,   83,   72,   61,   72,   72,  143,  143,   72,   72,
           72,   72,   72,   72,   72,   72,   72,   72,   72,    9,
            9,    9,    9,   72,   63,  180,  180,  113,   72,   72,
           72,   72,  117,  117,   72,   72,   72,   88,   88,  150,
           14,   88,   88,  180,  112,   88,   88,   88,   29,    7,
           14,   14,    7,   72,    7,  118,  118,   72,    7,  119,
          180,    9,   39,    9,   14,   14,  169,  169,   14,  115,
           14,  169,  169,  169,  169,  169,  169,  169,  169,  169,
          169,   14,  171,  171,   64,   14,   64,  171,  171,  171,
          171,  171,  171,  171,  171,  171,  171,   19,   48,   19,
            2,    2,   48,   48,   48,   25,   19,   48,  174,  174,
          174,   48,   48,   48,   48,   48,   48,    5,    5,   25,
           25,   25,   25,   25,   25,   18,   25,   25,   25,   13,
           19,   13,   14,   22,   91,   19,   19,   19,   19,  167,
          167,   19,   19,   19,    5,    5,    5,    5,    5,    5,
           28,   96,   96,   14,   37,    9,    9,   23,   23,   23,
            9,  129,    9,  129,   79,    9,    9,  106,  106,  159,
          129,   58,   58,   58,   58,   58,  109,    9,    9,    9,
            9,  176,  176,    9,   14,   22,    9,    9,   92,   92,
           92,   98,   24,   24,   24,  129,  129,  129,  129,   80,
           80,   80,   80,   80,   80,   80,   80,   80,   80,  128,
            8,    8,    9,    8,  156,   20,    8,    8,   41,    8,
          146,  116,  129,   84,   84,   84,  116,   16,   16,   16,
           16,   46,  131,   46,  116,  116,   95,   -1,   16,  116,
          116,  116,  116,   -1,   -1,   -1,   -1,   14,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   17,
           17,   23,   16,  129,   -1,   -1,   17,  129,  129,  129,
           20,   20,   17,  154,   -1,  154,   -1,   -1,   20,   -1,
           17,  154,   17,   17,   16,   16,   78,   78,   17,   -1,
           78,   78,   78,   78,   -1,   -1,   20,   20,   20,   -1,
           17,   -1,   -1,   -1,   22,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,    5,    5
    );

    protected $gotoBase = array(
            0,    0, -339,    0,    0,  386,  195,  312,  472,  -10,
            0,    0, -109,   62,   13, -184,   46,   65,   86,  102,
           93,    0,  125,  162,  197,  371,   18,  160,   83,   22,
            0,    0,    0,    0,    0, -166,    0,   85,    0,    9,
            0,   48,   -1,  157,    0,  207, -232,    0, -340,  223,
            0,    0,    0,    0,    0,  148,    0,    0,  396,    0,
            0,  231,    0,   52,  334, -236,    0,    0,    0,    0,
            0,    0,   -5,    0,    0, -139,    0,    0,  149,   91,
          112, -245,  -58, -205,   15, -695,    0,    0,   28,    0,
            0,   75,  154,    0,    0,   64, -310,    0,   55,    0,
            0,    0,  235,  221,    0,    0,  196,  -71,    0,   77,
            0,    0,   37,   24,    0,   56,  219,   23,   40,   39,
            0,    0,    0,    0,    0,    0,    5,    0,  106,  166,
            0,   61,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    1,    0,    0,   47,    0,  214,    0,
           35,    0,    0,    0,   49,    0,   45,    0,    0,   71,
            0,    0,    0,    0,    0,    0,    0,   88,  -56,   95,
          144,  111,    0,    0,   78,    0,   80,  229,    0,  222,
          -12, -299,    0,    0
    );

    protected $gotoDefault = array(
        -32768,  511,  737,    4,  738,  932,  814,  823,  594,  528,
          704,  347,  622,  421, 1299,  909, 1119,  575,  842, 1242,
         1250,  456,  845,  330,  727,  891,  892,  893,  399,  385,
          391,  397,  646,  623,  493,  877,  452,  869,  485,  872,
          451,  881,  164,  417,  509,  885,    3,  888,  554,  919,
          386,  896,  387,  674,  898,  560,  900,  901,  394,  400,
          401, 1124,  568,  619,  913,  256,  562,  914,  384,  915,
          922,  389,  392,  685,  464,  504,  498,  410, 1099,  563,
          605,  643,  446,  472,  617,  629,  615,  479,  433,  415,
          329,  954,  962,  486,  462,  976,  349,  984,  735, 1131,
          637,  488,  992,  638,  999, 1002,  529,  530,  477, 1014,
          272, 1017,  489,   12,  664, 1028, 1029,  665,  639, 1051,
          640,  666,  641, 1053,  471,  595, 1061,  453, 1069, 1287,
          454, 1073,  266, 1076,  277,  416,  434, 1082, 1083,    9,
         1089,  695,  696,   11,  276,  508, 1114,  686,  450, 1130,
          438, 1200, 1202,  556,  490, 1220, 1219,  677,  505, 1225,
          447, 1290,  448,  531,  473,  315,  532,  307,  333,  312,
          547,  294,  334,  533,  474, 1296, 1304,  331,   31, 1324,
         1335,  342,  572,  610
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    7,    7,
            7,    7,    7,    7,    7,    7,    8,    8,    9,   10,
           11,   11,   11,   12,   12,   13,   13,   14,   15,   15,
           16,   16,   17,   17,   18,   18,   21,   21,   22,   23,
           23,   24,   24,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   45,   46,   46,   48,   47,   47,   47,   47,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   25,   25,   68,   68,   71,   71,   70,
           69,   69,   62,   74,   74,   75,   75,   76,   76,   77,
           77,   78,   78,   79,   79,   26,   26,   27,   27,   27,
           27,   27,   87,   87,   89,   89,   82,   82,   90,   90,
           91,   91,   91,   83,   83,   86,   86,   84,   84,   92,
           93,   93,   56,   56,   64,   64,   67,   67,   67,   66,
           94,   94,   95,   57,   57,   57,   57,   96,   96,   97,
           97,   98,   98,   99,  100,  100,  101,  101,  102,  102,
           54,   54,   50,   50,  104,   52,   52,  105,   51,   51,
           53,   53,   63,   63,   63,   63,   80,   80,  108,  108,
          110,  110,  111,  111,  111,  111,  109,  109,  109,  113,
          113,  113,  113,   88,   88,  116,  116,  116,  117,  117,
          114,  114,  118,  118,  120,  120,  121,  121,  115,  122,
          122,  119,  123,  123,  123,  123,  112,  112,   81,   81,
           81,   20,   20,   20,  125,  124,  124,  126,  126,  126,
          126,   59,  127,  127,  128,   60,  130,  130,  131,  131,
          132,  132,   85,  133,  133,  133,  133,  133,  133,  133,
          138,  138,  139,  139,  140,  140,  140,  140,  140,  141,
          142,  142,  137,  137,  134,  134,  136,  136,  144,  144,
          143,  143,  143,  143,  143,  143,  143,  135,  145,  145,
          147,  146,  146,   61,  103,  148,  148,   55,   55,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,  155,  149,  149,  154,  154,  157,  158,  158,
          159,  160,  161,  161,  161,  161,   19,   19,   72,   72,
           72,   72,  150,  150,  150,  150,  163,  163,  151,  151,
          153,  153,  153,  156,  156,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  169,  169,  169,  107,  171,  171,
          171,  171,  152,  152,  152,  152,  152,  152,  152,  152,
           58,   58,  166,  166,  166,  166,  172,  172,  162,  162,
          162,  173,  173,  173,  173,  173,  173,   73,   73,   65,
           65,   65,   65,  129,  129,  129,  129,  176,  175,  165,
          165,  165,  165,  165,  165,  165,  164,  164,  164,  174,
          174,  174,  174,  106,  170,  178,  178,  177,  177,  179,
          179,  179,  179,  179,  179,  179,  179,  167,  167,  167,
          167,  181,  182,  180,  180,  180,  180,  180,  180,  180,
          180,  183,  183,  183,  183
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            0,    1,    0,    1,    1,    2,    1,    3,    4,    1,
            2,    0,    1,    1,    1,    1,    1,    3,    5,    4,
            3,    4,    2,    3,    1,    1,    7,    6,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    3,    2,    0,    1,    1,    1,    1,    1,    3,
            7,   10,    5,    7,    9,    5,    3,    3,    3,    3,
            3,    3,    1,    2,    5,    7,    9,    6,    5,    6,
            3,    2,    1,    1,    1,    0,    2,    1,    3,    8,
            0,    4,    2,    1,    3,    0,    1,    0,    1,    0,
            1,    3,    1,    1,    1,    8,    9,    7,    8,    7,
            6,    8,    0,    2,    0,    2,    1,    2,    1,    2,
            1,    1,    1,    0,    2,    0,    2,    0,    2,    2,
            1,    3,    1,    4,    1,    4,    1,    1,    4,    2,
            1,    3,    3,    3,    4,    4,    5,    0,    2,    4,
            3,    1,    1,    7,    0,    2,    1,    3,    3,    4,
            1,    4,    0,    2,    5,    0,    2,    6,    0,    2,
            0,    3,    1,    2,    1,    1,    2,    0,    1,    3,
            0,    2,    1,    1,    1,    1,    6,    8,    6,    1,
            2,    1,    1,    1,    1,    1,    1,    1,    1,    3,
            3,    3,    1,    3,    3,    3,    3,    3,    1,    3,
            3,    1,    1,    2,    1,    1,    0,    1,    0,    2,
            2,    2,    4,    3,    1,    1,    3,    1,    2,    2,
            3,    2,    3,    1,    1,    2,    3,    1,    1,    3,
            2,    0,    1,    5,    5,    6,   10,    3,    5,    1,
            1,    3,    0,    2,    4,    5,    4,    4,    4,    3,
            1,    1,    1,    1,    1,    1,    0,    1,    1,    2,
            1,    1,    1,    1,    1,    1,    1,    2,    1,    3,
            1,    1,    3,    2,    2,    3,    1,    0,    1,    1,
            3,    3,    3,    4,    1,    1,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    2,    2,    2,    2,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    5,    4,    3,
            4,    4,    2,    2,    4,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    1,    3,    2,    1,
            2,    4,    2,    2,    8,    9,    8,    9,    9,   10,
            9,   10,    8,    3,    2,    0,    4,    2,    1,    3,
            2,    1,    2,    2,    2,    4,    1,    1,    1,    1,
            1,    1,    1,    1,    3,    1,    1,    1,    0,    3,
            0,    1,    1,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    3,    5,    3,    3,    4,    1,
            1,    3,    1,    1,    1,    1,    1,    3,    2,    3,
            0,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            1,    1,    4,    4,    1,    4,    4,    0,    1,    1,
            1,    3,    3,    1,    4,    2,    2,    1,    3,    1,
            4,    4,    3,    3,    3,    3,    1,    3,    1,    1,
            3,    1,    1,    4,    1,    1,    1,    3,    1,    1,
            2,    1,    3,    4,    3,    2,    0,    2,    2,    1,
            2,    1,    1,    1,    4,    3,    3,    3,    3,    6,
            3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks() {
        $this->reduceCallbacks = [
            0 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            6 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            7 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            8 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            9 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            10 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            11 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            12 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            13 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            14 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            15 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            16 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            17 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            18 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            19 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            20 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            21 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            22 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            23 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            24 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            25 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            26 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            27 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            28 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            29 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            30 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            31 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            32 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            33 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            34 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            35 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            36 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            37 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            38 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            39 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            40 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            41 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            42 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            43 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            44 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            45 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            46 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            47 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            48 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            49 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            50 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            51 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            52 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            53 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            54 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            55 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            56 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            57 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            58 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            59 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            60 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            61 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            62 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            63 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            64 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            65 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            66 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            67 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            68 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            69 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            70 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            71 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            72 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            73 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            74 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            75 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            76 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            77 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            78 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            79 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            80 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            81 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            82 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            83 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            84 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            85 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            89 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            94 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            95 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            96 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            97 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 /* nothing */
            },
            101 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            102 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            103 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            105 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            106 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            107 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            108 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            109 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            110 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            111 => function ($stackPos) {
                 $this->semValue = [];
            },
            112 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            113 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            114 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            115 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            121 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            122 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            123 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            127 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            128 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            129 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            130 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            131 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            132 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            133 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            134 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            135 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            136 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            137 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            140 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            143 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            144 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            145 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            146 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            147 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            148 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            149 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributeStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            151 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributeStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            152 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            153 => function ($stackPos) {
                 $this->semValue = array();
            },
            154 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            155 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            156 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            157 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            158 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            159 => function ($stackPos) {

        if ($this->semStack[$stackPos-(3-2)]) {
            $this->semValue = $this->semStack[$stackPos-(3-2)]; $attrs = $this->startAttributeStack[$stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments'])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
        } else {
            $startAttributes = $this->startAttributeStack[$stackPos-(3-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if (null === $this->semValue) { $this->semValue = array(); }
        }

            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => is_array($this->semStack[$stackPos-(7-5)]) ? $this->semStack[$stackPos-(7-5)] : array($this->semStack[$stackPos-(7-5)]), 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], is_array($this->semStack[$stackPos-(7-2)]) ? $this->semStack[$stackPos-(7-2)] : array($this->semStack[$stackPos-(7-2)]), $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            171 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            172 => function ($stackPos) {
                 $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            173 => function ($stackPos) {

        $e = $this->semStack[$stackPos-(2-1)];
        if ($e instanceof Expr\Throw_) {
            // For backwards-compatibility reasons, convert throw in statement position into
            // Stmt\Throw_ rather than Stmt\Expression(Expr\Throw_).
            $this->semValue = new Stmt\Throw_($e->expr, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        } else {
            $this->semValue = new Stmt\Expression($e, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        }

            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->startAttributeStack[$stackPos-(6-4)] + $this->endAttributeStack[$stackPos-(6-4)]), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkTryCatch($this->semValue);
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            181 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            182 => function ($stackPos) {
                 $this->semValue = array(); /* means: no statement */
            },
            183 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            184 => function ($stackPos) {
                 $startAttributes = $this->startAttributeStack[$stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
            },
            185 => function ($stackPos) {
                 $this->semValue = array();
            },
            186 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            187 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            188 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            189 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            190 => function ($stackPos) {
                 $this->semValue = null;
            },
            191 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            192 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            193 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            194 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            195 => function ($stackPos) {
                 $this->semValue = false;
            },
            196 => function ($stackPos) {
                 $this->semValue = true;
            },
            197 => function ($stackPos) {
                 $this->semValue = false;
            },
            198 => function ($stackPos) {
                 $this->semValue = true;
            },
            199 => function ($stackPos) {
                 $this->semValue = false;
            },
            200 => function ($stackPos) {
                 $this->semValue = true;
            },
            201 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            202 => function ($stackPos) {
                 $this->semValue = [];
            },
            203 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            204 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            208 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            209 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            211 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            212 => function ($stackPos) {
                 $this->semValue = null;
            },
            213 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            214 => function ($stackPos) {
                 $this->semValue = null;
            },
            215 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            216 => function ($stackPos) {
                 $this->semValue = 0;
            },
            217 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            218 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            219 => function ($stackPos) {
                 $this->checkClassModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            220 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            221 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            222 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            223 => function ($stackPos) {
                 $this->semValue = null;
            },
            224 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            225 => function ($stackPos) {
                 $this->semValue = array();
            },
            226 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            227 => function ($stackPos) {
                 $this->semValue = array();
            },
            228 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            229 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            230 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            231 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            232 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            233 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            234 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            236 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            237 => function ($stackPos) {
                 $this->semValue = null;
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            239 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            240 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            241 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            242 => function ($stackPos) {
                 $this->semValue = new Stmt\DeclareDeclare($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            244 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            245 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            246 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            247 => function ($stackPos) {
                 $this->semValue = array();
            },
            248 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            249 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            250 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            251 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            252 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            253 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            254 => function ($stackPos) {
                 $this->semValue = [];
            },
            255 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            256 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            257 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            258 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            259 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            260 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            261 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            262 => function ($stackPos) {
                 $this->semValue = array();
            },
            263 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            264 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], is_array($this->semStack[$stackPos-(5-5)]) ? $this->semStack[$stackPos-(5-5)] : array($this->semStack[$stackPos-(5-5)]), $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            265 => function ($stackPos) {
                 $this->semValue = array();
            },
            266 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            267 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->fixupAlternativeElse($this->semValue);
            },
            268 => function ($stackPos) {
                 $this->semValue = null;
            },
            269 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_(is_array($this->semStack[$stackPos-(2-2)]) ? $this->semStack[$stackPos-(2-2)] : array($this->semStack[$stackPos-(2-2)]), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            270 => function ($stackPos) {
                 $this->semValue = null;
            },
            271 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->fixupAlternativeElse($this->semValue);
            },
            272 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            273 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            274 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            275 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            276 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            277 => function ($stackPos) {
                 $this->semValue = array();
            },
            278 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            279 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            280 => function ($stackPos) {
                 $this->semValue = 0;
            },
            281 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            282 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            283 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            284 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            285 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            287 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes, $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            288 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            289 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            290 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            291 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            292 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            293 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            294 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            295 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            297 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            298 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            299 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            300 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            301 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            302 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            303 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            304 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            305 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            306 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            307 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            308 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            309 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            310 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            311 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            312 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            313 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            314 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            315 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            316 => function ($stackPos) {
                 $this->semValue = null;
            },
            317 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            318 => function ($stackPos) {
                 $this->semValue = null;
            },
            319 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            320 => function ($stackPos) {
                 $this->semValue = null;
            },
            321 => function ($stackPos) {
                 $this->semValue = array();
            },
            322 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            323 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            325 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            326 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            327 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            328 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            329 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            330 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->semStack[$stackPos-(3-1)]);
            },
            331 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            332 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            333 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            334 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            335 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            336 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            337 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            339 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            340 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            341 => function ($stackPos) {
                 $this->semValue = array();
            },
            342 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            344 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            345 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-2)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-4)]);
            $this->checkClassConst($this->semValue, $stackPos-(6-2));
            },
            346 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            347 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            348 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            349 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            350 => function ($stackPos) {
                 $this->semValue = array();
            },
            351 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            352 => function ($stackPos) {
                 $this->semValue = array();
            },
            353 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            354 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            355 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            356 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            357 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            358 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            359 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            360 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            361 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            362 => function ($stackPos) {
                 $this->semValue = null;
            },
            363 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            364 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            365 => function ($stackPos) {
                 $this->semValue = 0;
            },
            366 => function ($stackPos) {
                 $this->semValue = 0;
            },
            367 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            368 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            369 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            370 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            371 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            372 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            373 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_STATIC;
            },
            374 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            375 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            376 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            377 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            378 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            379 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            381 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            382 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            383 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            384 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            385 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            386 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            387 => function ($stackPos) {
                 $this->semValue = array();
            },
            388 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            389 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            392 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            394 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            395 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            396 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            397 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            431 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            443 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            444 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            446 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            447 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            449 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            458 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            459 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            464 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            465 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            466 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            467 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            468 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            480 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            482 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            483 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            484 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            485 => function ($stackPos) {
                 $this->semValue = array();
            },
            486 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            487 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            488 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            489 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            490 => function ($stackPos) {
                 $this->semValue = new Expr\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            491 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            492 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            493 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            494 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            495 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            496 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            497 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            498 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            499 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            500 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            501 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            502 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            503 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            504 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            505 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            506 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            507 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            508 => function ($stackPos) {
                 $this->semValue = null;
            },
            509 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            510 => function ($stackPos) {
                 $this->semValue = array();
            },
            511 => function ($stackPos) {
                 $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$stackPos-(1-1)], '`'), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            512 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', true); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            513 => function ($stackPos) {
                 $this->semValue = array();
            },
            514 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            515 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            516 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            517 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            518 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            519 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            520 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            521 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            522 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            523 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            524 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            525 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            526 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)]), $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->errorState = 2;
            },
            527 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            528 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            },
            529 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            530 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            531 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$stackPos-(3-2)], $attrs);
            },
            532 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            533 => function ($stackPos) {
                 $this->semValue = Scalar\DNumber::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            534 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            535 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            536 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            537 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            538 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(2-2)] + $this->endAttributeStack[$stackPos-(2-2)], true);
            },
            539 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            540 => function ($stackPos) {
                 $this->semValue = null;
            },
            541 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            542 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            543 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            544 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            545 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            546 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            547 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            548 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            549 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            550 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            551 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            552 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            553 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            554 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            555 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            556 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            557 => function ($stackPos) {
                 $this->semValue = null;
            },
            558 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            559 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            560 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            561 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            562 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            563 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            564 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            565 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            566 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes); $this->errorState = 2;
            },
            567 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes) : $var;
            },
            568 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            569 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            570 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            571 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            573 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            574 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            575 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            576 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            577 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            578 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            579 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            580 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            581 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            582 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            583 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            584 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end] === null) array_pop($this->semValue);
            },
            585 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            586 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            587 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            588 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            589 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            591 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            592 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            593 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            594 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            595 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, true);
            },
            596 => function ($stackPos) {
                 $this->semValue = null;
            },
            597 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            598 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            599 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            600 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            601 => function ($stackPos) {
                 $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            602 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            603 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            604 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            605 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            606 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            607 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            608 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            609 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            610 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            611 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            612 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            613 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            614 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
        ];
    }
}
