<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم Cookie Consent المدمج والمميز</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/cookie-consent.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-50 to-pink-50 min-h-screen">
    <!-- Demo Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                    🍪 Cookie Consent المدمج والمميز
                </h1>
                <p class="text-gray-600 text-lg">
                    تصميم عصري، مدمج، وجذاب مع تأثيرات بصرية متقدمة
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-2xl shadow-xl p-6 border border-purple-100">
                    <h2 class="text-xl font-bold text-purple-800 mb-4 flex items-center gap-2">
                        ✨ المميزات الجديدة
                    </h2>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            تصميم مدمج وأنيق (لا يشغل مساحة كبيرة)
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-pink-500 rounded-full"></span>
                            تأثيرات بصرية متقدمة وجذابة
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            أنيميشن سلس للظهور والاختفاء
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-pink-500 rounded-full"></span>
                            تدرجات لونية متحركة
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            تصميم متجاوب لجميع الأجهزة
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-6 border border-pink-100">
                    <h2 class="text-xl font-bold text-pink-800 mb-4 flex items-center gap-2">
                        🎨 التحسينات التقنية
                    </h2>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            موضع ثابت في الزاوية (غير مزعج)
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            تأثيرات hover تفاعلية
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            إشعارات مصغرة للتأكيد
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            دعم كامل للغة العربية
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            أداء محسن وسرعة عالية
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="text-center">
                <button id="showCookieBanner" class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    🍪 عرض Cookie Banner المميز
                </button>
                <p class="text-gray-500 text-sm mt-4">
                    انقر لمشاهدة التصميم الجديد في الزاوية السفلية اليمنى
                </p>
            </div>
        </div>
    </div>

    <!-- Compact Cookie Consent Banner -->
    <div id="cookieConsentBanner" class="fixed bottom-6 right-6 z-50 max-w-sm transform translate-x-full transition-all duration-700 ease-out opacity-0">
        <!-- Floating Card -->
        <div class="cookie-banner-content relative bg-white rounded-2xl shadow-2xl border border-purple-200 overflow-hidden backdrop-blur-lg">
            <!-- Gradient Border Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 rounded-2xl opacity-75 animate-pulse"></div>
            <div class="absolute inset-[2px] bg-white rounded-2xl"></div>
            
            <!-- Content -->
            <div class="relative p-4">
                <!-- Header with Cookie Icon -->
                <div class="flex items-center gap-3 mb-3">
                    <div class="cookie-icon-container w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                        <span class="text-lg animate-bounce">🍪</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="cookie-title text-sm font-bold text-gray-800 flex items-center gap-1">
                            <span>ملفات تعريف الارتباط</span>
                            <div class="w-1.5 h-1.5 bg-purple-500 rounded-full animate-ping"></div>
                        </h3>
                    </div>
                    <!-- Close Button -->
                    <button id="cookieClose" class="cookie-close-btn w-6 h-6 bg-gray-100 hover:bg-red-100 rounded-full flex items-center justify-center transition-all duration-200 group">
                        <svg class="w-3 h-3 text-gray-400 group-hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Message -->
                <p class="text-xs text-gray-600 leading-relaxed mb-4">
                    نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا.
                    <a href="#" class="cookie-learn-more text-purple-600 hover:text-purple-700 font-medium underline decoration-dotted underline-offset-2">
                        تعرف على المزيد
                    </a>
                </p>
                
                <!-- Action Buttons -->
                <div class="flex gap-2">
                    <button id="cookieDecline" class="cookie-decline-btn flex-1 px-3 py-2 text-xs font-medium text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all duration-200 border border-gray-200">
                        رفض
                    </button>
                    <button id="cookieAccept" class="cookie-accept-btn flex-1 px-3 py-2 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                        موافق
                    </button>
                </div>
                
                <!-- Powered by (smaller) -->
                <div class="mt-3 pt-2 border-t border-gray-100">
                    <p class="text-[10px] text-gray-400 text-center">
                        Powered by <span class="font-medium text-purple-400">WebsitePolicies</span>
                    </p>
                </div>
            </div>
            
            <!-- Floating Particles Effect -->
            <div class="absolute top-2 right-2 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-60"></div>
            <div class="absolute bottom-3 left-3 w-0.5 h-0.5 bg-pink-400 rounded-full animate-pulse opacity-40"></div>
            <div class="absolute top-1/2 left-1 w-0.5 h-0.5 bg-purple-300 rounded-full animate-bounce opacity-50"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const banner = document.getElementById('cookieConsentBanner');
            const acceptBtn = document.getElementById('cookieAccept');
            const declineBtn = document.getElementById('cookieDecline');
            const closeBtn = document.getElementById('cookieClose');
            const showBtn = document.getElementById('showCookieBanner');
            
            function showBanner() {
                banner.classList.remove('translate-x-full', 'opacity-0');
                banner.classList.add('cookie-banner-enter');
                
                setTimeout(() => {
                    banner.style.animation = 'shake 0.5s ease-in-out';
                }, 800);
            }
            
            function hideBanner(action = 'dismissed') {
                banner.style.transform = 'translateX(100%) scale(0.8)';
                banner.style.opacity = '0';
                
                setTimeout(() => {
                    banner.classList.add('translate-x-full', 'opacity-0');
                    banner.classList.remove('cookie-banner-enter');
                    banner.style.transform = '';
                    banner.style.animation = '';
                    
                    if (action === 'accepted') {
                        showMiniToast('✅ تم القبول', 'success');
                    } else if (action === 'declined') {
                        showMiniToast('❌ تم الرفض', 'error');
                    }
                }, 400);
            }
            
            function showMiniToast(message, type = 'info') {
                const toast = document.createElement('div');
                const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                
                toast.className = `fixed bottom-20 right-6 z-50 px-4 py-2 rounded-full shadow-lg text-white text-xs font-medium transform translate-x-full transition-all duration-300 ${bgColor}`;
                toast.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span>${message}</span>
                        <div class="w-1 h-1 bg-white rounded-full animate-ping"></div>
                    </div>
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.classList.remove('translate-x-full');
                    toast.style.transform = 'translateX(0) scale(1.1)';
                    setTimeout(() => {
                        toast.style.transform = 'translateX(0) scale(1)';
                    }, 150);
                }, 100);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%) scale(0.8)';
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 2000);
            }
            
            showBtn.addEventListener('click', showBanner);
            acceptBtn.addEventListener('click', () => hideBanner('accepted'));
            declineBtn.addEventListener('click', () => hideBanner('declined'));
            closeBtn.addEventListener('click', () => hideBanner('dismissed'));
        });
    </script>
</body>
</html>
