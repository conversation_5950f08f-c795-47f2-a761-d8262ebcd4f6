/*** 

====================================================================
		About Section
====================================================================

***/

.about-section {
    position: relative;
    padding: 100px 0 50px;
    &:before{
        position: absolute;
        left: 0;
        bottom: 0;
        height: 100%;
        width: 35%;
        background-image: url(../images/icons/gradient.jpg);
        content: "";
    }
    .content-column {
        position: relative;
        margin-bottom: 50px;
        .inner-column {
            position: relative;
            padding-left: 70px;
            @include media-breakpoint-down(xl){
                padding-left: 0;
            }
        }
        .sec-title{
            margin-bottom: 35px;
        }

        .info-box{
            position: relative;
            padding-left: 105px;
            margin-bottom: 30px;
            @include media-breakpoint-down(sm){
                padding-left: 0;
                text-align: center;
            }
            &:hover{
                .icon{
                    color: var(--theme-color2);
                }
            }
            .icon{
                position: absolute;
                left: 0;
                top: 0px;
                color: var(--theme-color1);
                line-height: 1em;
                font-size: 62px;
                transition: all 300ms ease;
                @include media-breakpoint-down(sm){
                    position: relative;
                    display: inline-block;
                    margin-bottom: 20px;
                }
                &:after{
                    position: absolute;
                    left: 20px;
                    top: 20px;
                    height: 52px;
                    width: 52px;
                    z-index: -1;
                    background-color: #f2f3f6;
                    content: "";
                }
            }
            .title{
                margin-bottom: 5px;
            }
        }

        .other-info{
            position: relative;
            padding-right: 230px;
            margin-top: 42px;
            @include media-breakpoint-down(md){
                padding-right: 0;
            }
            .theme-btn{
                position: absolute;
                right: 0;
                top: 10px;
                @include media-breakpoint-down(md){
                    position: relative;
                    margin-top: 30px;
                    top: 0;
                }
            }
        }
        .author-info{
            position: relative;
            padding-left: 110px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 88px;
            .thumb{
                position: absolute;
                left: 0;
                top: 0;
                height: 88px;
                width: 88px;
                padding: 2px;
                background: linear-gradient(to top, var(--theme-color2), var(--theme-color1));
                border-radius: 50%;
                img{
                    border-radius: 50%;
                    border: 9px solid #fff;
                }
            }
            .name{
                margin-bottom: 0;
            }
            .designation{
                font-size: 13px;
                line-height: 15px;
                margin-bottom: 0;
            }
        }
    }
    .image-column {
        position: relative;
        margin-bottom: 50px;
        @include media-breakpoint-down(lg){
            order: 3;
        }
        .image-box{
            position: relative;
            padding-bottom: 120px;
            @include media-breakpoint-down(md){
                padding-bottom: 0;
            }
            .icon-circle{
                position: absolute;
                left: 85px;
                bottom: 40px;
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
            .icon-dots{
                position: absolute;
                right: 20px;
                bottom: 70px;
            }
        }
        .image-1{
            margin: 0;
        }
        .image-2{
            position: absolute;
            right: -23px;
            bottom: 0;
            border: 10px solid #fff;
            margin-bottom: 0;
            box-shadow: 0 10px 60px rgba(0,0,0,.10);
            @include media-breakpoint-down(xl){
                right: 0;
            }
            @include media-breakpoint-down(md){
                display: none;
            }
        }
        .exp-box{
            position: absolute;
            left: -80px;
            top: 90px;
            padding: 40px 40px 60px;
            background-image: url(../images/icons/shape2.png);
            height: 170px;
            width: 303px;
            @include media-breakpoint-down(xl){
                left: 15px;
            }
            @include media-breakpoint-down(md){
                display: none;
            }
            .inner{
                position: relative;
                padding-left: 85px;
            }
            .icon{
                position: absolute;
                left: 0;
                top: 0;
                font-size: 62px;
                color: var(--theme-color2);
            }
            .count{
                font-size: 48px;
                font-weight: 600;
                line-height: 1em;
                color: var(--theme-color2);
            }
            .text{
                font-size: 15px;
                color: var(--theme-color2);
                line-height: 1em;
                font-weight: 600;
            }
        }
    }
}

/*** 

====================================================================
		About Section Two
====================================================================

***/

.about-section-two{
    position: relative;
    padding: 120px 0 70px;
    overflow: hidden;
    .icon-line4{
        top: 90px;
        left: -130px;
    }
    .icon-line5{
        right: -475px;
        top: -56px;
    }
    .icon-arrow1{
        left: -190px;
        top:380px;
    }
    .icon-speaker{
        left: -375px;
        top: 430px;
    }
    
    .content-column {
        position: relative;
        margin-bottom: 50px;
        z-index: 1;
        .inner-column {
            position: relative;
        }
    }
    .sec-title{
        margin-bottom: 35px;
    }
    .info-box{
        position: relative;
        margin-bottom: 30px;
        .inner{
            position: relative;
            &:hover{
                .icon{
                    color: var(--theme-color1);
                }
            }
        }
        .icon{
            font-size: 14px;
            color: var(--theme-color1);
            margin-right: 5px;
            transition: all 300ms ease;
        }
        .title{
            margin-bottom: 20px;
        }
        .text{
            margin-bottom: 0;
        }
    } 
    .skills{
        margin-bottom: 45px;
    }

    .image-column {
        position: relative;
        margin-bottom: 30px;
        @include media-breakpoint-down(lg){
            order: 2;
        }
        .inner-column {
            position: relative;
        }
        .image-box{
            position: relative;
            padding-top: 100px;
            .icon-dots2{
                position: absolute;
                left: 70px;
                top: 40px;
            }
            @include media-breakpoint-down(lg){
                text-align: center;
            }
        }
        .image-1{
            position: relative;
            margin-bottom: 0;
        }
        .image-2{
            position: absolute;
            right: 45px;
            top: 0;
            margin-bottom: 20px;
            img {
                width: 100%;
            }
            @include media-breakpoint-down(lg){
                display: none;
            }
        }
        .exp-box {
            position: absolute;
            left: 220px;
            bottom: 55px;
            width: 275px;
            background-color: var(--theme-color2);
            padding: 10px;
            text-align: left;
            @include media-breakpoint-down(xl){
                left: 15px;
                bottom: 15px;
            }
            @include media-breakpoint-down(sm){
                position: relative;
                left: 0;
                bottom: 0;
                width: 100%;
            }
            .inner{
                position: relative;
                padding: 20px 30px 25px;
                padding-left: 110px;
                background-color: var(--theme-color-light);
                border: 4px solid var(--theme-color1);
            }
            .icon{
                position: absolute;
                left: 25px;
                top: 25px;
                display: block;
                font-size: 64px;
                font-weight: 400;
                color: var(--theme-color1);
                z-index: 2;
                margin-bottom: 5px;
            }
            .count{
                font-size: 46px;
                line-height: 1em;
                color: var(--theme-color2);
                font-weight: 700;
                margin-bottom: 10px;
            }
            .title{
                display: block;
                font-size: 14px;
                font-weight: 400;
                margin-bottom: 0;
            }
        }
    }
}
