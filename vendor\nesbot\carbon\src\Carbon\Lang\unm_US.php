<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD/MM/YY',
    ],
    'months' => ['enikwsi', 'chkwali', 'xamokhwite', 'kwetayoxe', 'tainipen', 'kichinipen', 'lainipen', 'winaminke', 'kichitahkok', 'puksit', 'wini', 'muxkotae'],
    'months_short' => ['eni', 'chk', 'xam', 'kwe', 'tai', 'nip', 'lai', 'win', 'tah', 'puk', 'kun', 'mux'],
    'weekdays' => ['kentuwei', 'manteke', 'tusteke', 'lelai', 'tasteke', 'pelaiteke', 'sateteke'],
    'weekdays_short' => ['ken', 'man', 'tus', 'lel', 'tas', 'pel', 'sat'],
    'weekdays_min' => ['ken', 'man', 'tus', 'lel', 'tas', 'pel', 'sat'],
    'day_of_first_week_of_year' => 1,

    // Too unreliable
    /*
    'year' => ':count kaxtëne',
    'y' => ':count kaxtëne',
    'a_year' => ':count kaxtëne',

    'month' => ':count piskewëni kishux', // less reliable
    'm' => ':count piskewëni kishux', // less reliable
    'a_month' => ':count piskewëni kishux', // less reliable

    'week' => ':count kishku', // less reliable
    'w' => ':count kishku', // less reliable
    'a_week' => ':count kishku', // less reliable

    'day' => ':count kishku',
    'd' => ':count kishku',
    'a_day' => ':count kishku',

    'hour' => ':count xkuk', // less reliable
    'h' => ':count xkuk', // less reliable
    'a_hour' => ':count xkuk', // less reliable

    'minute' => ':count txituwàk', // less reliable
    'min' => ':count txituwàk', // less reliable
    'a_minute' => ':count txituwàk', // less reliable

    'second' => ':count nisha', // less reliable
    's' => ':count nisha', // less reliable
    'a_second' => ':count nisha', // less reliable
    */
]);
