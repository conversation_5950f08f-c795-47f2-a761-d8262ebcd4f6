.whatsapp-float {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 100px;
  right: 30px;
  background-color: #25d366;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: transform 0.3s ease, background-color 0.3s ease;
  animation: float 2s infinite ease-in-out;
}

/* تأثير عند تمرير الماوس */
.whatsapp-float:hover {
  background-color: #20b456;
  transform: scale(1.1);
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
}

/* نمط الأيقونة */
.whatsapp-icon {
  font-size: 30px;
}

/* أنيميشن الأيقونة العائمة */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* my code */
.clients-grid div {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.clients-grid img {
  height: 100px; /* اضبط الارتفاع حسب الحاجة */
  object-fit: contain;
}

.clients-grid p {
  min-height: 40px; /* لضمان تناسق النصوص */
  margin-top: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #4a5568;
}

/* Service Detail Page Styles */
.service-detail-content {
  direction: rtl;
  text-align: right;
}

.service-detail-content h1,
.service-detail-content h2,
.service-detail-content h3 {
  text-align: right;
}

.service-detail-content p {
  text-align: right;
  line-height: 1.8;
}

/* FAQ Accordion Styles */
.faq-accordion {
  transition: all 0.3s ease;
}

.faq-accordion:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* RTL Support for Icons */
[dir="rtl"] .service-nav-icon {
  transform: rotate(180deg);
}

/* Smooth transitions for service cards */
.service-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Pricing Cards Styles */
.pricing-card {
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Popular badge animation */
.popular-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
  }
}

/* Pricing feature list styling */
.pricing-features li {
  transition: all 0.2s ease;
}

.pricing-features li:hover {
  transform: translateX(5px);
  color: #7c3aed;
}

/* CTA Button hover effects */
.pricing-cta {
  position: relative;
  overflow: hidden;
}

.pricing-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.pricing-cta:hover::before {
  left: 100%;
}

/* Portfolio Cards Styles */
.portfolio-card {
  transition: all 0.3s ease;
}

.portfolio-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Portfolio image hover effects */
.portfolio-image {
  transition: transform 0.5s ease;
}

.portfolio-card:hover .portfolio-image {
  transform: scale(1.1);
}

/* Portfolio overlay effects */
.portfolio-overlay {
  transition: opacity 0.3s ease;
}

/* Line clamp utility for text truncation */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Portfolio category badge */
.portfolio-badge {
  backdrop-filter: blur(10px);
  background: rgba(147, 51, 234, 0.9);
}

/* Portfolio action button hover effects */
.portfolio-action {
  transition: all 0.3s ease;
}

.portfolio-action:hover {
  transform: translateX(5px);
}

/* Empty state styling */
.empty-state {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Blog Cards Styles */
.blog-card {
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Blog image hover effects */
.blog-image {
  transition: transform 0.5s ease;
}

.blog-card:hover .blog-image {
  transform: scale(1.1);
}

/* Blog date badge */
.blog-date-badge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

/* Blog category badge */
.blog-category-badge {
  backdrop-filter: blur(10px);
  background: rgba(147, 51, 234, 0.9);
}

/* Blog read more button */
.blog-read-more {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
  transition: all 0.3s ease;
}

.blog-read-more:hover {
  background: linear-gradient(135deg, #6d28d9, #4338ca);
  transform: scale(1.05);
}

/* Blog tags styling */
.blog-tag {
  transition: all 0.2s ease;
}

.blog-tag:hover {
  background-color: #e5e7eb;
  transform: scale(1.05);
}

/* Newsletter section styling */
.newsletter-section {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
}

/* Pagination styling */
.pagination-wrapper {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Blog meta icons */
.blog-meta-icon {
  transition: color 0.2s ease;
}

.blog-meta-icon:hover {
  color: #7c3aed;
}

/* Contact Page Styles */
.contact-form {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.contact-form input,
.contact-form textarea {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: #7c3aed;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
  transform: translateY(-2px);
}

.contact-form label {
  color: #374151;
  font-weight: 500;
}

/* Contact submit button */
.contact-submit-btn {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
  transition: all 0.3s ease;
}

.contact-submit-btn:hover {
  background: linear-gradient(135deg, #6d28d9, #4338ca);
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(124, 58, 237, 0.3);
}

/* Contact info cards */
.contact-info-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.contact-info-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Contact icons */
.contact-icon {
  transition: all 0.3s ease;
}

.contact-icon:hover {
  transform: scale(1.1);
  color: #a855f7;
}

/* Map section styling */
.map-section {
  padding: 2rem 1rem;
  background: #f9fafb;
}

.map-section iframe {
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Contact hero section */
.contact-hero {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
}

/* Rating stars animation */
.rating-stars svg {
  transition: all 0.2s ease;
}

.rating-stars:hover svg {
  transform: scale(1.1);
}
