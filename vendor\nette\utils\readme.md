Nette Utility Classes
=====================

[![Downloads this Month](https://img.shields.io/packagist/dm/nette/utils.svg)](https://packagist.org/packages/nette/utils)
[![Tests](https://github.com/nette/utils/workflows/Tests/badge.svg?branch=master)](https://github.com/nette/utils/actions)
[![Coverage Status](https://coveralls.io/repos/github/nette/utils/badge.svg?branch=master)](https://coveralls.io/github/nette/utils?branch=master)
[![Latest Stable Version](https://poser.pugx.org/nette/utils/v/stable)](https://github.com/nette/utils/releases)
[![License](https://img.shields.io/badge/license-New%20BSD-blue.svg)](https://github.com/nette/utils/blob/master/license.md)


Introduction
------------

In package nette/utils you will find a set of [useful classes](https://doc.nette.org/utils) for everyday use:

- [Arrays](https://doc.nette.org/utils/arrays) - manipulate arrays
- [Callback](https://doc.nette.org/utils/callback) - PHP callbacks
- [Date and Time](https://doc.nette.org/utils/datetime) - modify times and dates
- [Filesystem](https://doc.nette.org/utils/filesystem) - copying, renaming, …
- [Finder](https://doc.nette.org/utils/finder) - finds files and directories
- [Helper Functions](https://doc.nette.org/utils/helpers)
- [HTML elements](https://doc.nette.org/utils/html-elements) - generate HTML
- [Images](https://doc.nette.org/utils/images) - crop, resize, rotate images
- [JSON](https://doc.nette.org/utils/json) - encoding and decoding
- [Generating Random Strings](https://doc.nette.org/utils/random)
- [Paginator](https://doc.nette.org/utils/paginator) - pagination math
- [PHP Reflection](https://doc.nette.org/utils/reflection)
- [Strings](https://doc.nette.org/utils/strings) - useful text functions
- [SmartObject](https://doc.nette.org/utils/smartobject) - PHP object enhancements
- [Validation](https://doc.nette.org/utils/validators) - validate inputs
- [Type](https://doc.nette.org/utils/type) - PHP data type


Installation
------------

The recommended way to install is via Composer:

```
composer require nette/utils
```

- Nette Utils 4.0 is compatible with PHP 8.0 to 8.3
- Nette Utils 3.2 is compatible with PHP 7.2 to 8.3
- Nette Utils 3.1 is compatible with PHP 7.1 to 8.0
- Nette Utils 3.0 is compatible with PHP 7.1 to 8.0
- Nette Utils 2.5 is compatible with PHP 5.6 to 8.0

[Support Me](https://github.com/sponsors/dg)
--------------------------------------------

Do you like Nette Utils? Are you looking forward to the new features?

[![Buy me a coffee](https://files.nette.org/icons/donation-3.svg)](https://github.com/sponsors/dg)

Thank you!
