{"name": "laravel/sanctum", "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["laravel", "sanctum", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-json": "*", "illuminate/console": "^9.21|^10.0", "illuminate/contracts": "^9.21|^10.0", "illuminate/database": "^9.21|^10.0", "illuminate/support": "^9.21|^10.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.28.2|^8.8.3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6"}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Sanctum\\Tests\\": "tests/", "Workbench\\App\\": "workbench/app/", "Workbench\\Database\\Factories\\": "workbench/database/factories/"}}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}