@extends('front.layouts.master')

@section('seo_title', $global_other_page_items->page_contact_seo_title)
@section('seo_meta_description', $global_other_page_items->page_contact_seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-purple-600 to-indigo-600 py-20 overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $global_other_page_items->page_contact_title }}</h1>
            <p class="text-xl text-purple-100 mb-8">نحن هنا لمساعدتك، تواصل معنا في أي وقت</p>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 text-white">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-purple-200 transition-colors">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-purple-200">{{ $global_other_page_items->page_contact_title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="min-h-screen bg-gray-900 py-16 lg:py-24">
    <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:items-stretch md:grid-cols-2 gap-x-12 lg:gap-x-20 gap-y-10">

            <!-- Contact Info Side -->
            <div class="flex flex-col justify-between py-10 lg:py-5">
                <div>
                    <h2 class="text-3xl sm:pt-10 font-bold leading-tight text-white sm:text-4xl lg:leading-tight lg:text-5xl">
                        {{ $global_other_page_items->page_contact_info_heading ? strip_tags($global_other_page_items->page_contact_info_heading) : 'تواصل معنا' }}
                    </h2>
                    <p class="max-w-xl mx-auto mt-4 text-base sm:pt-10 leading-relaxed text-white">
                        {{ $global_other_page_items->page_contact_info_text ? strip_tags($global_other_page_items->page_contact_info_text) : 'للمزيد من المعلومات وعروض الأسعار تواصل معنا' }}
                    </p>

                    <!-- Decorative Images -->
                    <img class="relative z-10 max-w-xs mx-auto -mb-16 md:hidden"
                         src="https://cdn.rareblocks.xyz/collection/celebration/images/contact/4/curve-line-mobile.svg" alt="" />
                    <img class="hidden w-full translate-x-24 translate-y-8 md:block"
                         src="https://cdn.rareblocks.xyz/collection/celebration/images/contact/4/curve-line.svg" alt="" />
                </div>

                <!-- Contact Information -->
                <div class="hidden md:mt-auto md:block">
                    <!-- Rating Stars -->
                    <div class="flex items-center mb-6">
                        @for($i = 1; $i <= 5; $i++)
                        <svg class="w-6 h-6 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        @endfor
                        <span class="text-white ml-3">عملاء راضون 100%</span>
                    </div>

                    <!-- Contact Details -->
                    <div class="space-y-4">
                        <!-- Phone -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">{{ $global_other_page_items->page_contact_info_phone_title }}</p>
                                <a href="tel:{{ $global_other_page_items->page_contact_info_phone_value }}"
                                   class="text-base font-semibold text-white hover:text-purple-300 transition-colors">
                                    {{ $global_other_page_items->page_contact_info_phone_value }}
                                </a>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">{{ $global_other_page_items->page_contact_info_email_title }}</p>
                                <a href="mailto:{{ $global_other_page_items->page_contact_info_email_value }}"
                                   class="text-base font-semibold text-white hover:text-purple-300 transition-colors">
                                    {{ $global_other_page_items->page_contact_info_email_value }}
                                </a>
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">{{ $global_other_page_items->page_contact_info_address_title }}</p>
                                <span class="text-base font-semibold text-white">
                                    {{ $global_other_page_items->page_contact_info_address_value }}
                                </span>
                            </div>
                        </div>

                        <!-- WhatsApp -->
                        @if($global_setting->whatsapp != null)
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                                </svg>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">واتساب</p>
                                <a href="https://wa.me/{{ $global_setting->whatsapp }}"
                                   target="_blank"
                                   class="text-base font-semibold text-white hover:text-green-300 transition-colors">
                                    {{ $global_setting->whatsapp }}
                                </a>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Contact Form Side -->
            <div class="overflow-hidden bg-white rounded-md">
                <div class="px-6 py-12 sm:p-12">
                    <h3 class="text-3xl font-semibold text-center text-black mb-8">
                        {{ $global_other_page_items->page_contact_send_mail_heading ? strip_tags($global_other_page_items->page_contact_send_mail_heading) : 'احصل على عرض سعر' }}
                    </h3>

                    <form id="contact_form" name="contact_form" action="{{ route('contact_send_message') }}" method="post" class="mt-4">
                        @csrf
                        <div class="space-y-6">
                            <!-- Name Field -->
                            <div>
                                <label for="name" class="text-base font-medium text-gray-900 text-right block mb-2">
                                    {{ __('Full Name') }}
                                </label>
                                <div class="mt-2.5 relative">
                                    <input type="text"
                                           name="name"
                                           id="name"
                                           placeholder="{{ __('Full Name') }}"
                                           required
                                           class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500 text-right" />
                                </div>
                            </div>

                            <!-- Email Field -->
                            <div>
                                <label for="email" class="text-base font-medium text-gray-900 text-right block mb-2">
                                    {{ __('Email Address') }}
                                </label>
                                <div class="mt-2.5 relative">
                                    <input type="email"
                                           name="email"
                                           id="email"
                                           placeholder="{{ __('Email Address') }}"
                                           required
                                           class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500 text-right" />
                                </div>
                            </div>

                            <!-- Subject Field -->
                            <div>
                                <label for="subject" class="text-base font-medium text-gray-900 text-right block mb-2">
                                    {{ __('Subject') }}
                                </label>
                                <div class="mt-2.5 relative">
                                    <input type="text"
                                           name="subject"
                                           id="subject"
                                           placeholder="{{ __('Subject') }}"
                                           required
                                           class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500 text-right" />
                                </div>
                            </div>

                            <!-- Message Field -->
                            <div>
                                <label for="message" class="text-base font-medium text-gray-900 text-right block mb-2">
                                    {{ __('Message') }}
                                </label>
                                <div class="mt-2.5 relative">
                                    <textarea name="message"
                                              id="message"
                                              placeholder="{{ __('Message') }}"
                                              rows="6"
                                              required
                                              class="block w-full px-4 py-4 text-black placeholder-gray-500 transition-all duration-200 bg-white border border-gray-200 rounded-md resize-y focus:outline-none focus:ring-purple-500 focus:border-purple-500 caret-purple-500 text-right"></textarea>
                                </div>
                            </div>

                            <!-- reCAPTCHA -->
                            @if($global_setting->google_recaptcha_status == 'Show')
                            <div>
                                <div class="g-recaptcha" data-sitekey="{{ $global_setting->google_recaptcha_site_key }}"></div>
                            </div>
                            @endif

                            <!-- Submit Button -->
                            <div>
                                <input name="form_botcheck" type="hidden" value="" />
                                <button type="submit"
                                        data-loading-text="{{ __('Please wait...') }}"
                                        class="inline-flex items-center justify-center w-full px-4 py-4 text-base font-semibold text-white transition-all duration-200 bg-purple-700 border border-transparent rounded-md focus:outline-none hover:bg-purple-400 focus:bg-purple-500 transform hover:scale-105">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    {{ __('Send Message') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Mobile Contact Info -->
        <div class="md:hidden mt-10">
            <div class="space-y-6">
                <!-- Rating Stars -->
                <div class="flex items-center justify-center mb-6">
                    @for($i = 1; $i <= 5; $i++)
                    <svg class="w-6 h-6 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    @endfor
                    <span class="text-white ml-3">عملاء راضون 100%</span>
                </div>

                <!-- Contact Cards -->
                <div class="grid grid-cols-1 gap-4">
                    <!-- Phone Card -->
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-purple-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">{{ $global_other_page_items->page_contact_info_phone_title }}</p>
                                <a href="tel:{{ $global_other_page_items->page_contact_info_phone_value }}"
                                   class="text-white font-semibold">
                                    {{ $global_other_page_items->page_contact_info_phone_value }}
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Email Card -->
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-purple-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <div class="mr-4">
                                <p class="text-sm text-gray-300">{{ $global_other_page_items->page_contact_info_email_title }}</p>
                                <a href="mailto:{{ $global_other_page_items->page_contact_info_email_value }}"
                                   class="text-white font-semibold">
                                    {{ $global_other_page_items->page_contact_info_email_value }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
@if($global_setting->map != '')
<section class="map-section">
    <div class="w-full h-96 relative overflow-hidden rounded-lg shadow-lg">
        {!! $global_setting->map !!}
    </div>
</section>
@endif

@endsection