@extends('front.layouts.master')

@section('seo_title', $global_other_page_items->page_portfolios_seo_title)
@section('seo_meta_description', $global_other_page_items->page_portfolios_seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-purple-600 to-indigo-600 py-20 overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $global_other_page_items->page_portfolios_title }}</h1>
            <p class="text-xl text-purple-100 mb-8">استكشف مجموعة من أعمالنا المتميزة ومشاريعنا الناجحة</p>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 text-white">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-purple-200 transition-colors">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-purple-200">{{ $global_other_page_items->page_portfolios_title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Portfolio Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">أعمالنا المتميزة</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                نفخر بتقديم مجموعة متنوعة من المشاريع الناجحة التي تعكس خبرتنا وإبداعنا في مختلف المجالات
            </p>
        </div>

        @if($portfolios->count() > 0)
        <!-- Portfolio Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($portfolios as $portfolio)
            <div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

                <!-- Portfolio Image -->
                <div class="relative h-64 overflow-hidden">
                    <img src="{{ asset('uploads/'.$portfolio->photo) }}"
                         alt="{{ $portfolio->name }}"
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">

                    <!-- Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <!-- View Project Button -->
                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <a href="{{ route('portfolio',$portfolio->slug) }}"
                           class="inline-flex items-center px-6 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-purple-50 transition-colors transform hover:scale-105">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض المشروع
                        </a>
                    </div>

                    <!-- Category Badge -->
                    @if($portfolio->client)
                    <div class="absolute top-4 right-4">
                        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                            {{ $portfolio->client }}
                        </span>
                    </div>
                    @endif
                </div>

                <!-- Portfolio Content -->
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-3 text-right group-hover:text-purple-600 transition-colors">
                        <a href="{{ route('portfolio',$portfolio->slug) }}" class="hover:underline">
                            {{ $portfolio->name }}
                        </a>
                    </h3>

                    @if($portfolio->description)
                    <p class="text-gray-600 text-right mb-4 line-clamp-3">
                        {{ Str::limit(strip_tags($portfolio->description), 120) }}
                    </p>
                    @endif

                    <!-- Project Details -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        @if($portfolio->date)
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {{ $portfolio->date }}
                        </span>
                        @endif

                        @if($portfolio->location)
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            {{ $portfolio->location }}
                        </span>
                        @endif
                    </div>

                    <!-- Action Button -->
                    <div class="text-center">
                        <a href="{{ route('portfolio',$portfolio->slug) }}"
                           class="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-purple-100 hover:text-purple-700 transition-colors group">
                            اقرأ المزيد
                            <svg class="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        @else
        <!-- Empty State -->
        <div class="text-center py-16">
            <div class="max-w-md mx-auto">
                <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع حالياً</h3>
                <p class="text-gray-600">سيتم إضافة المشاريع قريباً. تابعونا للاطلاع على أحدث أعمالنا.</p>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Call to Action Section -->
<section class="bg-gradient-to-r from-purple-600 to-indigo-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">هل لديك مشروع في ذهنك؟</h2>
        <p class="text-xl text-purple-100 mb-8">دعنا نساعدك في تحويل فكرتك إلى واقع مذهل</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact') }}"
               class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-purple-600 bg-white hover:bg-purple-50 transition-colors">
                ابدأ مشروعك الآن
            </a>
            @if($global_setting->whatsapp != null)
            <a href="https://wa.me/{{ $global_setting->whatsapp }}"
               target="_blank"
               class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-purple-600 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                </svg>
                تواصل عبر واتساب
            </a>
            @endif
        </div>
    </div>
</section>
@endsection