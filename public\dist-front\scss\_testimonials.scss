/*** 

====================================================================
	Testimonial Section
====================================================================

***/

.testimonial-section{
    position: relative;
    padding: 120px 0 120px;

    .title-column{
        position: relative;
        margin-bottom: 50px;
        .sec-title{
            margin-bottom: 30px;
        }
        .info-box{
            position: relative;
            padding-left: 110px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            min-height: 90px;
            @include media-breakpoint-down(sm){
                padding-left: 0;
            }
            .icon{
                position: absolute;
                left: 0;
                top: 0;
                height: 90px;
                width: 90px;
                font-size: 62px;
                background-color: var(--bg-theme-color1);
                color: var(--theme-color2);
                @include flex-center;
                @include media-breakpoint-down(sm){
                    position: relative;
                    margin-bottom: 20px;
                }
            }
            .text{
                font-size: 20px;
                line-height: 30px;
            }
        }
    }
    
    .testimonial-column{
        .inner-column{
            margin: -15px -15px -15px;
            margin-right: -285px;
            @include media-breakpoint-down(xl){
                margin-right: -15px;
            }
        }
        .owl-nav{
            position: absolute;
            left: -585px;
            bottom: 15px;
            @include media-breakpoint-down(xl){
                display: none;
            }
        }
    }
}

.testimonial-block{
    position: relative;
    overflow: hidden;
    padding: 15px 15px;
    .inner-box{
        position: relative;
        &:hover{
            .content-box{
                .icon{
                    color: var(--theme-color2);
                    background-color: var(--bg-theme-color2);
                    &:after{border-bottom-color: #e7a72c}
                }
                .info-box{
                    background-color: var(--bg-theme-color1);
                    .designation{color: #fff;}
                }
            }
        }
    }
    .content-box{
        position: relative;
        background-color: #fff;
        padding: 35px 40px 40px;
        box-shadow: 0 0 15px rgba(0,0,0,.04);
        background-image: url(../images/icons/shape3.png);
        background-position: right top;
        background-repeat: no-repeat;
        @include media-breakpoint-down(sm){
            padding: 40px 25px 45px;
            background-image: none;
        }
        &:before{
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to right, var(--theme-color2), var(--theme-color1));
            height: 6px;
            content: "";
        }
        &:after{
            position: absolute;
            left: 40px;
            top: 100%;
            clip-path: polygon(31% 100%, 0 0, 100% 0);
            background-color: #28251f;
            height: 23px;
            width: 54px;
            content: "";
        }
        .text {
            position: relative;
            font-size: 16px;
            line-height: 30px;
        }
    }
    .info-box{
        position: relative;
        margin-left: 10px;
        min-height: 96px;
        margin-top: 30px;
        padding-left: 115px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @include media-breakpoint-down(sm){
            margin-left: 0px;
        }
        .thumb{
            position: absolute;
            left: 0px;
            top: 0px;
            width: 96px;
            height: 96px;
            border: 5px solid #fff;
            overflow: hidden;
            border-radius: 50%;
            box-shadow: 0 5px 10px rgba(0, 0, 0, .10);
            img{
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 50%;
            }
        }
        .rating{
            position: relative;
            font-size: 12px;
            z-index: 2;
            color: var(--theme-color1);
        }
        .name{
            margin-bottom: 0;
        }
        .designation {
            display: block;
            margin-bottom: 0;
        }
    }
}

/*** 

====================================================================
	Testimonial Section Two
====================================================================

***/

.testimonial-section-two{
    position: relative;
    padding: 120px 0 120px;

    .title-column{
        position: relative;
        margin-bottom: 50px;
        .sec-title{
            margin-bottom: 0px;
            .sub-title,
            .text{color: var(--theme-color2);}
            h2{
                font-size: 40px;
                &:before{
                    background-color: var(--theme-color-light);
                }
            }
        }
    }
    
    .owl-nav{
        position: absolute;
        left: -300px;
        bottom: 15px;
        @include media-breakpoint-down(xl){
            display: none;
        }
    }
}

.testimonial-block-two{
    position: relative;
    overflow: hidden;
    .inner-box{
        position: relative;
        padding-top: 35px;
        &:hover{
            .content-box{
                .thumb{
                    transform: scale(-1) rotate(180deg);
                }
            }
            .info-box{
                .name{
                    color: var(--theme-color1);
                }
            }
        }
    }
    .content-box{
        position: relative;
        padding: 55px 40px 35px;
        background-color: var(--theme-color2);
        @include media-breakpoint-down(sm){
            padding: 55px 25px 20px;
        }
        .thumb{
            position: absolute;
            left: 40px;
            top: -34px;
            width: 68px;
            height: 68px;
            border: 2px solid var(--theme-color1);
            padding: 5px;
            background-color: var(--theme-color2);
            overflow: hidden;
            border-radius: 50%;
            transition: all 300ms ease;
            @include media-breakpoint-down(sm){
                left: 25px;
            }
            img{
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 50%;
            }
        }
        .rating{
            position: relative;
            font-size: 12px;
            line-height: 15px;
            letter-spacing: -.1em;
            z-index: 2;
            color: var(--theme-color1);
            margin-bottom: 10px;
        }
        .text {
            position: relative;
            line-height: 28px;
            margin-bottom: 15px;
        }
    }

    .info-box{
        position: relative;        
        .name{
            margin-bottom: 0;
            line-height: 1.2em;
            color: var(--theme-color-light);
            transition: all 300ms ease;
        }
        .designation {
            display: block;
            font-size: 11px;
            line-height: 1.8em;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: .1em;
            margin-top: 2px;
        }
    }
}
