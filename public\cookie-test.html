<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم Cookie Consent</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/cookie-consent.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Demo Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">
                اختبار تصميم Cookie Consent المحسن
            </h1>
            
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">المميزات الجديدة:</h2>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        تصميم عصري وجذاب مع تدرجات لونية
                    </li>
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        أنيميشن متقدم للظهور والاختفاء
                    </li>
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        دعم كامل للغة العربية (RTL)
                    </li>
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        تصميم متجاوب لجميع الأجهزة
                    </li>
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        تأثيرات بصرية تفاعلية
                    </li>
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        دعم الوضع المظلم والتباين العالي
                    </li>
                </ul>
            </div>
            
            <div class="text-center">
                <button id="showCookieBanner" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                    عرض Cookie Banner
                </button>
                <button id="resetCookies" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 ml-4">
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>

    <!-- Cookie Consent Banner -->
    <div id="cookieConsentBanner" class="fixed bottom-0 left-0 right-0 z-50 transform translate-y-full transition-all duration-500 ease-in-out opacity-0">
        <!-- Background Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-20 backdrop-blur-sm"></div>
        
        <!-- Banner Content -->
        <div class="cookie-banner-content relative shadow-2xl border-t-4 border-purple-500 mx-4 mb-4 rounded-xl overflow-hidden">
            <!-- Close Button -->
            <button id="cookieClose" class="cookie-close-btn absolute top-4 left-4 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 group">
                <svg class="w-4 h-4 text-gray-500 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            
            <div class="p-6 pt-12">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <!-- Content Section -->
                    <div class="flex-1">
                        <div class="flex items-start gap-4">
                            <!-- Cookie Icon with Animation -->
                            <div class="flex-shrink-0">
                                <div class="cookie-icon-container w-14 h-14 rounded-full flex items-center justify-center animate-pulse">
                                    <div class="w-8 h-8 text-2xl">🍪</div>
                                </div>
                            </div>
                            
                            <!-- Text Content -->
                            <div class="flex-1">
                                <h3 class="cookie-title text-xl font-bold mb-3 flex items-center gap-2">
                                    <span>ملفات تعريف الارتباط</span>
                                    <span class="inline-block w-2 h-2 bg-purple-500 rounded-full animate-ping"></span>
                                </h3>
                                <p class="text-gray-600 text-base leading-relaxed mb-3">
                                    نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا. بالمتابعة، فإنك توافق على استخدام ملفات تعريف الارتباط.
                                </p>
                                <a href="#" class="cookie-learn-more inline-flex items-center gap-1 text-purple-600 hover:text-purple-700 font-medium text-sm transition-colors duration-200 group">
                                    <span>تعرف على المزيد</span>
                                    <svg class="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0 lg:min-w-[280px]">
                        <button id="cookieDecline" class="cookie-decline-btn flex-1 px-6 py-3 text-gray-600 hover:text-gray-800 font-medium text-sm transition-all duration-200 border-2 border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                            <span class="flex items-center justify-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                رفض
                            </span>
                        </button>
                        <button id="cookieAccept" class="cookie-accept-btn flex-1 px-6 py-3 text-white font-medium text-sm rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                            <span class="flex items-center justify-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                موافق
                            </span>
                        </button>
                    </div>
                </div>
                
                <!-- Powered by section -->
                <div class="cookie-powered-by">
                    <p class="text-xs text-gray-400 text-center flex items-center justify-center gap-1">
                        <span>Powered by</span>
                        <span class="font-semibold text-purple-500 hover:text-purple-600 transition-colors duration-200">WebsitePolicies</span>
                        <svg class="w-3 h-3 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const banner = document.getElementById('cookieConsentBanner');
            const acceptBtn = document.getElementById('cookieAccept');
            const declineBtn = document.getElementById('cookieDecline');
            const closeBtn = document.getElementById('cookieClose');
            const showBtn = document.getElementById('showCookieBanner');
            const resetBtn = document.getElementById('resetCookies');
            
            // Show banner function
            function showBanner() {
                banner.classList.remove('translate-y-full', 'opacity-0');
                banner.classList.add('cookie-banner-enter');
                
                // Add bounce animation to cookie icon
                const cookieIcon = banner.querySelector('.cookie-icon-container');
                if (cookieIcon) {
                    cookieIcon.classList.remove('animate-pulse');
                    cookieIcon.classList.add('cookie-icon-bounce');
                }
                
                // Add glow effect to accept button
                const acceptButton = banner.querySelector('.cookie-accept-btn');
                if (acceptButton) {
                    acceptButton.classList.add('cookie-glow');
                }
            }
            
            // Hide banner function
            function hideBanner(action = 'dismissed') {
                banner.style.opacity = '0';
                banner.classList.add('translate-y-full');
                
                setTimeout(() => {
                    banner.classList.add('opacity-0');
                    banner.classList.remove('cookie-banner-enter');
                    
                    // Reset animations
                    const cookieIcon = banner.querySelector('.cookie-icon-container');
                    if (cookieIcon) {
                        cookieIcon.classList.remove('cookie-icon-bounce');
                        cookieIcon.classList.add('animate-pulse');
                    }
                    
                    const acceptButton = banner.querySelector('.cookie-accept-btn');
                    if (acceptButton) {
                        acceptButton.classList.remove('cookie-glow');
                    }
                    
                    // Show toast
                    if (action === 'accepted') {
                        showToast('تم قبول ملفات تعريف الارتباط', 'success');
                    } else if (action === 'declined') {
                        showToast('تم رفض ملفات تعريف الارتباط', 'info');
                    }
                }, 500);
            }
            
            // Toast notification function
            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white text-sm font-medium transform translate-x-full transition-transform duration-300 ${
                    type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                }`;
                toast.textContent = message;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.classList.remove('translate-x-full');
                }, 100);
                
                setTimeout(() => {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 3000);
            }
            
            // Event listeners
            showBtn.addEventListener('click', showBanner);
            resetBtn.addEventListener('click', () => {
                banner.style.opacity = '1';
                banner.classList.remove('translate-y-full', 'opacity-0');
            });
            
            acceptBtn.addEventListener('click', () => {
                acceptBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    acceptBtn.style.transform = 'scale(1)';
                }, 100);
                hideBanner('accepted');
            });
            
            declineBtn.addEventListener('click', () => {
                declineBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    declineBtn.style.transform = 'scale(1)';
                }, 100);
                hideBanner('declined');
            });
            
            closeBtn.addEventListener('click', () => {
                hideBanner('dismissed');
            });
        });
    </script>
</body>
</html>
