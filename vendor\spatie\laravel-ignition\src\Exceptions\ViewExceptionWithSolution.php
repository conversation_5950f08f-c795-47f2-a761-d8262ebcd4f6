<?php

namespace Spatie\LaravelIgnition\Exceptions;

use <PERSON><PERSON>\Ignition\Contracts\ProvidesSolution;
use <PERSON><PERSON>\Ignition\Contracts\Solution;

class ViewExceptionWithSolution extends ViewException implements ProvidesSolution
{
    protected Solution $solution;

    public function setSolution(Solution $solution): void
    {
        $this->solution = $solution;
    }

    public function getSolution(): Solution
    {
        return $this->solution;
    }
}
