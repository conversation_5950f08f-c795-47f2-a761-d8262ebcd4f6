# Security Policy

This library is intended to be used in development environments only. For instance, it is used by the testing framework PHPUnit. There is no reason why this library should be installed on a webserver.

**If you upload this library to a webserver then your deployment process is broken. On a more general note, if your `vendor` directory is publicly accessible on your webserver then your deployment process is also broken.**

## Security Contact Information

After the above, if you still would like to report a security vulnerability, please email `<EMAIL>`.
