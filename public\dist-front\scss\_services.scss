/*** 

====================================================================
    Services Section
====================================================================

***/

.services-section{
    position: relative;
    padding:120px 0 80px;
    .bg-pattern-1{
        max-height: 435px;
        @include media-breakpoint-down(md){
            min-height: 700px;
        }
    }
    .bg-shape{
        position: absolute;
        width: 377px;
        height: 614px;
        right: 0;
        top: 140px;
        background-image: url(../images/icons/shape1.png);
    }
    .sec-title{
        margin-bottom: 45px;
        .text{
            margin-top: 40px;
        }
    }

}

.service-block{
    margin-bottom: 30px;
    .inner-box {
        position: relative;
        background:#fff;
        transition: all 300ms ease;
        &:before{
            @include overlay;
            max-height: 4px;
            z-index: 2;
            background-color: var(--bg-theme-color1);
            transition: all 300ms ease;
            content: "";
        }
        &:hover {
            .image-box .image:before,
            .content-box{
                opacity: 0;
                visibility: hidden;
            }
            .hover-content{
                opacity: 1;
                visibility: visible;
                transform:scale(1);
                .icon{
                    opacity: 1;
                    transform: translate(0);
                    transition-delay: 300ms;
                }
                .title{
                    opacity: 1;
                    transform: translate(0);
                    transition-delay: 400ms;
                }
                .text{
                    opacity: .7;
                    transform: translate(0);
                    transition-delay: 500ms;
                }
            }
        }
        .image-box{
            position: relative;
            min-height: 304px;
            .image{
                &:before{
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    height: 40px;
                    width: 100%;
                    background-color: #fff;
                    content: "";
                    transition: all 200ms ease;
                }
                position: relative;
                margin-bottom: 0;
                background-color: var(--theme-color2);
                img{
                    width: 100%;
                    min-height: 350px;
                    object-fit: cover;
                    transition: all 300ms ease
                }
            }
        }
        .content-box{
            position: absolute;
            left: 0;
            bottom: 0;
            right: 20px;
            background-color: #fff;
            box-shadow: 0 10px 60px rgba(0,0,0,0.10);
            padding: 16px 30px;
            transition: all 200ms ease;
            .icon{
                position: absolute;
                right: 0;
                bottom: 100%;
                height: 64px;
                width: 64px;
                @include flex-center;
                background-color: var(--theme-color1);
                color: var(--theme-color2);
                font-size: 32px;
                z-index: 2;
                transition: all 300ms ease;
            }
            .title {
                color: var(--theme-color2);
                margin-bottom: 0;
            }
        }
        .hover-content{
            @include overlay;
            padding: 35px 30px 20px;
            z-index: 2;
            opacity: 0;
            visibility: hidden;
            transition: all 300ms ease;
            transform:scaleY(0);
            transform-origin: bottom;
            &::before{
                @include overlay;
                background-color: #0e0f11;
                opacity: .9;
                content: "";
                border-bottom: 4px solid var(--theme-color1);
            }
            .icon{
                position: relative;
                @include flex-center;
                height: 64px;
                width: 64px;
                color: var(--theme-color2);
                font-size: 32px;
                background-color: var(--theme-color1);
                margin-bottom: 25px;
                opacity: 0;
                transform: translateY(20px);
                transition: all 300ms ease;
            }
            .title {
                color: var(--theme-color-light);
                margin-bottom: 18px;
                transform: translateY(20px);
                opacity: 0;
                transition: all 300ms ease;
            }
            .text{
                color: var(--theme-color-light);
                transform: translateY(20px);
                opacity: 0;
                transition: all 300ms ease;
            }
        }
    }
}


/*** 

====================================================================
    Services Section Two
====================================================================

***/

.services-section-two{
    position: relative;
    padding:120px 0 80px;
}

.service-block-two{
    margin-bottom: 30px;
    .inner-box {
        position: relative;
        transition: all 300ms ease;
        .image-box{
            position: relative;
            .image{
                &:before{
                    @include overlay;
                    background:linear-gradient(-45deg, transparent, var(--theme-color2));
                    opacity: .9;
                    content: "";
                    transition: all 200ms ease;
                }
                position: relative;
                margin-bottom: 0;
                background-color: var(--theme-color2);
                img{
                    width: 100%;
                    min-height: 304px;
                    height: 100%;
                    object-fit: cover;
                    transition: all 300ms ease
                }
            }
        }
        .title-box{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 25px 30px;
            .title {
                color: var(--theme-color-light);
                margin-bottom: 18px;
                transition: all 300ms ease;
            }
        }
        .content-box{
            position: absolute;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background-color: #0e0f11;
            padding: 20px 30px;
            padding-left: 125px;
            transition: all 200ms ease;
            @include media-breakpoint-down(xs){
                padding-left: 100px;
            }
            .icon{
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 95px;
                @include flex-center;
                background-color: var(--theme-color1);
                color: var(--theme-color2);
                font-size: 62px;
                z-index: 2;
                transition: all 300ms ease;
                @include media-breakpoint-down(xs){
                    width: 70px;
                    font-size: 42px;
                }
            }
            .text{
                font-size: 14px;
                line-height: 26px;
                color: #75767a;
            }
        }
    }
}
