@extends('front.layouts.master')

@section('seo_title', $global_other_page_items->page_pricing_seo_title)
@section('seo_meta_description', $global_other_page_items->page_pricing_seo_meta_description)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-purple-600 to-indigo-600 py-20 overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $global_other_page_items->page_pricing_title }}</h1>
            <p class="text-xl text-purple-100 mb-8">اختر الخطة المناسبة لاحتياجاتك</p>
            <nav class="flex justify-center" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 text-white">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-purple-200 transition-colors">
                            {{ __('Home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-purple-200">{{ $global_other_page_items->page_pricing_title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Pricing Plans Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">خطط التسعير</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                نقدم لك مجموعة متنوعة من الخطط المصممة لتناسب احتياجاتك وميزانيتك
            </p>
        </div>

        <!-- Pricing Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($pricing_plans as $index => $item)
            <div class="relative group">
                <!-- Popular Badge for middle plan -->
                @if($index == 1 && count($pricing_plans) == 3)
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <span class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                        الأكثر شعبية
                    </span>
                </div>
                @endif

                <!-- Pricing Card -->
                <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden {{ $index == 1 && count($pricing_plans) == 3 ? 'ring-2 ring-purple-500 scale-105' : '' }}">

                    <!-- Card Header with Image -->
                    @if($item->photo)
                    <div class="relative h-48 overflow-hidden">
                        <img src="{{ asset('uploads/'.$item->photo) }}"
                             alt="{{ $item->name }}"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                    @endif

                    <!-- Card Content -->
                    <div class="p-8">
                        <!-- Plan Name -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 text-center">{{ $item->name }}</h3>

                        <!-- Price -->
                        <div class="text-center mb-8">
                            <div class="flex items-center justify-center">
                                <span class="text-lg text-gray-500">{{ $global_setting->currency_symbol }}</span>
                                <span class="text-5xl font-bold text-gray-900 mx-1">{{ $item->price }}</span>
                                <span class="text-lg text-gray-500">/ {{ $item->period }}</span>
                            </div>
                        </div>

                        <!-- Features List -->
                        <ul class="space-y-4 mb-8">
                            @php
                                $option_list = DB::table('pricing_plan_options')->where('pricing_plan_id', $item->id)->get();
                            @endphp
                            @foreach($option_list as $option)
                            <li class="flex items-center text-right">
                                <svg class="w-5 h-5 text-green-500 ml-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700">{{ $option->name }}</span>
                            </li>
                            @endforeach
                        </ul>

                        <!-- CTA Button -->
                        <div class="text-center">
                            <a href="{{ $item->button_url }}"
                               class="w-full inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-lg text-white
                                      {{ $index == 1 && count($pricing_plans) == 3 ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700' : 'bg-gray-900 hover:bg-gray-800' }}
                                      transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                {{ $item->button_text }}
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">الأسئلة الشائعة</h2>
            <p class="text-lg text-gray-600">إجابات على الأسئلة الأكثر شيوعاً حول خطط التسعير</p>
        </div>

        <div class="space-y-4">
            <!-- FAQ Item 1 -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="relative">
                    <input type="checkbox" id="faq-pricing-1" class="peer absolute top-0 inset-x-0 w-full h-16 opacity-0 z-10 cursor-pointer">
                    <div class="bg-gray-50 hover:bg-gray-100 transition-colors duration-300 h-16 pl-5 flex items-center justify-between px-6">
                        <h3 class="text-lg font-semibold text-gray-900 text-right flex-1">هل يمكنني تغيير خطتي لاحقاً؟</h3>
                        <div class="text-purple-600 transition-transform duration-300 rotate-0 peer-checked:rotate-180 ml-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden transition-all duration-500 max-h-0 peer-checked:max-h-96">
                        <div class="p-6 border-t border-gray-100">
                            <p class="text-gray-700 text-right leading-relaxed">
                                نعم، يمكنك ترقية أو تخفيض خطتك في أي وقت. سيتم تطبيق التغييرات في دورة الفوترة التالية.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 2 -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="relative">
                    <input type="checkbox" id="faq-pricing-2" class="peer absolute top-0 inset-x-0 w-full h-16 opacity-0 z-10 cursor-pointer">
                    <div class="bg-gray-50 hover:bg-gray-100 transition-colors duration-300 h-16 pl-5 flex items-center justify-between px-6">
                        <h3 class="text-lg font-semibold text-gray-900 text-right flex-1">هل هناك فترة تجريبية مجانية؟</h3>
                        <div class="text-purple-600 transition-transform duration-300 rotate-0 peer-checked:rotate-180 ml-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden transition-all duration-500 max-h-0 peer-checked:max-h-96">
                        <div class="p-6 border-t border-gray-100">
                            <p class="text-gray-700 text-right leading-relaxed">
                                نعم، نقدم فترة تجريبية مجانية لمدة 14 يوماً لجميع الخطط. لا حاجة لبطاقة ائتمان للبدء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 3 -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="relative">
                    <input type="checkbox" id="faq-pricing-3" class="peer absolute top-0 inset-x-0 w-full h-16 opacity-0 z-10 cursor-pointer">
                    <div class="bg-gray-50 hover:bg-gray-100 transition-colors duration-300 h-16 pl-5 flex items-center justify-between px-6">
                        <h3 class="text-lg font-semibold text-gray-900 text-right flex-1">ما هي طرق الدفع المتاحة؟</h3>
                        <div class="text-purple-600 transition-transform duration-300 rotate-0 peer-checked:rotate-180 ml-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden transition-all duration-500 max-h-0 peer-checked:max-h-96">
                        <div class="p-6 border-t border-gray-100">
                            <p class="text-gray-700 text-right leading-relaxed">
                                نقبل جميع البطاقات الائتمانية الرئيسية، PayPal، والتحويل البنكي. جميع المدفوعات آمنة ومشفرة.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="bg-gradient-to-r from-purple-600 to-indigo-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">هل تحتاج إلى خطة مخصصة؟</h2>
        <p class="text-xl text-purple-100 mb-8">تواصل معنا لمناقشة احتياجاتك الخاصة والحصول على عرض مخصص</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact') }}"
               class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-purple-600 bg-white hover:bg-purple-50 transition-colors">
                تواصل معنا
            </a>
            @if($global_setting->whatsapp != null)
            <a href="https://wa.me/{{ $global_setting->whatsapp }}"
               target="_blank"
               class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-purple-600 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                </svg>
                واتساب
            </a>
            @endif
        </div>
    </div>
</section>
@endsection