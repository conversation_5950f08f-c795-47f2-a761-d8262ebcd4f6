<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Cookie Banner</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        @keyframes cookieSlideIn {
            from {
                transform: translateX(100%) scale(0.8);
                opacity: 0;
            }
            to {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .cookie-banner-enter {
            animation: cookieSlideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .cookie-banner-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #9333ea, #ec4899, #8b5cf6);
            background-size: 200% 100%;
            animation: gradientShift 2s linear infinite;
        }

        .cookie-icon-container:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
        }

        .cookie-accept-btn:hover {
            box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
            transform: translateY(-2px) scale(1.05);
        }

        .cookie-decline-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-50 to-pink-50 min-h-screen">
    <!-- Demo Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-8">
                🍪 اختبار Cookie Banner المحسن
            </h1>
            
            <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
                <p class="text-gray-600 text-lg mb-6">
                    انقر على الزر أدناه لعرض Cookie Banner الجديد
                </p>
                
                <button id="showCookieBanner" class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    🍪 عرض Cookie Banner
                </button>
                
                <button id="resetCookies" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 ml-4">
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>

    <!-- Cookie Consent Banner -->
    <div id="cookieConsentBanner" class="fixed bottom-6 right-6 z-50 max-w-sm transform translate-x-full transition-all duration-700 ease-out opacity-0">
        <!-- Floating Card -->
        <div class="cookie-banner-content relative bg-white rounded-2xl shadow-2xl border border-purple-200 overflow-hidden backdrop-blur-lg">
            <!-- Gradient Border Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 rounded-2xl opacity-75 animate-pulse"></div>
            <div class="absolute inset-[2px] bg-white rounded-2xl"></div>
            
            <!-- Content -->
            <div class="relative p-4">
                <!-- Header with Cookie Icon -->
                <div class="flex items-center gap-3 mb-3">
                    <div class="cookie-icon-container w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg transition-all duration-300">
                        <span class="text-lg animate-bounce">🍪</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="cookie-title text-sm font-bold text-gray-800 flex items-center gap-1">
                            <span>ملفات تعريف الارتباط</span>
                            <div class="w-1.5 h-1.5 bg-purple-500 rounded-full animate-ping"></div>
                        </h3>
                    </div>
                    <!-- Close Button -->
                    <button id="cookieClose" class="cookie-close-btn w-6 h-6 bg-gray-100 hover:bg-red-100 rounded-full flex items-center justify-center transition-all duration-200 group">
                        <svg class="w-3 h-3 text-gray-400 group-hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Message -->
                <p class="text-xs text-gray-600 leading-relaxed mb-4">
                    نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا. Learn more
                    <a href="#" class="cookie-learn-more text-purple-600 hover:text-purple-700 font-medium underline decoration-dotted underline-offset-2">
                        تعرف على المزيد
                    </a>
                </p>
                
                <!-- Action Buttons -->
                <div class="flex gap-2">
                    <button id="cookieDecline" class="cookie-decline-btn flex-1 px-3 py-2 text-xs font-medium text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all duration-200 border border-gray-200">
                        رفض
                    </button>
                    <button id="cookieAccept" class="cookie-accept-btn flex-1 px-3 py-2 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                        موافق
                    </button>
                </div>
                
                <!-- Powered by (smaller) -->
                <div class="mt-3 pt-2 border-t border-gray-100">
                    <p class="text-[10px] text-gray-400 text-center">
                        Powered by <span class="font-medium text-purple-400">WebsitePolicies</span>
                    </p>
                </div>
            </div>
            
            <!-- Floating Particles Effect -->
            <div class="absolute top-2 right-2 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-60"></div>
            <div class="absolute bottom-3 left-3 w-0.5 h-0.5 bg-pink-400 rounded-full animate-pulse opacity-40"></div>
            <div class="absolute top-1/2 left-1 w-0.5 h-0.5 bg-purple-300 rounded-full animate-bounce opacity-50"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const banner = document.getElementById('cookieConsentBanner');
            const acceptBtn = document.getElementById('cookieAccept');
            const declineBtn = document.getElementById('cookieDecline');
            const closeBtn = document.getElementById('cookieClose');
            const showBtn = document.getElementById('showCookieBanner');
            const resetBtn = document.getElementById('resetCookies');
            
            function showBanner() {
                banner.classList.remove('translate-x-full', 'opacity-0');
                banner.classList.add('cookie-banner-enter');
                
                setTimeout(() => {
                    banner.style.animation = 'shake 0.5s ease-in-out';
                }, 800);
            }
            
            function hideBanner(action = 'dismissed') {
                banner.style.transform = 'translateX(100%) scale(0.8)';
                banner.style.opacity = '0';
                
                setTimeout(() => {
                    banner.classList.add('translate-x-full', 'opacity-0');
                    banner.classList.remove('cookie-banner-enter');
                    banner.style.transform = '';
                    banner.style.animation = '';
                    
                    if (action === 'accepted') {
                        showMiniToast('✅ تم القبول', 'success');
                    } else if (action === 'declined') {
                        showMiniToast('❌ تم الرفض', 'error');
                    }
                }, 400);
            }
            
            function showMiniToast(message, type = 'info') {
                const toast = document.createElement('div');
                const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                
                toast.className = `fixed bottom-20 right-6 z-50 px-4 py-2 rounded-full shadow-lg text-white text-xs font-medium transform translate-x-full transition-all duration-300 ${bgColor}`;
                toast.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span>${message}</span>
                        <div class="w-1 h-1 bg-white rounded-full animate-ping"></div>
                    </div>
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.classList.remove('translate-x-full');
                    toast.style.transform = 'translateX(0) scale(1.1)';
                    setTimeout(() => {
                        toast.style.transform = 'translateX(0) scale(1)';
                    }, 150);
                }, 100);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%) scale(0.8)';
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 2000);
            }
            
            // Event listeners
            showBtn.addEventListener('click', showBanner);
            resetBtn.addEventListener('click', () => {
                banner.style.opacity = '1';
                banner.classList.remove('translate-x-full', 'opacity-0');
                banner.style.transform = '';
                banner.style.animation = '';
            });
            
            acceptBtn.addEventListener('click', () => {
                acceptBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    acceptBtn.style.transform = 'scale(1)';
                }, 100);
                hideBanner('accepted');
            });
            
            declineBtn.addEventListener('click', () => {
                declineBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    declineBtn.style.transform = 'scale(1)';
                }, 100);
                hideBanner('declined');
            });
            
            closeBtn.addEventListener('click', () => {
                hideBanner('dismissed');
            });
            
            // Auto show for demo
            setTimeout(showBanner, 1000);
        });
    </script>
</body>
</html>
