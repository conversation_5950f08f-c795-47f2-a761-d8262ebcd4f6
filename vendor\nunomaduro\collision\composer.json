{"name": "nunomaduro/collision", "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["console", "command-line", "php", "cli", "error", "handling", "laravel-zero", "laravel", "artisan", "symfony"], "license": "MIT", "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1.0", "filp/whoops": "^2.15.3", "nunomaduro/termwind": "^1.15.1", "symfony/console": "^6.3.4"}, "conflict": {"laravel/framework": ">=11.0.0"}, "require-dev": {"brianium/paratest": "^7.3.0", "laravel/framework": "^10.28.0", "laravel/pint": "^1.13.3", "laravel/sail": "^1.25.0", "laravel/sanctum": "^3.3.1", "laravel/tinker": "^2.8.2", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.13.0", "pestphp/pest": "^2.23.2", "phpunit/phpunit": "^10.4.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.3.1"}, "autoload-dev": {"psr-4": {"Tests\\Printer\\": "tests/Printer", "Tests\\Unit\\": "tests/Unit", "Tests\\FakeProgram\\": "tests/FakeProgram", "Tests\\": "tests/LaravelApp/tests", "App\\": "tests/LaravelApp/app/"}}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}, "files": ["./src/Adapters/Phpunit/Autoload.php"]}, "config": {"preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit:phpunit": ["@putenv XDEBUG_MODE=coverage", "phpunit --colors=always"], "test:unit:pest": ["@putenv XDEBUG_MODE=coverage", "pest --colors=always -v"], "test": ["@test:lint", "@test:types", "@test:unit:phpunit", "@test:unit:pest"]}}