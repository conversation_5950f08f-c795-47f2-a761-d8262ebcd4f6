<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework\MockObject\Rule;

use function is_string;
use PHPUnit\Framework\Constraint\Constraint;
use PHPUnit\Framework\InvalidArgumentException;
use PHPUnit\Framework\MockObject\Invocation as BaseInvocation;
use PHPUnit\Framework\MockObject\MethodNameConstraint;

/**
 * @internal This class is not covered by the backward compatibility promise for PHPUnit
 */
final class MethodName
{
    private readonly Constraint $constraint;

    /**
     * @throws InvalidArgumentException
     */
    public function __construct(Constraint|string $constraint)
    {
        if (is_string($constraint)) {
            $constraint = new MethodNameConstraint($constraint);
        }

        $this->constraint = $constraint;
    }

    public function toString(): string
    {
        return 'method name ' . $this->constraint->toString();
    }

    /**
     * @throws \PHPUnit\Framework\ExpectationFailedException
     */
    public function matches(BaseInvocation $invocation): bool
    {
        return $this->matchesName($invocation->methodName());
    }

    /**
     * @throws \PHPUnit\Framework\ExpectationFailedException
     */
    public function matchesName(string $methodName): bool
    {
        return (bool) $this->constraint->evaluate($methodName, '', true);
    }
}
