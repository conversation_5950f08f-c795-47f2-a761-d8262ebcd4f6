{"name": "illuminate/notifications", "description": "The Illuminate Notifications package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/broadcasting": "^10.0", "illuminate/bus": "^10.0", "illuminate/collections": "^10.0", "illuminate/container": "^10.0", "illuminate/contracts": "^10.0", "illuminate/filesystem": "^10.0", "illuminate/mail": "^10.0", "illuminate/queue": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Notifications\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database transport (^10.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}