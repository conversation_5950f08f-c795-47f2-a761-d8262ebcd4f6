/* <PERSON><PERSON> Styles */

/* Animations */
@keyframes cookieSlideIn {
    from {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes borderGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(147, 51, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(147, 51, 234, 0.8), 0 0 30px rgba(236, 72, 153, 0.4);
    }
}

/* Banner Classes */
.cookie-banner-enter {
    animation: cookieSlideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.cookie-float {
    animation: float 3s ease-in-out infinite;
}

.cookie-sparkle {
    animation: sparkle 2s infinite;
}

.cookie-border-glow {
    animation: borderGlow 2s infinite;
}

.cookie-gradient-shift {
    background: linear-gradient(-45deg, #9333ea, #ec4899, #8b5cf6, #a855f7);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

/* Cookie Banner Container */
#cookieConsentBanner {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    backdrop-filter: blur(10px);
    will-change: transform;
}

/* Compact Banner Content */
.cookie-banner-content {
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #ffffff 100%);
    border: 1px solid rgba(147, 51, 234, 0.2);
    position: relative;
    overflow: hidden;
}

.cookie-banner-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #9333ea, #ec4899, #8b5cf6);
    background-size: 200% 100%;
    animation: gradientShift 2s linear infinite;
}

/* Compact Cookie Icon Container */
.cookie-icon-container {
    background: linear-gradient(135deg, #9333ea 0%, #ec4899 100%);
    position: relative;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
    transition: all 0.3s ease;
}

.cookie-icon-container:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
}

.cookie-icon-container::before {
    content: '';
    position: absolute;
    inset: -1px;
    background: linear-gradient(45deg, #9333ea, #ec4899, #8b5cf6, #a855f7);
    border-radius: inherit;
    z-index: -1;
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

/* Compact Button Styles */
.cookie-accept-btn {
    background: linear-gradient(135deg, #9333ea 0%, #ec4899 100%);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
    transition: all 0.3s ease;
}

.cookie-accept-btn:hover {
    box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
    transform: translateY(-2px) scale(1.05);
}

.cookie-accept-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s ease;
}

.cookie-accept-btn:hover::before {
    left: 100%;
}

.cookie-decline-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.cookie-decline-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cookie-decline-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    transition: left 0.4s ease;
}

.cookie-decline-btn:hover::before {
    left: 100%;
}

/* Close Button */
.cookie-close-btn {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.cookie-close-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* Text Styles */
.cookie-title {
    background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Learn More Link */
.cookie-learn-more {
    position: relative;
}

.cookie-learn-more::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #9333ea, #7c3aed);
    transition: width 0.3s ease;
}

.cookie-learn-more:hover::after {
    width: 100%;
}

/* Powered By Section */
.cookie-powered-by {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin-top: 1rem;
}

/* RTL Support */
[dir="rtl"] #cookieConsentBanner {
    direction: rtl;
}

[dir="rtl"] .cookie-learn-more::after {
    right: 0;
    left: auto;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
    #cookieConsentBanner {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .cookie-banner-content {
        border-radius: 1rem;
    }

    .cookie-icon-container {
        width: 2.5rem;
        height: 2.5rem;
    }

    .cookie-title {
        font-size: 0.875rem;
    }

    .cookie-accept-btn,
    .cookie-decline-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Stack buttons vertically on very small screens */
    @media (max-width: 380px) {
        .cookie-banner-content .flex {
            flex-direction: column;
        }

        .cookie-accept-btn,
        .cookie-decline-btn {
            width: 100%;
        }
    }
}

/* Tablet Optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
    #cookieConsentBanner {
        margin: 0 1rem 1rem 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .cookie-banner-content {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #f9fafb;
        border-color: rgba(147, 51, 234, 0.3);
    }
    
    .cookie-close-btn {
        background: rgba(31, 41, 55, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
        color: #f9fafb;
    }
    
    .cookie-close-btn:hover {
        background: rgba(31, 41, 55, 1);
    }
    
    .cookie-powered-by {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .cookie-banner-content {
        border: 2px solid #000;
    }
    
    .cookie-accept-btn {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }
    
    .cookie-decline-btn {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .cookie-banner-enter,
    .cookie-icon-bounce,
    .cookie-button-pulse,
    .cookie-glow {
        animation: none;
    }
    
    #cookieConsentBanner {
        transition: none;
    }
}

/* Print Styles */
@media print {
    #cookieConsentBanner {
        display: none !important;
    }
}
